x-bun-base: &bun-base
  image: oven/bun:alpine
  working_dir: /app
  shm_size: "512mb"
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "1"

x-node-base: &node-base
  build:
    context: ./docker
    dockerfile: nodejs.Dockerfile
  image: nodejs-dev:v1
  volumes:
    - node_modules/:/app
  working_dir: /app
  shm_size: "256mb"
  restart: always

x-golang-base: &golang-base
  build:
    context: ./docker
    dockerfile: golang.Dockerfile
  image: golang-dev:v1
  volumes:
    - ../digisac-go/:/app
    - go-cache:/app/.cache/go-build
  working_dir: /app
  shm_size: "256mb"
  restart: always

services:
  dependencies:
    <<: *bun-base
    container_name: dependencies
    volumes:
      - ./:/app
    command: |
      /bin/sh -c 'cd packages/back && bun install --no-cache'
    # command: |
    # /bin/sh -c '
    # yarn &&
    # yarn lerna exec --parallel "rm -rf package-lock.json" &&
    # yarn lerna exec --parallel "yarn --ignore-engines --cache-folder ./.ycache"
    # '
    restart: "no"

  # app-scripts:
  #   <<: *node-base
  #   container_name: app-scripts
  #   command: tail -f /dev/null
  #   volumes:
  #     - ./packages/back/:/app/

  app-front:
    <<: *bun-base
    container_name: app-front
    command: |
      /bin/sh -c 'bun install --no-cache && bun run develop'
    #    command: tail -f /dev/null
    env_file:
      - packages/front/.env
    ports:
      - 1337:1337
      - 7331:7331
    volumes:
      - ./packages/front/:/app/

  app-api:
    build:
      context: ./packages/back
      dockerfile: Dockerfile
    container_name: app-api
    command: |
      /bin/sh -c 'bun dist/api/index.js'
    expose:
      - 8080
    # ports:
      # - 8081:8080
    volumes:
      - ./packages/back/:/app/


  # app-api:
  #   <<: *bun-base
  #   command: |
  #     /bin/sh -c 'bun run-live src/microServices/api'
  #   deploy:
  #     replicas: 1
  #   expose:
  #     - 8080
  #   volumes:
  #     - ./packages/back/:/app/
  #   depends_on:
  #     dependencies:
  #       condition: service_completed_successfully
  #     postgresql:
  #       condition: service_started
  #     redis:
  #       condition: service_started
  #     mongodb:
  #       condition: service_started

  app-cron:
    <<: *bun-base
    container_name: app-cron
    command: bun run-live src/microServices/cron
    ports:
      - 8001:8000
    volumes:
      - ./packages/back/:/app/
    depends_on:
      - app-api

  app-serverpod1:
    <<: *bun-base
    container_name: app-serverpod1
    command: yarn run-static src/microServices/serverPod
    environment:
      INSTANCE_ID: serverpod-local1
      SERVER_POD_URL: http://app-serverpod1:5001
    ports:
      - 5001:5001
    volumes:
      - ./packages/back/:/app/
      - ~/browserless/wp-sessions/:/usr/src/app/wp-sessions
    depends_on:
      - app-api

  app-socket:
    <<: *bun-base
    container_name: app-socket
    command: bun run-live src/microServices/socket
    ports:
      - 7000:7000
    volumes:
      - ./packages/back/:/app/
    depends_on:
      - app-api

  app-workers:
    <<: *bun-base
    command: bun run-live src/microServices/workers
    deploy:
      replicas: 1
    expose:
      - 8000
    volumes:
      - ./packages/back/:/app/
      - ../whatsapp-scripts-src/:/whatsapp-scripts-src/
      - ../wa-js/:/wa-js/
    depends_on:
      - app-api

  message-broker-producer:
    container_name: message-broker-producer
    image: sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker-producer:1.3.2
    expose:
      - 2000
    ports:
      - "2000:2000"
    environment:
      - RABBITMQ_HOST=lavinmq
    depends_on:
      lavinmq:
        condition: service_healthy

  message-broker-consumer:
    container_name: message-broker-consumer
    image: sa-vinhedo-1.ocir.io/axvaplbwrlcl/message-broker-consumer:1.3.2
    volumes:
      - ./packages/back/src/core/queues-definition.yaml:/app/config.yaml
    environment:
      - RABBITMQ_HOST=lavinmq
    depends_on:
      lavinmq:
        condition: service_healthy

  lavinmq:
    container_name: lavinmq
    image: cloudamqp/lavinmq:latest
    ports:
      - "5672:5672"   # AMQP protocol port
      - "15672:15672" # Management UI port
    volumes:
      - ./lavinmq_data:/var/lib/lavinmq
    environment:
      - LAVINMQ_DEFAULT_USER=guest
      - LAVINMQ_DEFAULT_PASS=guest
    restart: always
    healthcheck:
      test: [ "CMD", "lavinmqctl", "status" ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  postgresql:
    container_name: postgresql
    image: postgres:16.3-alpine
    command: postgres -c 'max_connections=5000'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: mandeumzap
    restart: always
    shm_size: "256mb"
    ports:
      - 5432:5432
    volumes:
      - ~/postgresql/data/:/var/lib/postgresql/data

  redis:
    container_name: redis
    image: redis:6-alpine
    restart: always
    shm_size: "256mb"
    ports:
      - 6379:6379

  browserless:
    container_name: browserless
    image: browserless/chrome:1.61-chrome-stable
    command: /bin/sh -c "(rm /usr/src/app/wp-sessions/*/Singleton* || true) && ./start.sh"
    user: root
    environment:
      MAX_CONCURRENT_SESSIONS: 100
      CONNECTION_TIMEOUT: -1
      DEFAULT_LAUNCH_ARGS: '["--disable-web-security"]'
      DEFAULT_IGNORE_HTTPS_ERRORS: "true"
    volumes:
      - ~/browserless/wp-sessions/:/usr/src/app/wp-sessions
    restart: always
    shm_size: "512mb"
    ports:
      - 3000:3000
    extra_hosts:
      - "crashlogs.whatsapp.net:127.0.0.1"

  mongodb:
    container_name: mongodb
    image: mongo:4-xenial
    restart: unless-stopped
    ports:
      - "27017:27017"

  # desenvolvimento local com digisac-go
  # app-workers-go:
  #   <<: *golang-base
  #   deploy:
  #     replicas: 1
  #   expose:
  #     - 8000
  #   command: sh -c "./install-hooks.sh && cd ./worker && air"
  #   volumes:
  #     - go-cache:/app/.cache/go-build
  #     - ../digisac-go/:/app
  #     - ../digisac-go/commitizen-bin/:/usr/local/bin
  #     - ../digisac-go/.git/:/app/.git
  #     - ../digisac-go/.git-czrc:/app/.git-czrc
  #   depends_on:
  #     - app-api

  app-workers-go:
    deploy:
      replicas: 1
    image: sa-vinhedo-1.ocir.io/axvaplbwrlcl/digisac-go:1.38.0
    command: /app/worker/worker
    env_file:
      - .env.workers-go
    restart: always
    shm_size: '512mb'
    expose:
      - 8000

  nginx:
    image: nginx:latest
    container_name: nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    ports:
      - 8080:8080 # app-api
      - 8000:8000 # app-workers e app-workers-go
    depends_on:
      - app-api
      - app-workers
      - app-workers-go

volumes:
  go-cache: {}