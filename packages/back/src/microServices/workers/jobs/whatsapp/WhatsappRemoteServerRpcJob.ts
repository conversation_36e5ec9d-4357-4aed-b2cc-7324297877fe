/* eslint-disable import/no-cycle */
import { Inject, Service } from 'typedi'
import Job from '../../../../core/services/jobs/interfaces/Job'
import Logger from '../../../../core/services/logs/Logger'
import SyncJobsDispatcher from '../../../../core/services/jobs/sync/SyncJobsDispatcher'
import HttpJobsDispatcher from '../../../../core/services/jobs/http/HttpJobsDispatcher'
import RedisLock from '../../../../core/services/redis/RedisLock'
import Config from '../../../../core/services/config/Config'
import WhatsappRemoteRpcServerMethods from './WhatsappRemoteRpcServerMethods'
import configValues from '../../configValues'
import { type Tracer, TracerToken } from '../../../../core/services/tracer/Tracer'

export type Payload = {
  serviceId: string
  data: {
    method: string
    params: any[]
    meta: {
      serverPodUrl: string
    }
  }
}

@Service()
export default class WhatsappRemoteServerRpcJob implements Job {
  static jobName = 'whatsapp-remote-server-rpc'

  @Inject()
  protected syncJobsDispatcher: SyncJobsDispatcher

  @Inject()
  protected jobsDispatcher: HttpJobsDispatcher

  @Inject()
  protected logger: Logger

  @Inject()
  protected locker: RedisLock

  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected serverMethods: WhatsappRemoteRpcServerMethods

  @Inject(TracerToken)
  protected tracer: Tracer

  async handle(payload: Payload): Promise<any> {
    return this.generateApmMeta(() => this.runMethod(payload), payload)
  }

  protected runMethod(payload) {
    // From client to server (api)
    return this.serverMethods.webhook(payload.serviceId, payload.data)
  }

  protected async generateApmMeta(fn: () => Promise<any>, payload: Payload) {
    const { serviceId, data } = payload
    const { method, params } = data

    const span = this.tracer.startSpan(`WhatsApp method "${method}"`)

    const transaction = span.getTransaction()

    span.addLabels({
      serviceId,
    })

    transaction.addLabels({
      serviceId,
      rpcMethod: method,
    })

    const webhookMethod = params?.[1]?.request?.method
    const webhookEvent = params?.[1]?.request?.params?.[0]

    transaction.addLabels({
      rpcWebhookMethod: webhookMethod,
      rpcWebhookEvent: webhookEvent,
    })

    const messagePayload = params?.[1]?.request?.params?.[1]
    if (['message:ack-changed', 'message', 'message:revoked'].includes(webhookEvent) && messagePayload) {
      transaction.addLabels({
        messageId: messagePayload.fullId,
        messageIsFromMe: messagePayload.isFromMe,
        messageContactId: messagePayload.contactId,
      })
    }

    try {
      const res = await fn()

      span.end('success')

      return res
    } catch (e) {
      span.end('failure')

      throw e
    }
  }
}
