/* eslint-disable import/no-cycle */
import { Inject, Service } from 'typedi'
import Job from '../../../../core/services/jobs/interfaces/Job'
import Logger from '../../../../core/services/logs/Logger'
import SyncJobsDispatcher from '../../../../core/services/jobs/sync/SyncJobsDispatcher'
import HttpJobsDispatcher from '../../../../core/services/jobs/http/HttpJobsDispatcher'
import RedisLock from '../../../../core/services/redis/RedisLock'
import Config from '../../../../core/services/config/Config'
import WhatsappRemoteRpcClientMethods from './WhatsappRemoteRpcClientMethods'
import configValues from '../../configValues'
import { type Tracer, TracerToken } from '../../../../core/services/tracer/Tracer'

export type Payload = {
  method: string
  params: [serviceId: string, ...methodParams: any]
}

@Service()
export default class WhatsappRemoteClientRpcJob implements Job {
  static jobName = 'whatsapp-remote-client-rpc'

  @Inject()
  protected syncJobsDispatcher: SyncJobsDispatcher

  @Inject()
  protected jobsDispatcher: HttpJobsDispatcher

  @Inject()
  protected logger: Logger

  @Inject()
  protected locker: RedisLock

  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected clientMethods: WhatsappRemoteRpcClientMethods

  @Inject(TracerToken)
  protected tracer: Tracer

  async handle(payload: Payload): Promise<any> {
    return this.generateApmMeta(() => this.runMethod(payload), payload)
  }

  protected runMethod(payload: Payload) {
    const { method, params } = payload

    // From server to client (podGateway)
    const methods = this.clientMethods

    if (!methods[method]) {
      throw new Error(`Method "${method}" not found.`)
    }

    return methods[method](...params)
  }

  protected async generateApmMeta(fn: () => Promise<any>, payload: Payload) {
    const { method, params } = payload

    const span = this.tracer.startSpan(`WhatsApp method "${method}"`)

    const transaction = span.getTransaction()

    const serviceId = params?.[0]

    span.addLabels({
      serviceId,
    })

    transaction.addLabels({
      serviceId,
      rpcMethod: method,
    })

    const messagePayload = params?.[1]

    if (method === 'send' && messagePayload) {
      transaction.addLabels({
        messageId: messagePayload?.message?.fullOverrideMessageId,
        messageContactId: messagePayload?.contactIdFromService,
      })
    }

    try {
      const res = await fn()

      span.end('success')

      return res
    } catch (e) {
      span.end('failure')

      throw e
    }
  }
}
