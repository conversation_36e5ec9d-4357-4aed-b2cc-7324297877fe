import { Inject, Service } from 'typedi'
import { pick } from 'lodash'
import <PERSON>rowserControll<PERSON> from './BrowserController'
import Config from '../../../core/services/config/Config'
import Logger from '../../../core/services/logs/Logger'
import pooling from '../../../core/utils/pooling/pooling'
import configValues from '../configValues'
import { type Tracer, TracerToken } from '../../../core/services/tracer/Tracer'
import { HttpClient } from '../../../core/services/httpClient/HttpClient'

type Options = {
  apiUrl: string
  serviceId: string
  securityToken: string
  serverPodUrl: string
  defaultUA: string
}

@Service({ transient: true })
export default class ServerPod {
  @Inject()
  protected browserController: BrowserController

  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected logger: Logger

  @Inject(TracerToken)
  protected tracer: Tracer

  @Inject()
  protected httpClient: HttpClient

  protected options: Options

  setup(options: Options) {
    this.options = options
    const { serviceId } = this.options

    this.browserController.setup({
      id: serviceId,
      headfull: this.config.get('headfull'),
      whatsappScriptsPath: this.config.get('whatsappScriptsPath'),
      beta: this.config.get('waBeta'),
      onEvent: (method, params) => this.handleCallToServer(method, params),
    })
  }

  async connect() {
    if (!this.options) throw new Error('connect called before setup.')

    const { serviceId, serverPodUrl, securityToken } = this.options

    const getEnvInfo = () => ({
      name: 'ServerPod',
      version: this.config.get('version'),
      serverPodUrl,
    })

    try {
      await this.browserController.start()

      this.log('Attempting connection start...')

      await this.handleCallToServer('connect', [serviceId, securityToken, getEnvInfo()])
      this.log('Connection started.')
    } catch (e) {
      this.log(`Connect failed, Error: %o`, 'error', [e])
      await this.disconnect()
      throw e
    }
  }

  async stop() {
    await this.browserController.stop()
    return true
  }

  logout(reason?: 'BACKUP_RESTORE_FAILED' | 'LOGOUT_EVENT') {
    return this.browserController.logout(reason)
  }

  async disconnect() {
    await pooling(
      async () =>
        this.stop().catch((err) => {
          this.log(`Failed to stop browser page, will retry. ${err}`)
          throw err
        }),
      { interval: 2000, timeout: 30 * 1000, handleError: true },
    ).catch(() => {
      throw new Error('Timeout trying to stop browser page.')
    })

    return true
  }

  pageIsOpen() {
    return this.browserController && this.browserController.pageIsOpen()
  }

  getPageId() {
    return this.browserController.getPageId()
  }

  socketIsConnected() {
    return true
  }

  call(method: string, params: [string, ...any], metadata) {
    if (['disconnect', 'stop'].includes(method)) {
      return this.disconnect()
    }

    return this.browserController.getMethods()[method](...params)
  }

  protected async handleCallToServer(method: string, params: any[]) {
    const options = this.options

    const url = `${options.apiUrl}/${options.serviceId}?securityToken=${options.securityToken}`

    const transaction = this.tracer.startTransaction(`WhatsApp event "${method}"`, 'request')

    transaction.addLabels({ serviceId: options.serviceId, serverPodUrl: options.serverPodUrl })

    try {
      const result = await this.httpClient
        .post(url, {
          method,
          params,
          meta: pick(options, ['serverPodUrl']),
        })
        .then((res) => res.data)

      transaction.end('success')
      return result
    } catch (e) {
      transaction.end('failure')
      throw e
    }
  }

  protected log(message: string, ...rest) {
    this.logger.log(`[${this.options.serviceId}] ${message}`, ...rest)
  }
}
