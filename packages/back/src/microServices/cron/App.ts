import Schedule from 'node-schedule'
import { Inject, Service } from 'typedi'
import <PERSON>trapA<PERSON> from '../../core/services/app/BootstrapApp'
import Config from '../../core/services/config/Config'
import HttpJobsDispatcher from '../../core/services/jobs/http/HttpJobsDispatcher'
import { JobClass } from '../../core/services/jobs/interfaces/Job'
import Logger from '../../core/services/logs/Logger'
import reportError from '../../core/services/logs/reportError'
import HealthCheckHttpServer from '../../core/services/misc/HealthCheckHttpServer'
import { type Tracer, TracerToken } from '../../core/services/tracer/Tracer'
import AccountExpiredJob from '../workers/jobs/account/AccountExpiredJob'
import AccountWillExpireJob from '../workers/jobs/account/AccountWillExpireJob'
import GracePeriodJob from '../workers/jobs/account/GracePeriodJob'
import PlatformClientNotAccessJob from '../workers/jobs/account/PlatformClientNotAccessJob'
import PlatformTrialNotAccessJob from '../workers/jobs/account/PlatformTrialNotAccesJob'
import PlatformTrialNotFirstAccessJob from '../workers/jobs/account/PlatformTrialNotFirstAccessJob'
import PlatformTrialNotWizardFinish from '../workers/jobs/account/PlatformTrialNotWizardFinish'
import RefreshTicketsCountCacheJob from '../workers/jobs/caching/RefreshTicketsCountCacheJob'
import InitPrepareCampaignsJob from '../workers/jobs/campaign/cron/InitPrepareCampaignsJob'
import InitSendCampaignJob from '../workers/jobs/campaign/cron/InitSendCampaignsJob'
import WebchatContactIdleStagesJob from '../workers/jobs/contact/WebchatContactIdleStagesJob'
import BlockMessageRuleJob from '../workers/jobs/blockMessageRule/BlockMessageRuleJob'
import ConsolidateConsumedCreditsJob from '../workers/jobs/creditMovement/ConsolidateConsumedCreditsJob'
import CreditsRenewalJob from '../workers/jobs/creditMovement/CreditsRenewalJob'
import AiCreditExpirationJob from '../workers/jobs/creditMovement/AiCreditExpirationJob'
import OpportunityNotifications from '../workers/jobs/funnel/OpportunityNotifications'
import DistributionJob from '../workers/jobs/distribution/DistributionJob'
import RefreshEmailOutlookTokenJob from '../workers/jobs/email/crons/RefreshEmailOutlookTokenJob'
import SyncEmailsJob from '../workers/jobs/email/crons/SyncEmailsJob'
import DeleteUnusedFilesJob from '../workers/jobs/file/DeleteUnusedFilesJob'
import MessageScheduleJob from '../workers/jobs/message/MessageScheduleJob'
import AccountNotificationJob from '../workers/jobs/notifications/AccountNotificationJob'
import SyncMessagesJob from '../workers/jobs/reclameAqui/crons/SyncMessagesJob'
import PodScalerParallelJob from '../workers/jobs/serverPodManager/PodScalerParallelJob'
import HsmLimitResetJob from '../workers/jobs/service/HsmLimitResetJob'
import SyncFlowDoneJob from '../workers/jobs/service/SyncFlowDoneJob'
import TicketInactiveJob from '../workers/jobs/ticket/TicketInactiveJob'
import BotInactiveJob from '../workers/jobs/bot/BotInactiveJob'
import MFAUserEmailJob from '../workers/jobs/user/crons/MFAUserEmailJob'
import NotificationExpiredPassword from '../workers/jobs/user/crons/NotificationExpiredPassword'
import RefreshUsersInternalchatTokenJob from '../workers/jobs/user/crons/RefreshUsersInternalchatTokenJob'
import OfflineUserLogoutJob from '../workers/jobs/user/OfflineUserLogoutJob'
import InvalidWebhooksInactivatorJob from '../workers/jobs/webhook/InvalidWebhooksInactivatorJob'
import WhatsappKeepOnlineJob from '../workers/jobs/whatsapp/WhatsappKeepOnlineJob'
import WhatsappPingJob from '../workers/jobs/whatsapp/WhatsappPingJob'
import WhatsappQrCodeTimeoutJob from '../workers/jobs/whatsapp/WhatsappQrCodeTimeoutJob'
import CheckServiceDisconnectionJob from '../workers/jobs/whatsapp/CheckServiceDisconnectionJob'
import RefreshWabaTemplatesJob from '../workers/jobs/whatsappBusiness/crons/RefreshWabaTemplates'
import RenewPositusTokenJob from '../workers/jobs/whatsappBusiness/crons/RenewPositusToken'
import configValues from './configValues'
import WabaHealth from '../workers/jobs/whatsappBusiness/crons/WabaHealth'
import Summary from '../workers/jobs/summary/crons/Summary'
import CopilotSuggestion from '../workers/jobs/knowledgeBase/crons/CopilotSuggestion'
import { CsatFeedbackScoreJob } from '../workers/jobs/csat/crons/CsatFeedbackScore'
import { PersonalAccessTokenExpirationJob } from '../workers/jobs/personalAccessTokens/crons/PersonalAccessTokenExpirationJob'

@Service()
export default class App extends BootstrapApp {
  @Inject()
  protected jobsDispatcher: HttpJobsDispatcher

  @Inject()
  protected healthCheckHttpServer: HealthCheckHttpServer

  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected logger: Logger

  @Inject(TracerToken)
  protected tracer: Tracer

  protected cronJobs: ([string, JobClass] | [string, JobClass, any])[] = [
    // whatsapp related
    ['*/5 * * * *', WhatsappPingJob], // Every 10 minutes
    // ['* * * * *', WhatsappCheckWrongStatus], // Every minute
    ['* * * * *', WhatsappQrCodeTimeoutJob], // Every minute
    ['* * * * *', WhatsappKeepOnlineJob], // Every minute
    ['* * * * *', CheckServiceDisconnectionJob], // Every minute
    ['* * * * *', PodScalerParallelJob, { scalerType: 'creator' }], // Every minute
    ['*/10 * * * *', PodScalerParallelJob, { scalerType: 'destroyer' }], // Every 10 minutes
    // account plan related
    ['0 * * * *', InvalidWebhooksInactivatorJob], // Every hour
    ['0 * * * *', GracePeriodJob], // Every hour
    ['0 0 * * *', AccountExpiredJob], // Once a day
    ['0 0 * * *', AccountWillExpireJob], // Once a day
    ['0 0 * * *', DeleteUnusedFilesJob], // Once a day
    ['0 0 * * *', SyncFlowDoneJob], // Once a day
    ['*/5 3 * * *', PlatformTrialNotFirstAccessJob],
    ['*/10 3 * * *', PlatformTrialNotWizardFinish],
    ['*/15 3 * * *', PlatformTrialNotAccessJob],
    ['*/25 3 * * *', PlatformClientNotAccessJob],
    // rest
    ['* * * * *', TicketInactiveJob], // Every minute
    ['* * * * *', BotInactiveJob], // Every minute
    ['* * * * *', WebchatContactIdleStagesJob], // Every minute
    ['* * * * *', MessageScheduleJob],
    ['0 */12 * * *', RenewPositusTokenJob], // a cada 12 horas
    ['*/5 * * * *', WabaHealth], // Every 5 minutes
    ['* * * * *', Summary], // Every minute
    ['* * * * *', CopilotSuggestion], // Every minute
    ['0 0 1 * *', HsmLimitResetJob],
    ['*/10 * * * *', RefreshWabaTemplatesJob], // A cada 10 minutos
    ['* * * * *', SyncMessagesJob],
    ['* * * * *', SyncEmailsJob], // Every minute
    ['*/30 * * * *', RefreshEmailOutlookTokenJob], // A cada meia hora
    ['* * * * *', OfflineUserLogoutJob], // A todo minuto
    ['* * * * *', DistributionJob], // A todo minuto

    // Credit Movement
    ['* * * * *', AccountNotificationJob, { event: 'credits-consumed' }], // Every minute (tempo de teste)
    ['0 * * * *', CreditsRenewalJob], // Every hour
    ['0 0 * * *', AiCreditExpirationJob], // Once a day

    // Campaign
    ['*/10 * * * * *', InitPrepareCampaignsJob], // inicia preparação da campanha
    ['*/10 * * * * *', InitSendCampaignJob], // inicia envio da campanha

    // User
    ['0 0 * * *', RefreshUsersInternalchatTokenJob],
    ['* * * * *', MFAUserEmailJob], // Expiraçao de senha
    ['0 0 * * *', NotificationExpiredPassword],

    // IA CSAT
    ['* * * * * ', CsatFeedbackScoreJob],

    //Funil de vendas
    ['0 0 * * *', OpportunityNotifications], // Once a day

    // Personal Access Tokens Notification
    ['0 1 * * *', PersonalAccessTokenExpirationJob],
  ]

  protected runningCronJobs: Schedule.Job[]

  async onStart() {
    if (this.config.get('useCachedTicketsCount')) {
      this.cronJobs.push(
        [this.config.get('refreshTicketCountCronExpression'), RefreshTicketsCountCacheJob], // A cada 5 segundos
      )
    }

    // Só definir esse valor no arquivo .env do cliente, se estiver com use-block-message-rules-by-service habilitado na conta
    // Caso contrário, vai gerar processamento de cron sem necessidade
    if (this.config.get('blockMessageRuleCronExpression')) {
      this.cronJobs.push([this.config.get('blockMessageRuleCronExpression'), BlockMessageRuleJob, { event: 'cron' }])
    }

    if (this.config.get('consolidateConsumedCreditsCronExpression')) {
      this.cronJobs.push([this.config.get('consolidateConsumedCreditsCronExpression'), ConsolidateConsumedCreditsJob])
    }

    // Run Cron Jobs
    this.runningCronJobs = this.cronJobs.map(([rule, jobClass, payload]) => {
      this.logger.log(`Registering job "${jobClass.jobName}" to run on "${rule}".`)
      return Schedule.scheduleJob(rule, () => this.runJob(jobClass, payload, rule))
    })

    await this.healthCheckHttpServer.start(this.config.get('appName'), this.config.get('cronPort', 8000))
  }

  async onDeath() {
    await this.healthCheckHttpServer.stop()
    this.runningCronJobs.forEach((job) => Schedule.cancelJob(job))
  }

  protected async runJob(jobClass: JobClass, payload, rule: string) {
    const transaction = this.tracer.startTransaction(`Cron "${jobClass.jobName}"`, 'request')

    transaction.addLabels({ rule: rule })

    try {
      await this.jobsDispatcher.dispatch(jobClass.jobName, payload, {
        timeout: 10 * 60 * 1000,
      })
      transaction.end('success')
    } catch (e) {
      reportError(e)
      transaction.end('failure')
    }
  }
}
