import ffmpeg from 'fluent-ffmpeg'

// Configuração global do ffmpeg
const systemFfmpegPath = process.env.FFMPEG_PATH || '/usr/bin/ffmpeg'
const systemFfprobePath = process.env.FFPROBE_PATH || '/usr/bin/ffprobe'

console.log('Global ffmpeg configuration:')
console.log('- FFMPEG_PATH:', systemFfmpegPath)
console.log('- FFPROBE_PATH:', systemFfprobePath)

// Configurar caminhos globalmente
ffmpeg.setFfmpegPath(systemFfmpegPath)
ffmpeg.setFfprobePath(systemFfprobePath)

console.log('FFmpeg configured globally')

export { systemFfmpegPath, systemFfprobePath }
export default ffmpeg
