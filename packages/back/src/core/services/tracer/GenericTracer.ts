import { type Tracer, Outcome, Span, TracerToken, Transaction } from './Tracer'
import { Container, Service } from 'typedi'
import { IncomingMessage, ServerResponse } from 'http'
import Crypto from 'crypto'
import RequestContextCls, { TraceIds } from '../cls/RequestContextCls'

export class GenericTransaction implements Transaction {
  constructor(protected tracer: GenericTracer, protected transactionId: string) {}

  addLabels(labels: { [p: string]: string }): void {}

  setName(name: string) {}

  setOutcome(outcome: Outcome) {}

  end(outcome?: Outcome): void {}

  startSpan(name: string, type: string): Span {
    return this.tracer.startSpan(name, type)
  }
}

export class GenericSpan implements Span {
  constructor(protected tracer: GenericTracer, protected transaction: GenericTransaction, protected spanId: string) {}

  addLabels(labels: { [p: string]: string }): void {}

  setOutcome(outcome: Outcome) {}

  end(outcome?: Outcome): void {}

  getTransaction() {
    return this.transaction
  }
}

@Service()
export class GenericTracer implements Tracer {
  protected requestContextCls: RequestContextCls

  constructor() {
    this.requestContextCls = Container.get(RequestContextCls)
  }

  start(options: {}) {
    return this
  }

  getCurrentTraceIds(): { 'transaction.id'?: string; 'trace.id'?: string; 'span.id'?: string } {
    const traceIds = this.ensureTraceIds()

    return {
      'transaction.id': traceIds.transactionId,
      'trace.id': traceIds.traceId,
      'span.id': traceIds.spanId,
    }
  }

  getCurrentTraceParent(): string {
    const { traceId, spanId } = this.requestContextCls.getTraceIds()

    return this.generateTraceparent(traceId, spanId)
  }

  startTransaction(name: string, type: string, options?: { childOf: string }): Transaction {
    this.ensureTraceIds()

    const newTransactionId = this.createSpanId()
    const newSpanId = this.createSpanId()

    this.requestContextCls.setTraceIds({ transactionId: newTransactionId, spanId: newSpanId }, true)

    return new GenericTransaction(this, newTransactionId)
  }

  startGenericTransaction(): Transaction {
    const newTransactionId = this.createSpanId()
    const newSpanId = this.createSpanId()

    this.requestContextCls.setTraceIds({ transactionId: newTransactionId, spanId: newSpanId }, true)

    return new GenericTransaction(this, newTransactionId)
  }

  getCurrentTransaction() {
    const { transactionId } = this.ensureTraceIds()
    return new GenericTransaction(this, transactionId)
  }

  startSpan(name: string, type?: string): Span {
    const { transactionId } = this.ensureTraceIds()
    const transaction = new GenericTransaction(this, transactionId)

    const newSpanId = this.createSpanId()

    this.requestContextCls.setTraceIds({ spanId: newSpanId }, true)

    return new GenericSpan(this, transaction, newSpanId)
  }

  setLabel(name: string, value: string | number | boolean | null | undefined): void {}

  setUserContext(user: { id?: string | number; username?: string; email?: string }): void {}

  captureError(error: Error, options?: { request?: IncomingMessage; response?: ServerResponse }): void {}

  protected ensureTraceIds(): TraceIds {
    let traceIds = this.requestContextCls.getTraceIds()

    if (traceIds.transactionId) return traceIds

    const parsedTraceparent = traceIds.traceparent && this.parseTraceparent(traceIds.traceparent)

    const traceId = parsedTraceparent?.traceId || this.createTraceId()
    const transactionId = this.createSpanId()
    const spanId = this.createSpanId()

    traceIds = {
      traceId,
      transactionId,
      spanId,
      parentId: parsedTraceparent?.parentId,
    }

    this.requestContextCls.setTraceIds(traceIds, true)

    return traceIds
  }

  protected generateTraceparent(traceId: string, id: string) {
    // https://github.com/w3c/trace-context/blob/main/spec/20-http_request_header_format.md#considerations-for-trace-id-field-generation
    const version = '00' // the W3C protocol version
    const flags = '01' // 01 means "sampled"
    return `${version}-${traceId}-${id}-${flags}`
  }

  protected parseTraceparent(traceparent: string) {
    const [version, traceId, parentId, flags] = traceparent.split('-')

    return { version, traceId, parentId, flags }
  }

  protected createSpanId() {
    return Crypto.randomBytes(8).toString('hex')
  }

  protected createTraceId() {
    return Crypto.randomBytes(16).toString('hex')
  }
}

Container.set(TracerToken, new GenericTracer())
