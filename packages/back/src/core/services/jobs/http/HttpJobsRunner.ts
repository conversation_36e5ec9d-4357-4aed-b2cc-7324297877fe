import { Container, Inject, Service } from 'typedi'
import express, { Response, Request } from 'express'
import { Server } from 'http'
import bodyParser from 'body-parser'
import { JobClass } from '../interfaces/Job'
import Config from '../../config/Config'
import getJobName from '../getJobName'
import Logger from '../../logs/Logger'
import tracing from '../../../middlewares/tracing'
import RequestContextCls, { Context } from '../../cls/RequestContextCls'
import errorHandler from '../../../middlewares/errorHandler'
import { serializeError } from '../../../utils/error/serializeError'
import reportError from '../../logs/reportError'
import configValues from '../../../../microServices/workers/configValues'
import { type Tracer, TracerToken } from '../../tracer/Tracer'

@Service()
export default class HttpJobsRunner {
  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected logger: Logger

  @Inject()
  protected requestContextCls: RequestContextCls

  @Inject(TracerToken)
  protected tracer: Tracer

  protected jobs: Map<string, JobClass> = new Map()

  protected httpServer: Server

  registerJob(jobClass: JobClass) {
    const jobName = getJobName(jobClass)

    if (this.jobs.has(jobName)) {
      throw new Error(`There was already a job registered with name "${jobName}".`)
    }

    this.jobs.set(jobName, jobClass)

    this.logger.log(`Job "${jobName}" registered.`)
  }

  async start() {
    const app = express()
    app.use(bodyParser.json({ limit: '100mb' }))
    app.use(bodyParser.text({ limit: '100mb' }))
    app.use(tracing())
    app.use(async (req, res, next) => {
      this.tracer.getCurrentTransaction().setName(`${req.method} ${req.path}`)
      next()
    })
    app.get('/health', this.healthHandler.bind(this))
    app.post('/run/:job', this.postHandler.bind(this))
    app.use(errorHandler)

    const port = this.config.get('workersPort', 8080)
    this.httpServer = await new Promise((resolve) => {
      const server = app.listen(port, () => resolve(server))
    })

    this.logger.log(`Started HttpJobsRunner on port ${port}.`)
  }

  async stop() {
    await new Promise((resolve) => this.httpServer.close(resolve))
  }

  protected getPayload(req: Request) {
    const { raw, wrapped } = req.query
    if (raw) return req.body
    if (wrapped) return req.body.payload?.payload // from queue-manager
    return req.body.payload // from http dispatcher
  }

  protected getMetadata(req: Request) {
    const { raw, wrapped } = req.query
    if (raw) return {}
    if (wrapped) return req.body.payload?.metadata // from queue-manager
    return req.body.metadata // from http dispatcher
  }

  protected async postHandler(req: Request, res: Response) {
    const { job } = req.params
    const payload = this.getPayload(req)
    const metadata = this.getMetadata(req)
    const context: Context = metadata?.context

    if (!this.jobs.has(job)) {
      throw new Error(`No job with name "${job}" was registered.`)
    }

    const jobClass = this.jobs.get(job)

    await this.requestContextCls.run(async () => {
      const span = this.tracer.startSpan(`Running "${jobClass.jobName}"`)
      this.tracer.setLabel('job', jobClass.jobName)

      const jobTraces = [...(context?.jobTraces || []), jobClass.jobName]
      this.requestContextCls.setContext({ ...context, jobTraces }, true)

      try {
        const data = await this.run(jobClass, payload)
        // @ts-ignore
        res.json(data)
        if (span) span.setOutcome('success')
      } catch (e) {
        // console.log(e)
        reportError(e)
        res.status(500).send(serializeError(e?.response?.data || e))
        if (span) span.setOutcome('failure')
      }

      if (span) span.end()
    })
  }

  protected healthHandler(req: Request, res: Response) {
    res.sendStatus(200)
  }

  run(jobClass: JobClass, payload: any) {
    const jobName = getJobName(jobClass)
    this.logger.log(`Running job "${jobName}".`, 'info')
    const job = Container.get(jobClass)
    return job.handle(payload)
  }
}
