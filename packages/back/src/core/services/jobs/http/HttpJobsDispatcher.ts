import { Inject, Service } from 'typedi'
import Job from '../interfaces/Job'
import JobsDispatcher from '../interfaces/JobsDispatcher'
import Config from '../../config/Config'
import Logger from '../../logs/Logger'
import RequestContextCls from '../../cls/RequestContextCls'
import { handleAxiosError } from '../../../utils/error/serializeError'
import configValues from '../../../configValues'
import { type Tracer, TracerToken } from '../../tracer/Tracer'
import { HttpClient } from '../../httpClient/HttpClient'

@Service()
export default class HttpJobsDispatcher implements JobsDispatcher {
  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected logger: Logger

  @Inject()
  protected requestContextCls: RequestContextCls

  @Inject(TracerToken)
  protected tracer: Tracer

  @Inject()
  protected httpClient: HttpClient

  async dispatch<T extends Job>(
    jobName: string,
    payload?: Parameters<T['handle']>[0],
    options: { timeout?: number; useWorkersGo?: boolean } = {},
  ): Promise<ReturnType<T['handle']>> {
    const span = this.tracer.startSpan(`Dispatch "${jobName}"`, 'request')

    this.logger.log(`Dispatching job "${jobName}"`, 'info')
    const elapsed = Date.now()
    const traceparent = this.tracer.getCurrentTraceParent()
    const metadata = {
      context: this.requestContextCls.getContext(),
    }

    try {
      const result = await this.httpClient
        .post(
          `${options.useWorkersGo ? this.config.get('workersGoUrl') : this.config.get('workersUrl')}/run/${jobName}`,
          { payload, metadata },
          {
            headers: { traceparent },
            timeout: options.timeout,
            maxContentLength: 2147483640,
          },
        )
        .then((res) => res.data)
        .catch(handleAxiosError)

      // res && this.logger.log(
      //   JSON.stringify(res, null, 2)
      // )

      this.logger.log(`Job "${jobName}" returned (${Date.now() - elapsed}ms).`, 'info')

      span.end('success')

      return result
    } catch (e) {
      span.end('failure')

      throw e
    }
  }
}
