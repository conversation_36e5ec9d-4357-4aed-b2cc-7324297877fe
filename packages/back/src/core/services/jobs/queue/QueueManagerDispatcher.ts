import { Inject, Service } from 'typedi'
import Config from '../../config/Config'
import Logger from '../../logs/Logger'
import configValues from '../../../configValues'
import RequestContextCls from '../../cls/RequestContextCls'
import { type Tracer, TracerToken } from '../../tracer/Tracer'
import { HttpClient } from '../../httpClient/HttpClient'

@Service()
export default class QueueManagerDispatcher {
  @Inject()
  protected config: Config<typeof configValues>

  @Inject()
  protected logger: Logger

  @Inject()
  protected requestContextCls: RequestContextCls

  @Inject()
  protected httpClient: HttpClient

  @Inject(TracerToken)
  protected tracer: Tracer

  async dispatch<T>(
    topic: string,
    payload?: any,
    options?: {
      hashKey?: string
    },
  ): Promise<T> {
    const id = payload?.data?.id

    this.logger.log('Dispatching to queue manager: %s - id: %s - hashKey: %s - payload: %j', 'info', [
      topic,
      id,
      options?.hashKey,
      payload?.data,
    ])

    const dispatchUrl = this.config.get('queueManagerDispatchUrl')

    const metadata = {
      traceparent: this.tracer.getCurrentTraceParent(),
      context: this.requestContextCls.getContext(),
    }

    const requestPayload = {
      topic,
      hashKey: options?.hashKey || payload?.id || null,
      payload: {
        payload,
        metadata,
      },
    }

    try {
      const response = await this.httpClient.post(dispatchUrl, requestPayload)

      this.logger.log('Successfully dispatched to queue manager: %s - id: %s - hashKey: %s', 'info', [
        topic,
        id,
        options?.hashKey,
      ])

      return response.data as T
    } catch (error) {
      this.logger.log('Failed to dispatch to queue manager: %s - id: %s - hashKey: %s - error: %j', 'error', [
        topic,
        id,
        options?.hashKey,
        error.message,
      ])
      throw error
    }
  }
}
