import fs from 'fs'
import { Readable } from 'stream'
import flffmpeg from 'fluent-ffmpeg'
import { spawn } from 'child_process'

// Usar ffmpeg do sistema
const systemFfmpegPath = process.env.FFMPEG_PATH || '/usr/bin/ffmpeg'
flffmpeg.setFfmpegPath(systemFfmpegPath)

type AudioDataFFmpeg = {
  duration: number
  bitRate: number
  sampleRate: number
  channels: number
  samples: number[]
}

type AudioMetadata = {
  duration: number
  bitRate: number
  sampleRate: number
  channels: number
  peaks: number[]
}

const waveformTypeEnum = {
  STACK: 0,
  LINE: 1,
}

/**
 * Normalização de áudio
 * Os valores de amplitude do áudio podem ser mapeados para a faixa de -1.0 a +1.0 (valor normalizado)
 * Para normalizar os valores inteiros de 16 bits, o valor máximo (32767.0) é usado
 */
const audioNormalize = 32767.0

/**
 * Calcula o logaritmo na base 10 de um número
 */
const log10 = (arg: number): number => {
  return Math.log(arg) / Math.LN10
}

/**
 *  Converter um coeficiente (amplitude) para decibéis
 */
const coefficientToDb = (coeff: number): number => {
  // Multiplicamos coeff por 20.0, porque para conversões de amplitude, a fórmula para decibéis é essa:
  return 20.0 * log10(coeff)
}

/**
 * Calcula uma medição de potência de maneira não linear, com base em uma faixa de valores decibéis e um fator de não linearidade
 * power_db: A quantidade de "potência" ou valor em decibéis que está sendo medido
 * lower_db e upper_db: A faixa de valores de decibéis em que a medição ocorre
 * non_linearity: Um fator que controla a não linearidade da escala
 */
const logMeter = (power_db: number, lower_db: number, upper_db: number, non_linearity: number): number => {
  if (power_db < lower_db) {
    return 0
  } else {
    return Math.pow((power_db - lower_db) / (upper_db - lower_db), non_linearity)
  }
}

/**
 * Calcula a medição de potência não linear (logMeter) com valores fixos
 * lower_db: O valor mínimo em decibéis (-192)
 * upper_db: O valor máximo em decibéis (normalmente, o ponto de referência é 0 decibéis)
 * non_linearity: Um fator de não linearidade que é relativamente alto (8), indicando uma curva muito comprimida
 */
const altLogMeter = (power_db: number): number => {
  return logMeter(power_db, -192.0, 0.0, 8.0)
}

/**
 * Obter os picos de cada pedaço da amostra
 */
const getPeaks = (waveformType: number, samplesPerPeak: number, samples: number[]): number[] => {
  const peaks: number[] = []

  if (waveformType === waveformTypeEnum.LINE) {
    for (let i = 0; i < samples.length; i += samplesPerPeak) {
      peaks.push(samples[i])
    }
  } else if (waveformType === waveformTypeEnum.STACK) {
    // Calcular o pico das amostras
    let partialMax = 0
    let sampleIndex = 0

    for (let index in samples) {
      const value = Math.abs(samples[index])
      sampleIndex++

      if (value > partialMax) {
        partialMax = value
      }

      if (sampleIndex >= samplesPerPeak) {
        peaks.push(altLogMeter(coefficientToDb(partialMax)))
        partialMax = 0
        sampleIndex = 0
      }
    }
  } else {
    throw new Error(`Unsupported wave form type: ${waveformType}`)
  }

  return peaks
}

/**
 * Obter determinada quantidade de picos da amostra
 */
const getByNumberOfPeaks = (waveformType: number, numberOfPeaks: number, samples: number[]): number[] => {
  const samplesPerPeak = Math.ceil(samples.length / numberOfPeaks)

  const peaks = getPeaks(waveformType, samplesPerPeak, samples)

  while (peaks.length < numberOfPeaks) {
    peaks.push(peaks[0])
  }

  return peaks
}

/**
 * Obter determinada quantidade de picos para cada segundo da amostra
 */
const getByPeaksPerSecond = (
  waveformType: number,
  peaksPerSecond: number,
  samples: number[],
  duration: number,
): number[] => {
  let samplesPerSecond = Math.ceil(samples.length / duration)

  if (samplesPerSecond > peaksPerSecond) {
    samplesPerSecond = peaksPerSecond
  }

  const samplesPerPeak = Math.ceil(samples.length / (samplesPerSecond * duration))

  return getPeaks(waveformType, samplesPerPeak, samples)
}

/**
 * Criar um arquivo temporário
 */
export const getTempFile = (fileStream: Readable, fileId: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (fileStream instanceof Readable && typeof fileId === 'string') {
      const tempFilePath = `/tmp/audio-${fileId}.tmp`

      const writer = fs.createWriteStream(tempFilePath)
      fileStream.pipe(writer)

      writer.on('finish', () => {
        resolve(tempFilePath)
      })

      writer.on('error', () => {
        reject('getTempFile: error to write fileStream')
      })

      return
    }

    reject('getTempFile: fileStream or fileId are invalid')
  })
}

/**
 * Extrair os dados do áudio através do FFmpeg
 */
const getAudioDataFromFFmpeg = (filePath: string): Promise<AudioDataFFmpeg> => {
  return new Promise((resolve, reject) => {
    let outputStr = ''
    let gotData = false
    let oddByte = null
    const samples: number[] = []

    // Extract signed 16-bit little endian PCM data with ffmpeg and pipe to stdout
    const ffmpeg = spawn(systemFfmpegPath, ['-i', filePath, '-f', 's16le', '-acodec', 'pcm_s16le', '-y', 'pipe:1'])

    ffmpeg.stdout.on('data', function (data) {
      // Obter as amostras do áudio
      gotData = true

      let value: number
      let i = 0
      const dataLen = data.length

      if (oddByte !== null) {
        // If there is a leftover byte from the previous block, combine it with the first byte from this block
        value = ((data.readInt8(i++, true) << 8) | oddByte) / audioNormalize
        samples.push(value)
      }

      for (; i < dataLen; i += 2) {
        value = data.readInt16LE(i, true) / audioNormalize
        samples.push(value)
      }

      oddByte = i < dataLen ? data.readUInt8(i, true) : null
    })

    ffmpeg.stderr.on('data', function (data) {
      // As informações do ffmpeg são enviadas para o stderr (vamos usar para obter metadados do áudio)
      outputStr += data.toString()
    })

    ffmpeg.stderr.on('end', function () {
      // Obter metadados do áudio
      if (!gotData) {
        reject(`getAudioDataFromFFmpeg: Error to got data (${outputStr})`)
      }

      // Obter duração, bitRate, sampleRate e canais do áudio
      const pattern1 = new RegExp(/Duration: ([0-9:.]*),( start: -?[0-9:.]*,)? bitrate: ([0-9]*) kb/)
      const pattern2 = new RegExp(/Stream #[0-9:]*.* Audio: [^,]*, ([0-9]*) Hz, ([a-z]*),/)

      const matches1 = outputStr.match(pattern1)
      const matches2 = outputStr.match(pattern2)

      if (!matches1 || !matches2) {
        reject('getAudioDataFromFFmpeg: Error to match outputStr')
      }

      // Calcular a duração
      const durationList = matches1[1].split(new RegExp('[:.]', 'g'))
      let duration = 0
      for (let i = 0; i < 3; i++) {
        duration *= 60
        duration += parseInt(durationList[i])
      }

      // Obter os canais
      let channels = null
      if (matches2[2] !== undefined) {
        if (matches2[2] === 'mono') {
          channels = 1
        } else {
          channels = 2
        }
      }

      resolve({
        duration,
        bitRate: parseInt(matches1[3]),
        sampleRate: parseInt(matches2[1]),
        channels,
        samples,
      })
    })
  })
}

/**
 * Obter os dados do arquivo de áudio
 */
export const getAudioMetadata = async (
  fileStream: Readable,
  fileId: string,
  options: { numberOfPeaks?: number; peaksPerSecond?: number; waveformType?: number } = {},
): Promise<AudioMetadata> => {
  const numberOfPeaks = options.numberOfPeaks || 100
  const peaksPerSecond = options.peaksPerSecond || undefined
  const waveformType = options.waveformType || waveformTypeEnum.STACK

  if (numberOfPeaks < 1) {
    throw new Error('Number of peaks should larger than 1')
  }

  const tempFilePath = await getTempFile(fileStream, fileId)

  const { duration, bitRate, sampleRate, channels, samples } = await getAudioDataFromFFmpeg(tempFilePath)

  let peaks: number[] = []
  if (peaksPerSecond === undefined) {
    peaks = getByNumberOfPeaks(waveformType, numberOfPeaks, samples)
  } else {
    peaks = getByPeaksPerSecond(waveformType, peaksPerSecond, samples, duration)
  }

  fs.unlinkSync(tempFilePath)

  return {
    duration,
    bitRate,
    sampleRate,
    channels,
    peaks,
  }
}
