/**
 * Wrapper for pdf-parse to prevent test code execution
 * This wrapper ensures module.parent exists during import to prevent debug mode activation
 * without modifying the original library code.
 */

// Single Promise to ensure the module is loaded only once, preventing concurrency issues
let pdfParsePromise: Promise<any> | null = null

const getPdfParse = (): Promise<any> => {
  // If already loading or loaded, return the same Promise
  if (pdfParsePromise) {
    return pdfParsePromise
  }

  // Create the Promise only once - this prevents concurrency issues
  pdfParsePromise = (async () => {
    // The key insight: pdf-parse checks `!module.parent` to determine if it's running in debug mode
    // We need to ensure module.parent exists when pdf-parse is loaded

    // Store original module state
    const originalModule = (globalThis as any).module

    // Create a mock module with parent to fool the debug check
    const mockModule = {
      parent: { filename: __filename }, // This makes !module.parent = false
      exports: {},
      require: require,
      id: __filename,
      filename: __filename,
      loaded: false,
      children: [],
      paths: []
    }

    // Temporarily set the global module to our mock
    ;(globalThis as any).module = mockModule

    try {
      // Now import pdf-parse - it will see module.parent exists and skip debug mode
      const imported = await import('pdf-parse')
      return imported.default || imported
    } finally {
      // Restore original module state
      if (originalModule !== undefined) {
        ;(globalThis as any).module = originalModule
      } else {
        delete (globalThis as any).module
      }
    }
  })()

  return pdfParsePromise
}

// Create a wrapper function that mimics the pdf-parse interface
const pdfParse = async (buffer: Buffer, options?: any) => {
  const pdfParseFunc = await getPdfParse()
  return pdfParseFunc(buffer, options)
}

export default pdfParse
