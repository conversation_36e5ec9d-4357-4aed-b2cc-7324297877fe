import fs from 'fs'
import ffmpeg from '../config/ffmpeg'
import fileType from 'file-type'
import { Readable, Writable } from 'stream'
import { v4 as uuid } from 'uuid'
import { Container } from 'typedi'
import serviceResource from '../resources/serviceResource'
import Logger from '../services/logs/Logger'
import { getTempFile } from './parseAudio'

const logger = Container.get(Logger)

export const mimetypeIsAudio = (mimetype: string) => mimetype.startsWith('audio/')

// Desejável aac ou mp3 sempre que possível, senão oga com codec opus
// voice: audio de voz gravado
// audio: qualquer audio enviado como arquivo
const compatibleFormatAndCodecForServicetype = {
  telegram: {
    voice: {
      format: 'oga',
      codec: 'libopus',
    },
    audio: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
  },
  'whatsapp-business': {
    voice: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
    audio: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
  },
  whatsapp: {
    voice: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
    audio: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
  },
}

export const getFormatAndCodecByServiceId = async (
  serviceId: string,
  isPtt: boolean,
): Promise<{
  format: 'adts' | 'mp3' | 'oga'
  codec: 'aac' | 'libmp3lame' | 'libopus'
  channels?: number
  bitrate?: number
}> => {
  const service = serviceId && (await serviceResource.findById(serviceId, { cache: true }))

  return ((service && compatibleFormatAndCodecForServicetype[service.type]) || {
    voice: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
    audio: {
      format: 'mp3',
      codec: 'libmp3lame',
    },
  })[isPtt ? 'voice' : 'audio']
}

export const mustConvertAudio = async (mimetype, isPtt, serviceId?) => {
  const [type, extension] = mimetype.split('/')
  if (type !== 'audio') return false

  if (!serviceId) return extension !== 'aac'

  const service = await serviceResource.findById(serviceId, { cache: true })

  if (service.type === 'telegram') {
    if (isPtt) return extension !== 'ogg'

    return extension !== 'mp3'
  }

  return extension !== 'aac'
}

export const getFileTypeFromBuffer = async (buffer: Buffer) => {
  let { ext, mime } = (await fileType.fromBuffer(buffer)) || {}

  // Geralmente os brokers usam extensão oga mesmo sendo codec opus (whatsapp, telegram, etc)
  if (ext === 'opus') ext = 'oga'

  return { ext, mime }
}

export const convertAudioStreamToMp3 = (stream: Readable) =>
  ffmpeg(stream)
    .on('error', (err) => logger.log('An error occurred on convert media to mp3: ' + err.message, 'error'))
    .format('mp3')
    .audioCodec('libmp3lame')
    .pipe()

export default async (
  audioStream: Readable,
  format: string,
  codec: string,
  channels?: number,
  bitrate?: number,
): Promise<Writable> => {
  const tmpFile = await getTempFile(audioStream, uuid())
  const audioCommand = ffmpeg(tmpFile)
    .on('error', (err) => logger.log('An error occurred on convert media: ' + err.message, 'error'))
    .on('end', () => {
      fs.unlinkSync(tmpFile)
    })
    .format(format)
    .audioCodec(codec)

  if (channels) {
    audioCommand.audioChannels(channels)
  }

  if (bitrate) {
    audioCommand.audioBitrate(bitrate)
  }

  return audioCommand.pipe()
}
