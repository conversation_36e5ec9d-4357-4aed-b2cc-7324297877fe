import ffmpeg from '../config/ffmpeg'
import serviceResource from '../resources/serviceResource'

export const mustConvertVideo = async (mimetype, serviceId?) => {
  if (!serviceId) return false
  const service = await serviceResource.findById(serviceId, { cache: true })
  return service.type === 'whatsapp-business' && !mimetype.endsWith('/mp4')
}

export const convertVideo = async (filePath: string, output: string, format: string) => {
  return new Promise<void>((resolve, reject) => {
    ffmpeg(filePath)
      .videoCodec('libx264')
      .audioCodec('aac')
      .outputOptions('-movflags', '+faststart')
      .format(format)
      .save(output)
      .on('end', resolve)
      .on('error', reject)
  })
}
