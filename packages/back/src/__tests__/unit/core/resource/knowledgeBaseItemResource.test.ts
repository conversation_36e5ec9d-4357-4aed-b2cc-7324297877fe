// Mocks antes dos imports
const findKnowledge = jest.fn()
const createKnowledge = jest.fn()
const deleteKnowledgeDocs = jest.fn()

jest.mock('../../../../core/dbSequelize/repositories/knowledgeBaseItemRepository', () => ({
  findById: jest.fn(),
  create: jest.fn(),
  destroy: jest.fn(),
}))

jest.mock('../../../../core/dbSequelize/repositories/knowledgeBaseItemDocRepository', () => ({
  bulkCreate: jest.fn(),
}))

jest.mock('../../../../core/resources/fileResource', () => ({
  findById: jest.fn(),
  getBuffer: jest.fn(),
  updateById: jest.fn(),
}))

jest.mock('../../../../core/services/haystackIa', () => {
  const mockFindKnowledge = jest.fn()
  const mockCreateKnowledge = jest.fn()
  const mockDeleteKnowledgeDocs = jest.fn()

  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      findKnowledge: mockFindKnowledge,
      createKnowledge: mockCreateKnowledge,
      deleteKnowledgeDocs: mockDeleteKnowledgeDocs,
    })),
    // Export for access in tests
    mockFindKnowledge,
    mockCreateKnowledge,
    mockDeleteKnowledgeDocs,
  }
})

jest.mock('../../../../core/resources/BaseResource', () => jest.fn())

import { Container } from 'typedi'
import KnowledgeBaseItemResource from '../../../../core/resources/knowledgeBaseItemResource'
import knowledgeBaseItemRepository from '../../../../core/dbSequelize/repositories/knowledgeBaseItemRepository'
import knowledgeBaseItemDocRepository from '../../../../core/dbSequelize/repositories/knowledgeBaseItemDocRepository'
import fileResource from '../../../../core/resources/fileResource'
import pdfParse from '../../../../core/utils/pdfParseWrapper'
jest.mock('../../../../core/utils/pdfParseWrapper', () => jest.fn())

describe('KnowledgeBaseItemResource', () => {
  let resource: any

  beforeEach(() => {
    jest.clearAllMocks()

    jest.spyOn(Container, 'get').mockReturnValue({
      findKnowledge,
      createKnowledge,
      deleteKnowledgeDocs,
    })

    const ResourceClass = KnowledgeBaseItemResource
    resource = new ResourceClass()
  })

  describe('findById', () => {
    it('should return knowledge with enriched docs', async () => {
      const fakeKnowledge = {
        dataValues: { id: '1', accountId: 'acc1' },
        docs: [{ docId: 'doc1' }],
        accountId: 'acc1',
      }

      ;(knowledgeBaseItemRepository.findById as jest.Mock).mockResolvedValue(fakeKnowledge)
      findKnowledge.mockResolvedValue([{ docId: 'doc1', enriched: true }])

      const result = await resource.findById('1')

      expect(findKnowledge).toHaveBeenCalledWith(['doc1'], 'acc1')
      expect(result.docs).toEqual([{ docId: 'doc1', enriched: true }])
    })

    it('should return knowledge without docs if docs array is empty', async () => {
      const fakeKnowledge = {
        dataValues: { id: '2' },
        docs: [],
      }

      ;(knowledgeBaseItemRepository.findById as jest.Mock).mockResolvedValue(fakeKnowledge)

      const result = await resource.findById('2')

      expect(findKnowledge).not.toHaveBeenCalled()
      expect(result).toEqual(fakeKnowledge)
    })
  })

  describe('create', () => {
    it('should create item with file', async () => {
      const data = { fileId: 'file1', name: 'My File', accountId: 'acc1' }
      const file = { name: 'file.pdf' }
      const buffer = Buffer.from('some data')
      const createdItem = { id: 'item1', accountId: 'acc1' }

      ;(fileResource.findById as jest.Mock).mockResolvedValue(file)
      ;(fileResource.getBuffer as jest.Mock).mockResolvedValue(buffer)
      createKnowledge.mockResolvedValue({ docIds: ['doc1'], sourceId: 'source1' })
      ;(knowledgeBaseItemRepository.create as jest.Mock).mockResolvedValue(createdItem)

      const result = await resource.create(data)

      expect(fileResource.updateById).toHaveBeenCalledWith('file1', {
        attachedId: 'item1',
        attachedType: 'knowledgebase.item',
      })

      expect(knowledgeBaseItemDocRepository.bulkCreate).toHaveBeenCalledWith(
        [{ knowledgeBaseItemId: 'item1', docId: 'doc1', accountId: 'acc1' }],
        {},
      )

      expect(result).toEqual(createdItem)
    })

    it('should create item without file', async () => {
      const data = { url: 'google.com.br', name: 'My File', accountId: 'acc1' }
      const createdItem = { id: 'item1', accountId: 'acc1' }

      createKnowledge.mockResolvedValue({ docIds: ['doc1'], sourceId: 'source1' })
      ;(knowledgeBaseItemRepository.create as jest.Mock).mockResolvedValue(createdItem)

      const result = await resource.create(data)

      expect(knowledgeBaseItemDocRepository.bulkCreate).toHaveBeenCalledWith(
        [{ knowledgeBaseItemId: 'item1', docId: 'doc1', accountId: 'acc1' }],
        {},
      )

      expect(result).toEqual(createdItem)
    })

    it('should throw error if no source is provided', async () => {
      const data = { name: 'invalid', accountId: 'acc1' }
      await expect(resource.create(data)).rejects.toThrow('You must provide fileId, text, or url')
    })
  })

  describe('destroyById', () => {
    it('should delete knowledge and docs', async () => {
      const item = {
        id: 'item1',
        accountId: 'acc1',
        docs: [{ docId: 'doc1' }, { docId: 'doc2' }],
      }

      ;(knowledgeBaseItemRepository.findById as jest.Mock).mockResolvedValue(item)

      await resource.destroyById('item1')

      expect(deleteKnowledgeDocs).toHaveBeenCalledWith(['doc1', 'doc2'], 'acc1')
      expect(knowledgeBaseItemRepository.destroy).toHaveBeenCalledWith(item, {})
    })

    it('should delete knowledge without docs', async () => {
      const item = { id: 'item2', accountId: 'acc1', docs: [] }
      ;(knowledgeBaseItemRepository.findById as jest.Mock).mockResolvedValue(item)

      await resource.destroyById('item2')

      expect(deleteKnowledgeDocs).not.toHaveBeenCalled()
      expect(knowledgeBaseItemRepository.destroy).toHaveBeenCalledWith(item, {})
    })
  })

  describe('checkPdfText', () => {
    it('should return true when PDF has text', async () => {
      const buffer = Buffer.from('fake pdf content')

      const mockPdfParse = pdfParse as jest.Mock
      mockPdfParse.mockResolvedValue({ text: 'Some PDF text' })

      jest.spyOn(resource, 'getSource' as any).mockResolvedValue({ source: buffer })

      const result = await resource.checkPdfText({} as any)
      expect(result).toBe(true)
    })

    it('should return false when PDF has no text', async () => {
      ;(pdfParse as jest.Mock).mockResolvedValue({
        text: '   ',
      })

      const result = await resource.checkPdfText({} as any)
      expect(result).toBe(false)
    })

    it('should return false if pdfParse throws an error', async () => {
      ;(pdfParse as jest.Mock).mockRejectedValue(new Error('PDF parse failed'))

      const result = await resource.checkPdfText({} as any)
      expect(result).toBe(false)
    })

    it('should return undefined if source is not a Buffer', async () => {
      const buffer = Buffer.from('fake pdf content')

      const mockPdfParse = pdfParse as jest.Mock
      mockPdfParse.mockRejectedValue('Error')

      jest.spyOn(resource, 'getSource' as any).mockResolvedValue({ source: buffer })

      const result = await resource.checkPdfText({} as any)
      expect(result).toBe(false)
    })
  })
})
