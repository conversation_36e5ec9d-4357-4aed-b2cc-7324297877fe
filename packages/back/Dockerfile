FROM oven/bun:alpine

WORKDIR /app

# COPY package.no-version.json package.json
COPY package.json package.json

COPY bun.lock .npmrc ./
RUN bun install --no-cache

COPY src ./src
COPY tsconfig.json .sequelizerc ./

RUN apk add --no-cache ffmpeg

# Verificar se ffmpeg foi instalado corretamente
RUN ffmpeg -version

# Configurar ffmpeg para usar o binário do sistema
ENV FFMPEG_PATH=/usr/bin/ffmpeg
ENV FFPROBE_PATH=/usr/bin/ffprobe

RUN bun build --entrypoints ./src/microServices/*/index.ts --outdir ./dist --target bun --packages external