# Build stage
FROM oven/bun:alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache ffmpeg

# Copy dependency files
COPY package.json bun.lock .npmrc ./

# Install dependencies
RUN bun install --frozen-lockfile --production=false

# Copy source code
COPY src ./src
COPY tsconfig.json .sequelizerc ./

# Configure ffmpeg paths
ENV FFMPEG_PATH=/usr/bin/ffmpeg
ENV FFPROBE_PATH=/usr/bin/ffprobe

# Build application
RUN bun build --entrypoints ./src/microServices/*/index.ts --outdir ./dist --target bun --packages external

# Production stage
FROM oven/bun:alpine AS production

WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache ffmpeg

# Configure ffmpeg paths
ENV FFMPEG_PATH=/usr/bin/ffmpeg
ENV FFPROBE_PATH=/usr/bin/ffprobe

# Copy package files for production install
COPY --from=builder /app/package.json /app/bun.lock /app/.npmrc ./

# Install only production dependencies
RUN bun install --frozen-lockfile --production

# Copy built application
COPY --from=builder /app/dist ./dist

# Copy source files needed at runtime
COPY src ./src
COPY storage ./storage
COPY tsconfig.json .sequelizerc ./