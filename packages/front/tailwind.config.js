import { tailwind } from '@ikatec/nebula-react'
import { colors, fontSizes, fontWeights, letterSpacings, lineHeights, radii } from '@ikatec/nebula-tokens'

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/**/*.{js,jsx,ts,tsx}',
    './src/components/**/**/*.{js,jsx,ts,tsx}',
    './src/routes/**/**/*.{js,jsx,ts,tsx}',
    './src/layouts/**/**/*.{js,jsx,ts,tsx}',
    'node_modules/@ikatec/nebula-react/dist/**/*.mjs',
    './src/app/components/App/Dashboard/Pipelines/EmptyState/index.tsx',
  ],
  important: true,
  darkMode: ['class'],
  theme: {
    extend: {
      colors: { ...colors },
      fontSize: { ...fontSizes },
      fontWeight: { ...fontWeights },
      letterSpacing: { ...letterSpacings },
      lineHeight: { ...lineHeights },
      borderRadius: { ...radii },
      boxShadow: {
        'actionBar-shadow': `
          0px 3px 3px hsla(0, 0%, 0%, 0.03),
          0px 17px 14px hsla(0, 0%, 0%, 0.05),
          0px 4px 12px hsla(220, 43%, 11%, 0.08),
          inset 0px 0px 0px 1px var(--bulkaction-background-divider)
        `,
      },
      // borderRadius: {
      //   ...radii,
      // },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      animation: {
        'slide-in-from-left': 'slideInFromLeft 0.3s ease-out',
        'slide-out-to-left': 'slideOutToLeft 0.3s ease-in',
        'slide-in-from-right': 'slideInFromRight 0.3s ease-out',
        'slide-out-to-right': 'slideOutToRight 0.3s ease-in',
        'slide-in-from-top': 'slideInFromTop 0.3s ease-out',
        'slide-out-to-top': 'slideOutToTop 0.3s ease-in',
        'slide-in-from-bottom': 'slideInFromBottom 0.3s ease-out',
        'slide-out-to-bottom': 'slideOutToBottom 0.3s ease-in',
        'slide-down-and-fade': 'slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        'slide-left-and-fade': 'slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        'slide-up-and-fade': 'slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        'slide-right-and-fade': 'slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'slide-down-center': 'slideDownAndCenter 120ms ease-in',
        'slide-up-center': 'slideUpAndCenter 0.3s cubic-bezier(.33,.31,.44,1.32)',
      },
      keyframes: {
        'accordion-down': {
          from: { height: 0 },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: 0 },
        },
        slideInFromLeft: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOutToLeft: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-100%)' },
        },
        slideInFromRight: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOutToRight: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        slideInFromTop: {
          '0%': { transform: 'translateY(-100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideOutToTop: {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(-100%)' },
        },
        slideInFromBottom: {
          '0%': { transform: 'translateY(100%)' },
          '100%': { transform: 'translateY(0)' },
        },
        slideOutToBottom: {
          '0%': { transform: 'translateY(0)' },
          '100%': { transform: 'translateY(100%)' },
        },
        slideDownAndFade: {
          from: { opacity: '0', transform: 'translateY(-2px)' },
          to: { opacity: '1', transform: 'translateY(0)' },
        },
        slideLeftAndFade: {
          from: { opacity: '0', transform: 'translateX(2px)' },
          to: { opacity: '1', transform: 'translateX(0)' },
        },
        slideUpAndFade: {
          from: { opacity: '0', transform: 'translateY(2px)' },
          to: { opacity: '1', transform: 'translateY(0)' },
        },
        slideRightAndFade: {
          from: { opacity: '0', transform: 'translateX(-2px)' },
          to: { opacity: '1', transform: 'translateX(0)' },
        },
        slideDownAndCenter: {
          from: {
            transform: 'translateY(0) translateX(-50%)',
          },
          to: { transform: 'translateY(200%) translateX(-50%)' },
        },
        slideUpAndCenter: {
          from: { transform: 'translateY(200%) translateX(-50%)' },
          to: { transform: 'translateY(0) translateX(-50%)' },
        },
      },

      colors: { ...colors },
      fontSize: { ...fontSizes },
      fontWeight: { ...fontWeights },
      letterSpacing: { ...letterSpacings },
      lineHeight: { ...lineHeights },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      addUtilities(
        {
          '.md-block': {
            '@media (min-width: 768px)': {
              display: 'block !important',
            },
          },
          '.md-hidden': {
            '@media (min-width: 768px)': {
              display: 'hidden !important',
            },
          },
          '.md-flex-row': {
            '@media (min-width: 768px)': {
              flexDirection: 'row !important',
            },
          },
          '.md-flex-col': {
            '@media (min-width: 768px)': {
              flexDirection: 'column !important',
            },
          },
          '.md-w-full': {
            '@media (min-width: 768px)': {
              width: '100% !important',
            },
          },
          '.md-w-auto': {
            '@media (min-width: 768px)': {
              width: 'auto !important',
            },
          },
        },
        ['responsive'],
      )
    },
  ],
}
