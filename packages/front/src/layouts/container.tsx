import React, { HTMLAttributes } from 'react'
import { PropsWithChildren } from 'react'
import { cn } from '../app/components/common/unconnected/ui/utils'

const Container = (props: PropsWithChildren) => {
  return <div {...props} className="flex flex-col gap-6 max-w-[1440px] px-6 mx-auto" />
}

interface ContainerHeaderProps extends HTMLAttributes<HTMLElement> {
  mtSize?: 'sm' | 'md'
}

const ContainerHeader = ({ mtSize = 'md', children, className, ...props }: PropsWithChildren<ContainerHeaderProps>) => {
  return (
    <header
      {...props}
      className={cn('grid w-full gap-y-4', className, {
        'mt-6': mtSize === 'sm',
        'mt-10': mtSize === 'md',
      })}
    >
      {children}
    </header>
  )
}

const ContainerContent = ({ className, ...props }: PropsWithChildren<HTMLAttributes<HTMLDivElement>>) => {
  return <div className={cn('w-full mb-10', className)} {...props} />
}

export { Container, ContainerHeader, ContainerContent }
