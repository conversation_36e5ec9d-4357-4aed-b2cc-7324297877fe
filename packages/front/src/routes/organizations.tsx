import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { CreateOrganizations } from '../pages/organizations/create'
import { DeleteOrganizations } from '../pages/organizations/delete'
import { EditOrganizations } from '../pages/organizations/edit'
import { ListOrganizations } from '../pages/organizations/list'
import { ViewOrganizations } from '../pages/organizations/view'

const OrganizationsRoutes = () => {
  return (
    <div className="tailwind">
      <IfUserCanRoute permission="organizations.view" path="/organizations" component={ListOrganizations} />
      <Switch>
        <IfUserCanRoute
          permission="organizations.create"
          exact
          path="/organizations/create"
          component={CreateOrganizations}
        />
        <IfUserCanRoute permission="organizations.view" exact path="/organizations/:id" component={ViewOrganizations} />
        <IfUserCanRoute
          permission="organizations.update"
          exact
          path="/organizations/:id/edit"
          component={EditOrganizations}
        />
        <IfUserCanRoute
          permission="organizations.destroy"
          exact
          path="/organizations/:id/delete"
          component={DeleteOrganizations}
        />
      </Switch>
    </div>
  )
}

export { OrganizationsRoutes }
