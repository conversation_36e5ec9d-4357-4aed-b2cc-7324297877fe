import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { ListBots } from '../pages/bots/list'
import { DeleteBots } from '../pages/bots/delete'

const BotsRoutes = () => {
  return (
    <div className="tailwind">
      <IfUserCanRoute permission="bots.view" path="/bots" component={ListBots} />
      <Switch>
        <IfUserCanRoute permission="bots.destroy" exact path="/bots/:id/delete" component={DeleteBots} />
      </Switch>
    </div>
  )
}

export { BotsRoutes }
