import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { CreateCustomFields } from '../pages/custom-fields/create'
import { EditCustomFields } from '../pages/custom-fields/edit'
import { ViewCustomFields } from '../pages/custom-fields/view'
import { ListCustomFields } from '../pages/custom-fields/list'
import { DeleteCustomFields } from '../pages/custom-fields/delete'

const CustomFieldsListRoutes = () => {
  return (
    <>
      <ListCustomFields />
      <Switch>
        <IfUserCanRoute
          permission="customFields.destroy"
          exact
          path="/custom-fields/:id/delete"
          component={DeleteCustomFields}
        />
      </Switch>
    </>
  )
}

const CustomFieldsRoutes = () => {
  return (
    <div className="tailwind">
      <Switch>
        <IfUserCanRoute
          permission="customFields.create"
          exact
          path="/custom-fields/create"
          component={CreateCustomFields}
        />
        <IfUserCanRoute
          permission="customFields.update"
          exact
          path="/custom-fields/:id/edit"
          component={EditCustomFields}
        />
        <IfUserCanRoute permission="customFields.view" exact path="/custom-fields/:id" component={ViewCustomFields} />
        <IfUserCanRoute permission="customFields.view" path="/custom-fields" component={CustomFieldsListRoutes} />
      </Switch>
    </div>
  )
}

export { CustomFieldsRoutes }
