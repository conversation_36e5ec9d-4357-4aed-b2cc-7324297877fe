import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { ListDepartments } from '../pages/departments/list'
import { CreateDepartment } from '../pages/departments/create'
import { ViewDepartments } from '../pages/departments/view'
import { EditDepartments } from '../pages/departments/edit'
import { ArchiveDepartment } from '../pages/departments/archive'

const DepartmentsRoutes = () => {
  return (
    <div className="tailwind">
      <IfUserCanRoute permission="departments.view" path="/departments" component={ListDepartments} />
      <Switch>
        <IfUserCanRoute permission="departments.create" exact path="/departments/create" component={CreateDepartment} />
        <IfUserCanRoute
          permission="departments.archive"
          exact
          path="/departments/:id/archive"
          component={ArchiveDepartment}
        />
        <IfUserCanRoute permission="departments.view" exact path="/departments/:id" component={ViewDepartments} />
        <IfUserCanRoute
          permission="departments.update"
          exact
          path="/departments/:id/edit"
          component={EditDepartments}
        />
      </Switch>
    </div>
  )
}

export { DepartmentsRoutes }
