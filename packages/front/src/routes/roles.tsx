import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { RolesCreate } from '../pages/roles/create'
import { RolesEdit } from '../pages/roles/edit'
import { RolesList } from '../pages/roles/list'
import { RolesView } from '../pages/roles/view'
import { RolesDelete } from '../pages/roles/delete'

const RolesListRoutes = () => {
  return (
    <>
      <RolesList />
      <Switch>
        <IfUserCanRoute permission="roles.destroy" exact path="/roles/:id/delete" component={RolesDelete} />
      </Switch>
    </>
  )
}

const RolesRoutes = () => {
  return (
    <div className="tailwind">
      <Switch>
        <IfUserCanRoute permission="roles.create" exact path="/roles/create" component={RolesCreate} />
        <IfUserCanRoute permission="roles.update" exact path="/roles/:id/edit" component={RolesEdit} />
        <IfUserCanRoute permission="roles.view" exact path="/roles/:id" component={RolesView} />
        <IfUserCanRoute permission="roles.view" path="/roles" component={RolesListRoutes} />
      </Switch>
    </div>
  )
}

export { RolesRoutes }
