import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { CreateTags } from '../pages/tags/create'
import { DeleteTags } from '../pages/tags/delete'
import { EditTags } from '../pages/tags/edit'
import { ListTags } from '../pages/tags/list'
import { ViewTags } from '../pages/tags/view'

const TagsRoutes = () => {
  return (
    <div className="tailwind">
      <IfUserCanRoute permission="tags.view" path="/tags" component={ListTags} />
      <Switch>
        <IfUserCanRoute permission="tags.create" exact path="/tags/create" component={CreateTags} />
        <IfUserCanRoute permission="tags.view" exact path="/tags/:id" component={ViewTags} />
        <IfUserCanRoute permission="tags.update" exact path="/tags/:id/edit" component={EditTags} />
        <IfUserCanRoute permission="tags.destroy" exact path="/tags/:id/delete" component={DeleteTags} />
      </Switch>
    </div>
  )
}

export { TagsRoutes }
