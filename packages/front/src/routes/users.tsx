import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { CreateUsers } from '../pages/users/create'
import { EditUsers } from '../pages/users/edit'
import { ListUsers } from '../pages/users/list'
import { ViewUsers } from '../pages/users/view'
import { ArchiveUser } from '../pages/users/archive'
import { UnblockUser } from '../pages/users/unblock'
import { ResetPassword } from '../pages/users/reset-password'
import { UpdateTimetable } from '../pages/users/update-timetable'

const UserListRoutes = () => {
  return (
    <>
      <ListUsers />
      <Switch>
        <IfUserCanRoute permission="users.view" exact path="/users/:id/reset-password" component={ResetPassword} />
        <IfUserCanRoute permission="users.view" exact path="/users/update-timetable" component={UpdateTimetable} />
        <IfUserCanRoute permission="users.view" exact path="/users/:id" component={ViewUsers} />
        <IfUserCanRoute permission="users.archive" exact path="/users/:id/archive" component={ArchiveUser} />
        <IfUserCanRoute permission="users.view" exact path="/users/:id/unblock" component={UnblockUser} />
      </Switch>
    </>
  )
}

const UsersRoutes = () => {
  return (
    <div className="tailwind">
      <Switch>
        <IfUserCanRoute permission="users.create" exact path="/users/create" component={CreateUsers} />
        <IfUserCanRoute permission="users.update" exact path="/users/:id/edit" component={EditUsers} />
        <IfUserCanRoute permission="users.view" path="/users" component={UserListRoutes} />
      </Switch>
    </div>
  )
}

export { UsersRoutes }
