import React from 'react'
import { Switch } from 'react-router-dom'
import IfUserCanRoute from '../app/components/common/connected/IfUserCanRoute'
import { ListTicketTopics } from '../pages/ticket-topics/list'
import { CreateTicketTopics } from '../pages/ticket-topics/create'
import { ViewTicketTopics } from '../pages/ticket-topics/view'
import { EditTicketTopics } from '../pages/ticket-topics/edit'
import { ArchiveTicketTopics } from '../pages/ticket-topics/archive'

const TicketTopicsRoutes = () => {
  return (
    <div className="tailwind">
      <IfUserCanRoute permission="ticketTopics.view" path="/ticket-topics" component={ListTicketTopics} />
      <Switch>
        <IfUserCanRoute
          permission="ticketTopics.create"
          exact
          path="/ticket-topics/create"
          component={CreateTicketTopics}
        />
        <IfUserCanRoute
          permission="ticketTopics.archive"
          exact
          path="/ticket-topics/:id/archive"
          component={ArchiveTicketTopics}
        />
        <IfUserCanRoute permission="ticketTopics.view" exact path="/ticket-topics/:id" component={ViewTicketTopics} />
        <IfUserCanRoute
          permission="ticketTopics.update"
          exact
          path="/ticket-topics/:id/edit"
          component={EditTicketTopics}
        />
      </Switch>
    </div>
  )
}

export { TicketTopicsRoutes }
