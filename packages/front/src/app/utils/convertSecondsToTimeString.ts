import moment from 'moment'

const convertSecondsToTimeString = (totalSeconds: number): string => {
  if (totalSeconds <= 0) {
    return '0h 0m 0s'
  }

  const duration = moment.duration(totalSeconds, 'seconds')

  const totalHours = duration.days() * 24 + duration.hours()
  const minutes = duration.minutes()
  const seconds = duration.seconds()

  return (
    (totalHours > 0 ? `${totalHours}h ` : ``) +
    (minutes > 0 ? `${minutes}m ` : ``) +
    (seconds > 0 ? `${seconds}s ` : ``)
  ).trim()
}

export default convertSecondsToTimeString
