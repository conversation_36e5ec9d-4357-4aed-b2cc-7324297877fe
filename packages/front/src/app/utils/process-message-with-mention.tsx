import React, { ReactNode } from 'react'

export function processMessageWithMention(text: string): { modifiedText: string; mentionedList: string[] } {
  const regex = /<(\d+)@c\.us:\[.*?\]>/g
  let matches: Set<string> = new Set()

  const modifiedText = text.replace(regex, (_, number) => {
    matches.add(`${number}@c.us`)
    return `@${number}`
  })

  return { modifiedText, mentionedList: Array.from(matches) }
}

export function replaceMentions(
  message: (
    | string
    | number
    | React.ReactElement<any, string | React.JSXElementConstructor<any>>
    | Iterable<React.ReactNode>
    | React.ReactPortal
  )[] = [],
  mentionedListMessage: { id: string; name: string; numberToRender: string }[] = [],
  contactColor = 'inherit',
  contactFontWeight = 'inherit',
): ReactNode {
  const messageAsArray = Array.isArray(message) ? message : [message]

  try {
    return messageAsArray.reduce<ReactNode[]>((acc, curr) => {
      if (typeof curr !== 'string') {
        acc.push(curr)
        return acc
      }

      const match = curr.match(/@(\d{12,14})/)
      const parts = curr.split(/(@\d{12,14})/g)

      if (match) {
        const nodes = parts.map((part, index) => {
          const contact = mentionedListMessage.find((c) => c.numberToRender === part)
          return contact ? (
            <span style={{ fontWeight: contactFontWeight, color: contactColor }} key={index}>
              @{contact.name ?? contact.numberToRender?.replace('@', '~')}
            </span>
          ) : (
            part
          )
        })

        acc.push(...nodes)
      } else {
        acc.push(curr)
      }

      return acc
    }, [])
  } catch (error) {
    return message
  }
}

export function formatMentionedMessage({
  text = '',
  mentionedList = [],
}: {
  text: string
  mentionedList: { id: string; name: string; numberToRender: string }[]
}): string {
  let formattedText = text

  mentionedList.forEach(({ numberToRender, name }) => {
    const mentionTag = `<${numberToRender.replace('@', '')}@c.us:[${name ?? numberToRender?.replace('@', '~')}]>`
    const regex = new RegExp(numberToRender.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'), 'g')
    formattedText = formattedText.replace(regex, mentionTag)
  })

  return formattedText
}

export function cleanMessageWithSpanMentions(message = [], mentionedListMessage = []) {
  const messageAsArray = Array.isArray(message) ? message : [message]

  try {
    return messageAsArray.reduce((acc, curr) => {
      if (typeof curr !== 'string') {
        acc.push(curr)
        return acc
      }

      const match = curr.match(/@\d{13,}/)
      const parts = curr.split(/(@\d{13,})/g)

      if (match) {
        const nodes = parts.map((part) => {
          const contact = mentionedListMessage.find((c) => c.numberToRender === part)
          return (contact ? `@${contact.name}` : part).replace(/<!-- -->/g, '')
        })

        acc.push(...nodes)
      } else {
        acc.push(curr.replace(/<!-- -->/g, ''))
      }

      return acc
    }, [])
  } catch (error) {
    return message
  }
}
