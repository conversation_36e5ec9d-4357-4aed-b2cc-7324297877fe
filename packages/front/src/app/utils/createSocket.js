import io from 'socket.io-client'
import { select, call } from 'redux-saga/effects'
import config from '../../../config'
import { selectors } from '../modules/auth'
import { selectors as impersonateSelectors } from '../modules/admin/modules/users/modules/impersonate'
import asyncS<PERSON><PERSON> from './asyncSingleton'

export const getOrCreateSocket = async (token, isImpersonating = false, url = config('socketUrl')) =>
  asyncSingleton('getOrCreateSocket', async () => {
    if (process.env.BUILD_FLAG_IS_CLIENT !== 'true') {
      throw new Error('Calling createSocket from node.')
    }

    return new Promise((resolve, reject) => {
      const s = io(`${url}`, {
        query: {
          access_token: token,
          ...(isImpersonating && { impersonate: isImpersonating }),
          client: 'web',
        },
        transports: ['websocket'],
        upgrade: false,
        autoConnect: false,
        pingInterval: 5000,
        pingTimeout: 60000,
      })

      s.on('connect_error', (err) => console.error('socket connect_error', err))
      s.on('connect_timeout', (err) => console.error('socket connect_timeout', err))

      s.once('connect', (res) => {
        resolve(s, res)
      })

      s.on('error', (err) => {
        reject(err)
        console.error('socket error', err)
      })

      s.open()
    })
  })

export const createSocketWithToken =
  (token, isImpersonating = false) =>
  async (namespace, query, url = config('socketUrl')) => {
    const socket = await getOrCreateSocket(token, isImpersonating, url)

    return {
      on: (event, listener) => socket.on(`${namespace}.${event}`, listener),
      off: (event, listener) => socket.off(`${namespace}.${event}`, listener),
      emit: (event, data) => socket.emit(`${namespace}.${event}`, data),
    }
  }

export default function* (...params) {
  const token = yield select(selectors.getAccessToken)
  const isImpersonating = yield select(impersonateSelectors.getIsImpersonating)

  return yield call(createSocketWithToken(token, isImpersonating), ...params)
}
