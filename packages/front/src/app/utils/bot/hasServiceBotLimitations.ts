import { BotLimitations } from '../../types/Bot'

export type BotLimitationCode = 'ERROR_BOT_INTERACTIVE_MESSAGE' | 'ERROR_BOT_WEBCHAT_TRIGGER'

export const hasServiceBotLimitations = (serviceType: string, botLimitations: BotLimitations): BotLimitationCode => {
  if (!serviceType || !botLimitations) {
    return null
  }

  if (botLimitations.hasInteractiveMessage && serviceType !== 'whatsapp-business') {
    return 'ERROR_BOT_INTERACTIVE_MESSAGE'
  }

  if (botLimitations.hasWebchatTrigger && serviceType !== 'webchat') {
    return 'ERROR_BOT_WEBCHAT_TRIGGER'
  }

  return null
}
