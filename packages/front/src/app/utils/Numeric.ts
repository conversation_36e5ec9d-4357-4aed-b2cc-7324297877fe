export class Numeric {
  constructor(
    private number: number,
    private min: number,
    private max: number,
  ) {
    this.number = number
  }

  public isValid(): boolean {
    let isValid = true

    if (!!this.min && this.number < this.min) {
      return (isValid = false)
    }

    if (!!this.max && this.number > this.max) {
      return (isValid = false)
    }

    return isValid
  }
}

export function getNumericErrorMessageKey(min: number, max: number): string {
  if (min && max) {
    return 'KEY_NUMERIC_RANGE'
  }

  if (min) {
    return 'KEY_NUMERIC_ABOVE'
  }

  if (max) {
    return 'KEY_NUMERIC_BELOW'
  }

  return ''
}

export function getNumericWarningMessageKey(min: number, max: number): string {
  if (min && max) {
    return 'KEY_NUMERIC_RANGE_WARNING'
  }

  if (min) {
    return 'KEY_NUMERIC_ABOVE_WARNING'
  }

  if (max) {
    return 'KEY_NUMERIC_BELOW_WARNING'
  }

  return ''
}
