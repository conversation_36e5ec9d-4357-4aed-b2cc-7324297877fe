import currencyOptions from '../components/common/unconnected/maskedInputs/Monetary/currency-options.json'

export class Monetary {
  constructor(
    private value: number,
    private min: number,
    private max: number,
  ) {
    this.value = value
    this.min = min
    this.max = max
  }

  public isValid(): boolean {
    if (!!this.min && this.value < this.min) {
      return false
    }

    if (!!this.max && this.value > this.max) {
      return false
    }

    return true
  }
}

export function getMonetaryErrorMessageKey(min: number, max: number): string {
  if (min && max) {
    return 'KEY_MONETARY_RANGE'
  }

  if (min) {
    return 'KEY_MONETARY_ABOVE'
  }

  if (max) {
    return 'KEY_MONETARY_BELOW'
  }

  return ''
}

export function getMonetaryWarningMessageKey(min: number, max: number): string {
  if (min && max) {
    return 'KEY_MONETARY_RANGE_WARNING'
  }

  if (min) {
    return 'KEY_MONETARY_ABOVE_WARNING'
  }

  if (max) {
    return 'KEY_MONETARY_BELOW_WARNING'
  }

  return ''
}

export function formatMonetaryValue(value: string, currencyCode: string) {
  const currency = currencyOptions.find((option) => option.code === currencyCode) ?? currencyOptions[0]
  const numericValue = parseFloat(value)

  if (isNaN(numericValue)) {
    return ''
  }

  const [integerPart, decimalPart] = numericValue.toFixed(2).split('.')

  const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, currency.thousandSeparator)
  return `${currency.symbol} ${formattedIntegerPart}${currency.decimalSeparator}${decimalPart}`
}
