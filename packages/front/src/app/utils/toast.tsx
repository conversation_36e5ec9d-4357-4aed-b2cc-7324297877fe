import React from 'react'
import { toast } from 'react-toastify'
import { css } from 'glamor'
import { toast as nebulaToast } from '../hooks/useToast'
import Link from '../../app/components/common/unconnected/Link'
import { ToastAction } from '../components/common/unconnected/ui/toast'

const defaultStyle = {
  backgroundColor: 'white',
  color: 'black',
  fontFamily: 'Inter',
}

const NebulaLinkStyle = {
  fontWeight: 'bold',
  textDecoration: 'underline',
  color: 'white',
  cursor: 'pointer',
  backgroundColor: 'transparent',
  border: 'none',
  padding: 0,
  textAlign: 'left',
  marginTop: 8,
}

const Toast = {
  warnWithLink: (message, config, options, [text, path]) => {
    toast.warn(
      <>
        {message}
        <br />
        <Link to={path}>{text}</Link>
      </>,
      {
        ...{
          className: css(defaultStyle),
          progressClassName: css({ backgroundColor: '#FFD646' }),
          icon: false,
        },
        ...options,
      },
      config,
    )
  },

  warn: (message, config) => {
    toast.warn(
      <>{message}</>,
      {
        className: css(defaultStyle),
        progressClassName: css({ backgroundColor: '#FFD646' }),
        icon: false,
      },
      config,
    )
  },

  successWithLink: (message, config, options, [text, path]) => {
    toast.success(
      <>
        {message}
        <br />
        <Link to={path}>{text}</Link>
      </>,
      {
        ...{
          className: css(defaultStyle),
          progressClassName: css({ backgroundColor: '#24CE46' }),
          icon: false,
        },
        ...options,
      },
      config,
    )
  },

  success: (message, config) => {
    toast.success(
      <>{message}</>,
      {
        className: css(defaultStyle),
        progressClassName: css({ backgroundColor: '#24CE46' }),
        icon: false,
      },
      config,
    )
  },

  errorWithLink: (message, config, options, [text, path]) => {
    toast.error(
      <>
        {message}
        <br />
        <Link to={path}>{text}</Link>
      </>,
      {
        ...{
          className: css(defaultStyle),
          progressClassName: css({ backgroundColor: '#FF4651' }),
          icon: false,
        },
        ...options,
      },
      config,
    )
  },

  error: (message, config) => {
    toast.error(
      <>{message}</>,
      {
        className: css(defaultStyle),
        progressClassName: css({ backgroundColor: '#FF4651' }),
        icon: false,
      },
      config,
    )
  },

  info: (message, config) => {
    toast.info(
      <>{message}</>,
      {
        className: css(defaultStyle),
        progressClassName: css({ backgroundColor: '#52658C' }),
        icon: false,
      },
      config,
    )
  },

  nebula: ({
    action: { label, onClick },
    ...rest
  }: Omit<Parameters<typeof nebulaToast>[0], 'action'> & {
    action: {
      label: string
      onClick?: () => void
    }
  }) => {
    nebulaToast({
      ...rest,
      action: (
        <ToastAction altText={label} {...{ onClick }}>
          {label}
        </ToastAction>
      ),
    })
  },

  nebulaWithLink: ({
    action: { label, link, path, variant },
    ...rest
  }: Omit<Parameters<typeof nebulaToast>[0], 'action'> & {
    action: {
      label: string
      link: string
      path: string
      variant?: 'default' | 'destructive' | 'success' | 'warn'
    }
  }) => {
    const { dismiss } = nebulaToast({
      ...rest,
      autoDismiss: false,
      action: (
        <>
          {label}
          <Link onClick={() => dismiss()} style={NebulaLinkStyle} to={path}>
            {link}
          </Link>
        </>
      ),
      variant: variant,
    })
  },
}

export default Object.create(Toast)
