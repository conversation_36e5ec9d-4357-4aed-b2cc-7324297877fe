import { hasPermission } from '../components/common/connected/IfUserCan/IfUserCan'

const checkUserDownloadPermissionByType = (permissions: any, type: string) => {
  if (type === 'video') return hasPermission(permissions, 'download.video')
  if (type === 'image') return hasPermission(permissions, 'download.image')
  if (type === 'audio') return hasPermission(permissions, 'download.audio')
  return true
}

export function checkUserCanDownload(user: any, type: string): boolean {
  return checkUserDownloadPermissionByType(user?.permissions, type) ?? false
}

export function getFileTypeByMimetype(mimeType: string) {
  if (!mimeType) return ''

  const acceptedTypes = ['video', 'image', 'audio']
  const type = mimeType.split('/')[0]

  return acceptedTypes.includes(type) ? type : ''
}
