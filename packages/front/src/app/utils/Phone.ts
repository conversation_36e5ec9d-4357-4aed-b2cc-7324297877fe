export class Phone {
  constructor(
    private phone: string,
    private mask: string,
    private selectedCountryCode: string,
  ) {
    this.phone = phone || ''
    this.mask = mask
    this.selectedCountryCode = selectedCountryCode
  }

  private unmask() {
    return this.phone.replace(/\D/g, '')
  }

  public isValid(): boolean {
    const phone = this.unmask()

    if (phone.length === 0) return true

    if (this.selectedCountryCode === '+55') {
      return phone.length === 10 || phone.length === 11
    }

    return phone.length === this.mask.replace(/[^#]/g, '').length
  }
}
