// @ts-ignore
import formatDate from 'date-fns/format'

import { CustomFieldType } from '../../constants/customFields'
import { Cpf } from '../Cpf'
import { Cnpj } from '../Cnpj'
import { CommonLocaleMessages, CustomField, CustomFieldsLocalMessages } from '../../types/CustomFields'
import { isEmail, isGreaterThan, required as requiredFieldValidation } from '../validator/validators'
import { Hour } from '../hour'
import { HourRange } from '../hourRange'
import countryCodes from '../../components/common/unconnected/maskedInputs/PostalCode/codes.json'
import countryCodesPhone from '../../components/common/unconnected/maskedInputs/Phone/codes.json'
import { PostalCode } from '../PostalCode'
import { Phone } from '../Phone'
import { Monetary, formatMonetaryValue, getMonetaryErrorMessageKey } from '../Monetary'
import { Numeric, getNumericErrorMessageKey } from '../Numeric'
import { t } from 'i18next'

export const EMPTY_CUSTOM_FIELD_VALUE = ''
export const EMPTY_COMPLEX_CUSTOM_FIELD_VALUE = { value: '' }

export enum AvailableFormatRangeTypesEnum {
  DATE_RANGE = CustomFieldType['date-range'],
  HOUR_RANGE = CustomFieldType['hour-range'],
}

export type RangeObject = {
  start: string
  end: string
  rangeType: AvailableFormatRangeTypesEnum
}

const validateObjectReturn = (value: string, defaultValue: string) => {
  try {
    if (typeof value === 'object') {
      return value
    }
    return JSON.parse(value ?? defaultValue)
  } catch {
    return value
  }
}

export const getValueFromCustomFieldValues = (value: string, type: CustomFieldType): any => {
  const defaultParsers: Record<CustomFieldType, any> = {
    [CustomFieldType['date-range']]: '{}',
    [CustomFieldType['hour-range']]: '{}',
    [CustomFieldType.hour]: '""',
    [CustomFieldType['postal-code']]: '{"value": "", "identifier": ""}',
    [CustomFieldType.phone]: '{"value": "", "identifier": ""}',
    [CustomFieldType.monetary]: '{"value": "", "identifier": "", "min": "", "max": ""}',
    [CustomFieldType.numeric]: '{"value": "", "min": "", "max": ""}',
    [CustomFieldType.checkbox]: '[]',
    [CustomFieldType.cpf]: '""',
    [CustomFieldType.cnpj]: '""',
    [CustomFieldType.email]: '""',
    [CustomFieldType.date]: '""',
    [CustomFieldType.list]: '""',
    [CustomFieldType.type]: '""',
    [CustomFieldType.text]: '""',
    [CustomFieldType['long-text']]: '""',
  }

  const defaultValue = defaultParsers[type] || null
  return validateObjectReturn(value, defaultValue)
}

export const formatDateToLocal = (date: Date, local = 'pt-BR') => {
  return new Intl.DateTimeFormat(local).format(date)
}

export const formatRangeObject = ({ start, end, rangeType }: RangeObject) => {
  if (!start || !end) {
    return ''
  }

  if (rangeType === AvailableFormatRangeTypesEnum.DATE_RANGE)
    return `${formatDateToLocal(new Date(start.replace('-', '/')))} - ${formatDateToLocal(new Date(end.replace('-', '/')))}`

  return `${start} - ${end}`
}

export const formatCustomField = (
  value: string,
  type: CustomFieldType,
  settings?: { options: [{ id: string; label: string }] },
) => {
  if (!value) return ''

  const parsedValue = getValueFromCustomFieldValues(value, type)
  if (type === CustomFieldType['date-range'] || type === CustomFieldType['hour-range']) {
    return formatRangeObject({ ...parsedValue, rangeType: type })
  }

  if (type === CustomFieldType.date) {
    return formatDateToLocal(new Date(value.replace('-', '/')))
  }

  if (type === CustomFieldType.cpf) {
    const cpfValue = String(parsedValue)
    return new Cpf(cpfValue).format()
  }

  if (type === CustomFieldType.cnpj) {
    const cnpjValue = String(parsedValue)
    return new Cnpj(cnpjValue).format()
  }

  if (type === CustomFieldType.phone) {
    return `${parsedValue.identifier} ${parsedValue.value}`
  }

  if ([CustomFieldType.monetary, CustomFieldType.numeric, CustomFieldType['postal-code']].includes(type)) {
    return parsedValue.value
  }

  if (type === CustomFieldType.checkbox) {
    const values = parsedValue
      .map((value: string) => {
        const option = settings.options.find((option) => option.id === value)
        return option ? option.label : null
      })
      .filter(Boolean)

    return values.join(', ')
  }

  if (type === CustomFieldType.list) {
    const value = settings.options.find((option) => option.id === parsedValue)
    return value.label
  }

  return parsedValue
}

export type Validation = [(args: { value: any }) => boolean, CustomFieldsLocalMessages & CommonLocaleMessages][]

type CustomFieldsValidations = Record<string, Validation>

export const customFieldKeyPrefix = 'custom_field_'

export function parseCustomFieldValueByTypeFromApi(value: any, type?: CustomFieldType): any {
  const formatFunctions = new Map<CustomFieldType, (value: any) => any>([
    [
      CustomFieldType['date-range'],
      function (dateRange: { start: Date; end: Date } | { from: Date; to: Date }) {
        if (!dateRange) {
          return { from: '', to: '' }
        }
        // @ts-ignore
        let [from, to] = [dateRange.from || dateRange.start, dateRange.to || dateRange.end]

        if (typeof from === 'string') {
          from = new Date(from.replace('-', '/'))
        }

        if (typeof to === 'string') {
          to = new Date(to.replace('-', '/'))
        }

        return { from, to }
      },
    ],
    [
      CustomFieldType.date,
      function (date?: string): Date | string {
        try {
          if (date) return new Date(date.replace('-', '/'))

          return EMPTY_CUSTOM_FIELD_VALUE
        } catch {
          return EMPTY_CUSTOM_FIELD_VALUE
        }
      },
    ],
    [
      CustomFieldType['hour-range'],
      function (hourRange: { start: string; end: string } | string) {
        if (!hourRange) return ''

        if (typeof hourRange === 'string') return hourRange

        const { start, end } = hourRange

        return `${start} - ${end}`
      },
    ],
    [
      CustomFieldType['postal-code'],
      function (value) {
        if (!value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        if (!value.identifier && !value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, identifier: value.identifier }
      },
    ],
    [
      CustomFieldType.phone,
      function (value) {
        if (!value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        if (!value.identifier && !value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, identifier: value.identifier, min: value.min, max: value.max }
      },
    ],
    [
      CustomFieldType.monetary,
      function (value) {
        if (!value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        if (!value.identifier && !value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, identifier: value.identifier, min: value.min, max: value.max }
      },
    ],
    [
      CustomFieldType.numeric,
      function (value) {
        if (!value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        if (!value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, min: value.min, max: value.max }
      },
    ],
  ])

  return formatFunctions.get(type)?.(value) ?? value
}

export function getCustomFieldsInputId(customFields?: CustomField[]): string[] {
  return (customFields || []).map(({ id }) => customFieldKeyPrefix + id)
}

export function fillCustomFieldsFromApi(payload: Record<any, any>): Record<string, any> {
  if (!payload) return {}

  Object.values(payload?.customFields ?? {}).forEach((customField: CustomField) => {
    payload[customFieldKeyPrefix + customField.fieldId] = parseCustomFieldValueByTypeFromApi(
      customField.value,
      customField.type,
    )
  })

  return payload
}

export function parseCustomFieldValueByType(value: any, type: CustomFieldType): any {
  const formatFunctions = new Map<CustomFieldType, (value: any) => any>([
    [
      CustomFieldType['date-range'],
      function (dateRange: { start: Date; end: Date } | { from: Date; to: Date }) {
        // @ts-ignore
        let [start, end] = [dateRange?.from || dateRange?.start, dateRange?.to || dateRange?.end]

        if (!start || !end) {
          return EMPTY_CUSTOM_FIELD_VALUE
        }

        if (typeof start === 'string') {
          start = new Date(start.replace('-', '/'))
        }
        if (typeof end === 'string') {
          end = new Date(end.replace('-', '/'))
        }

        return { start: formatDate(start, 'yyyy-MM-dd'), end: formatDate(end, 'yyyy-MM-dd') }
      },
    ],
    [
      CustomFieldType.date,
      function (date?: Date) {
        const parseToDate = typeof date == 'string' ? new Date(String(date).replace('-', '/')) : date
        if (date) {
          return formatDate(parseToDate, 'yyyy-MM-dd')
        }

        return EMPTY_CUSTOM_FIELD_VALUE
      },
    ],
    [
      CustomFieldType['hour-range'],
      function parseHourRange(hourRange) {
        if (typeof hourRange === 'object') return hourRange

        const regex = /^(\d{2}:\d{2})\s*-\s*(\d{2}:\d{2})$/
        const match = hourRange?.match(regex)

        if (match) {
          return { start: match[1], end: match[2] }
        }
      },
    ],
    [
      CustomFieldType['postal-code'],
      function (value) {
        if (!value.identifier && !value.value) return EMPTY_CUSTOM_FIELD_VALUE

        return { value: value.value, identifier: value.identifier }
      },
    ],
    [
      CustomFieldType.phone,
      function (value) {
        if (!value.identifier && !value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, identifier: value.identifier }
      },
    ],
    [
      CustomFieldType.monetary,
      function (value) {
        if (!/\d/.test(value.value)) {
          return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE
        }

        if (!value.identifier && !value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, identifier: value.identifier, min: value.min, max: value.max }
      },
    ],
    [
      CustomFieldType.numeric,
      function (value) {
        if (typeof value === 'object') {
          if (!value.min && !value.max) return { value: value.value }
        } else {
          if (!value.min && !value.max) return { value: value }
        }

        if (!value.value) return EMPTY_COMPLEX_CUSTOM_FIELD_VALUE

        return { value: value.value, min: value.min || '', max: value.max || '' }
      },
    ],
  ])

  return formatFunctions.get(type)?.(value) ?? value
}

export function handleCustomFieldValuesFormat(customFields: CustomField[]): CustomField[] {
  return customFields.reduce<CustomField[]>((acc, curr) => {
    if (!curr.id) return acc

    const hasValue = Boolean(curr.value)

    if (!curr.required && !hasValue) {
      acc.push({ ...curr, value: EMPTY_CUSTOM_FIELD_VALUE })
      return acc
    }

    acc.push({ ...curr, value: parseCustomFieldValueByType(curr.value, curr.type) })

    return acc
  }, [])
}

export function higienizeCustomFieldToApi(payload: Record<string, any>): Record<string, any> {
  const body = Object.entries(payload).reduce((acc, curr) => {
    const [key, value] = curr

    if (!key.startsWith(customFieldKeyPrefix)) {
      acc[key] = value
    }

    return acc
  }, {})
  return body
}

export function addValidationByType(
  type: CustomFieldType,
  handleTranslation: (
    context: 'common' | 'customFields',
    message: CustomFieldsLocalMessages & CommonLocaleMessages,
  ) => string,
  isRequired?: boolean,
): Validation {
  const validationsMap = new Map<CustomFieldType, Validation>([
    [
      CustomFieldType.cpf,
      [
        [
          ({ value }) => !new Cpf(value).isWrongFormat(isRequired),
          handleTranslation('customFields', 'KEY_CPF_WRONG_FORMAT'),
        ],
        [({ value }) => new Cpf(value).isValid(isRequired), handleTranslation('customFields', 'KEY_CPF_INVALID_CPF')],
      ],
    ],
    [
      CustomFieldType.cnpj,
      [
        [
          ({ value }) => !new Cnpj(value).isWrongFormat(isRequired),
          handleTranslation('customFields', 'KEY_CNPJ_WRONG_FORMAT'),
        ],
        [
          ({ value }) => new Cnpj(value).isValid(isRequired),
          handleTranslation('customFields', 'KEY_CNPJ_INVALID_CNPJ'),
        ],
      ],
    ],
    [
      CustomFieldType.email,
      [
        [
          ({ value = '' }) => (isRequired || value?.length ? isEmail({ value }) : true),
          handleTranslation('customFields', 'KEY_EMAIL_WRONG_FORMAT'),
        ],
      ],
    ],
    [
      CustomFieldType['date-range'],
      [
        [
          ({ value }) => {
            const { from, to } = value || {}

            if (!isRequired && !from && !to) return true

            return isGreaterThan(from)({ value: to })
          },
          handleTranslation('customFields', 'END_DATE_MUST_BE_GREATER_THEN'),
        ],
      ],
    ],
    [
      CustomFieldType.hour,
      [
        [
          ({ value }) => !new Hour(value).isWrongFormat(isRequired),
          handleTranslation('customFields', 'KEY_HOUR_WRONG_FORMAT'),
        ],
        [
          ({ value }) => new Hour(value).isValid(isRequired),
          handleTranslation('customFields', 'KEY_HOUR_INVALID_HOUR'),
        ],
      ],
    ],
    [
      CustomFieldType['hour-range'],
      [
        [
          ({ value }) => !new HourRange(value).isWrongFormat(isRequired),
          handleTranslation('customFields', 'KEY_HOUR_RANGE_WRONG_FORMAT'),
        ],
        [
          ({ value }) => new HourRange(value).isValid(isRequired),
          handleTranslation('customFields', 'KEY_HOUR_RANGE_INVALID_HOUR_RANGE'),
        ],
      ],
    ],
    [
      CustomFieldType['postal-code'],
      [
        [
          ({ value: customFieldValue }) => {
            const { value, identifier } = customFieldValue || {}

            if (!isRequired && !value && !identifier) return true

            let mask = countryCodes.find((country) => country.code === identifier)?.mask

            return new PostalCode(value, mask).isValid()
          },
          handleTranslation('customFields', 'KEY_INVALID_POSTAL_CODE'),
        ],
      ],
    ],
    [
      CustomFieldType.phone,
      [
        [
          ({ value: customFieldValue }) => {
            const { value, identifier } = customFieldValue || {}

            if (!isRequired && !value && !identifier) return true

            const mask = countryCodesPhone.find((country) => country.code === identifier)?.mask

            return new Phone(value, mask, identifier).isValid()
          },
          handleTranslation('customFields', 'KEY_INVALID_PHONE_NUMBER'),
        ],
      ],
    ],
    [
      CustomFieldType.monetary,
      [
        [
          ({ value: customFieldValue }) => {
            const { value, min, max } = customFieldValue || {}

            if (!isRequired && !value) return true
            else if (!value) return false

            const currencyValue = parseFloat(value.replace(/[^\d.,]/g, ''))
            return new Monetary(currencyValue, min, max).isValid()
          },
          ({ value: customFieldValue }) => {
            const { identifier, min, max } = customFieldValue || {}
            return t(`customFields:${getMonetaryErrorMessageKey(min, max)}`, {
              min: formatMonetaryValue(min, identifier),
              max: formatMonetaryValue(max, identifier),
              symbol: '',
            })
          },
        ],
      ],
    ],
    [
      CustomFieldType.numeric,
      [
        [
          ({ value: customFieldValue }) => {
            const { value, min, max } = customFieldValue || {}

            if (!isRequired && !value) return true
            else if (!value) return false

            const numericValue = parseFloat(value.replace(/[^\d.,]/g, ''))
            return new Numeric(numericValue, min, max).isValid()
          },
          ({ value: customFieldValue }) => {
            const { min, max } = customFieldValue || {}
            return t(`customFields:${getNumericErrorMessageKey(min, max)}`, { min, max })
          },
        ],
      ],
    ],
  ])

  return validationsMap.get(type) || ([] as Validation)
}

export const requiredFieldValidationObject = (
  type: CustomFieldType,
  translation: (
    context: 'common' | 'customFields',
    message: CustomFieldsLocalMessages | CommonLocaleMessages,
  ) => string,
): Validation[number] => [
  ({ value }) => {
    if (
      [
        CustomFieldType['postal-code'],
        CustomFieldType.phone,
        CustomFieldType.numeric,
        CustomFieldType.monetary,
      ].includes(type)
    ) {
      return value && value.value
    }
    if (type === CustomFieldType.checkbox) {
      let parsedValue = value
      if (typeof value === 'string' && !!value) {
        try {
          parsedValue = JSON.parse(parsedValue)
        } catch (e) {
          parsedValue = []
        }
      }
      return !!parsedValue && parsedValue.length > 0
    }
    if (type === CustomFieldType['date-range']) {
      const { from, to } = value || {}

      return from && to
    }
    return requiredFieldValidation({ value })
  },
  translation('common', 'REQUIRED_FIELD'),
]

export function customFieldsValidations(
  customFields: CustomField[],
  translation: (
    context: 'common' | 'customFields',
    message: CustomFieldsLocalMessages | CommonLocaleMessages,
  ) => string,
): CustomFieldsValidations {
  return customFields.reduce<CustomFieldsValidations>((acc, curr) => {
    const { type, required, id } = curr

    const customFieldKey = `${customFieldKeyPrefix}${id}`
    acc[customFieldKey] = []

    if (required) {
      acc[customFieldKey].push(requiredFieldValidationObject(type, translation))
    }

    acc[customFieldKey].push(...addValidationByType(type, translation, required))

    return acc
  }, {})
}

export function unformatByType(value: any, type: CustomFieldType) {
  const unformatFunctionsMap = new Map<CustomFieldType, Function>([
    [
      CustomFieldType.cpf,
      () => {
        return new Cpf(value).unformat()
      },
    ],
    [
      CustomFieldType.cnpj,
      () => {
        return new Cnpj(value).unformat()
      },
    ],
  ])

  return unformatFunctionsMap.get(type)?.(value) ?? value
}
