import signaturesFiles from '../../data/signatureFiles'
import readFileAsDataURL from '../readFileAsDataURL'

const getExtensionFromFilename = (name) => {
  const parts = name.split('.')
  return parts.length <= 1 ? null : parts.pop()
}

const convertBase64ToHex = (base64) =>
  window
    .atob(base64)
    .split('')
    .map((aChar) => `0${aChar.charCodeAt(0).toString(16)}`.slice(-2))
    .join('')
    .toUpperCase()

const getMetaFromSignatureFile = async (blob, extension) => {
  const base64 = (await readFileAsDataURL(blob)).split('base64,')[1]

  const hexSignature = convertBase64ToHex(base64)

  return (
    signaturesFiles.find(
      (item) =>
        ((extension && item.extension === extension) || (!extension && item.hexadecimalBytes)) &&
        hexSignature.includes(item.hexadecimalBytes),
    ) || {}
  )
}

export async function checkFileProperties(file) {
  const extension = getExtensionFromFilename(file.blob.name)
  const metafile = !extension ? await getMetaFromSignatureFile(file.blob, extension) : {}

  let mimetype = file.blob.type || metafile.mimetype || 'application/octet-stream'

  // Treat image/svg+xml, video/webm as a file
  if (mimetype === 'image/svg+xml' || mimetype === 'video/webm') {
    mimetype = 'application/octet-stream'
  }

  return {
    ...file,
    name:
      (!extension && file.blob.name.replace(new RegExp(`(.${extension}})$`), `.${metafile.extension || extension}`)) ||
      file.blob.name,
    mimetype,
  }
}

export default checkFileProperties
