import validator from 'validator'
import zxcvbnHelper from '../zxcvbnHelper'

export const acceptBlank =
  (rule) =>
  ({ value, ...rest }) =>
    value === undefined || value === '' || value === null || rule({ value, ...rest })

export const required = ({ value }) => !!value

export const isUrl = ({ value }) => validator.isURL(value)

export const isEmail = ({ value }) => validator.isEmail(value)

export const string = ({ value }) => typeof value === 'string'

export const number = ({ value }) => typeof value === 'number'

export const boolean = ({ value }) => typeof value === 'boolean'

export const regex =
  (reg, callback) =>
  ({ value }) =>
    callback ? callback(new RegExp(reg).test(value)) : new RegExp(reg).test(value)

export const uuid4 = regex(/^[0-9A-F]{8}-[0-9A-F]{4}-[4][0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i)

export const checkTime = ({ value }) => {
  const date = new Date(value)
  const currentDate = Date.now()

  return date.getTime() - currentDate > 0
}

export const isGreaterThan =
  (size) =>
  ({ value }) =>
    value > size

export const isLesserThan =
  (size) =>
  ({ value }) =>
    value < size

export const isGreaterThanOrEqual =
  (size) =>
  ({ value }) =>
    value >= size

export const isLesserThanOrEqual =
  (size) =>
  ({ value }) =>
    value <= size

export const between =
  (size1, size2) =>
  ({ value }) =>
    value > size1 && value < size2

export const betweenOrEqual =
  (size1, size2) =>
  ({ value }) =>
    value >= size1 && value <= size2

export const hasLengthGreaterThan =
  (size) =>
  ({ value, ...rest }) =>
    isGreaterThan(size)({ value: String(value).length, ...rest })

export const hasLengthGreaterThanOrEqual =
  (size) =>
  ({ value, ...rest }) =>
    isGreaterThanOrEqual(size)({ value: String(value).length, ...rest })

export const hasLengthLesserThan =
  (size) =>
  ({ value, ...rest }) =>
    isLesserThan(size)({ value: String(value).length, ...rest })

export const hasLengthLesserThanOrEqual =
  (size) =>
  ({ value, ...rest }) =>
    isLesserThanOrEqual(size)({ value: String(value).length, ...rest })

export const sameAs =
  (key) =>
  ({ value, inputData }) =>
    value === inputData[key]

export const hasCapitalLetter = ({ value }) => /[A-Z]/.test(value)

export const requiredIfOtherPresent = (otherKey) => (props) => {
  const { inputData } = props
  return otherKey in inputData[otherKey] ? required(props) : true
}

export const requiredIfOtherNotPresent = (otherKey) => (props) => {
  const { inputData } = props
  return otherKey in inputData ? true : required(props)
}

export const requiredIfOtherNotValid = (otherKey) => (props) => {
  const { inputData, value } = props
  return inputData[otherKey] ? true : value.length
}

export const arrayWithoutBlanks = ({ value }) => {
  const hasFalsy = (arr) => {
    for (const item of arr) {
      if (!item) return true
    }

    return false
  }

  return Array.isArray(value) && !hasFalsy(value)
}

export const arrayOf =
  (typeOrValidator) =>
  ({ value }) =>
    Array.isArray(value) &&
    value.length &&
    value.every((item) => {
      if (typeof typeOrValidator === 'string') {
        return typeof item === typeOrValidator
      }

      if (typeof typeOrValidator === 'object') {
        return typeof item === typeOrValidator
      }

      if (typeof typeOrValidator === 'function') {
        return typeOrValidator({ value: item })
      }

      return false
    })

export const requiredIf = (fn) => (props) => {
  const { inputData } = props

  const res = fn(inputData)
  return res ? required(props) : true
}

export const hasLowerCaseCharacter = () => regex(/[a-z]/)
export const hasUpperCaseCharacter = () => regex(/[A-Z]/)
export const hasDigitCharacter = () => regex(/[0-9]/)
export const hasSpecialCharacter = () => regex(/[-#!$@£%^&*()_+|~=`{}[\]:";'<>?,./ ]/)
export const containKnownPatternsInZxcvbnResult =
  (patternNames, result) =>
  ({ value }) => {
    if (!value) return false

    return zxcvbnHelper.containSomePatternInResult(patternNames, result || zxcvbnHelper.getPasswordStrength(value))
  }
