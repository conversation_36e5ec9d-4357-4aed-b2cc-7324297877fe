import { OAuthAccessToken } from '../types/OAuthAccessToken'

export class ApiToken {
  private token: OAuthAccessToken

  constructor(token: OAuthAccessToken) {
    this.token = token
  }

  public get id(): string {
    return this.token.id
  }

  public get name(): string {
    return this.token.name
  }

  public get accessToken(): string {
    return this.token.accessToken
  }

  public get accessTokenExpiresAt(): Date {
    return this.token.accessTokenExpiresAt
  }

  public get status(): string {
    return this.token.status
  }

  public expiresInDaysBefore(): { willExpireSoon: true; days: number } | { willExpireSoon: false } {
    if (this.token.status === 'active') {
      const now = new Date()
      const diffTime = new Date(this.token.accessTokenExpiresAt).getTime() - now.getTime()
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (diffDays <= 5 && diffDays > 0) {
        return { willExpireSoon: true, days: diffDays }
      }
    }

    return { willExpireSoon: false }
  }
}
