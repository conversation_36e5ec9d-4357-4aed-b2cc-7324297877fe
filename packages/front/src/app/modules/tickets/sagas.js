import { call, cancelled, fork, put, select, take, takeLatest, delay } from 'redux-saga/effects'
import omitBy from 'lodash/omitBy'
import { actions as entitiesActions } from '../entities'
import { actions as appActions } from '../app'
import { actions as messageActions } from '../messages'
import { actions as servicesAllActions } from '../services/modules/all'
import { normalizeList } from '../../resources/ticket/schema'
import * as actions from './actions'
import createSocket from '../../utils/createSocket'
import socketToChannel, { CREATED, DESTROYED, UPDATED, TRANSFER, CLOSED } from '../../utils/socketToChannel'
import { addAccessTokenToUrl } from '../app/sagas'
import { handleNotification } from '../messages/sagas'
import { selectors as contactsSelectors } from '../contacts'
import { selectors as authSelectors } from '../auth'
import { selectors as chatSelectors } from '../chat'
import * as actionsApp from '../app/actions'

const parseTicketFromServer = async (ticket) => ticket
const parseTicketsFromServer = (tickets) => Promise.all(tickets.map(parseTicketFromServer))

// ------------------------------------
// Sub-routines
// ------------------------------------

function* setEntities(entities) {
  yield put(entitiesActions.transactionSet({ entities, includes: { tickets: { user: false, department: false } } }))
}

function* waitForContact(contactId) {
  for (let i = 0; i < 10; i++) {
    const contact = yield select((state) => state.entities.contacts[contactId])
    if (contact) return contact
    yield delay(100)
  }
  return null
}

function* handleTicketNotification(event, data, user) {
  if (
    (!user.account.settings.ticketTransferNotification && !user.account.settings.ticketOpenNotification) ||
    (event !== TRANSFER && event !== CREATED)
  ) {
    return
  }

  if (data.userId && data.userId === user.id) {
    let contact = yield select((state) => state.entities.contacts[data.contactId])
    if (!contact) contact = yield call(waitForContact, data.contactId)

    if (contact) {
      const icon = yield call(addAccessTokenToUrl, contact.avatarUrl)

      yield put(
        actionsApp.showNotification({
          title: contact.name,
          body: `Chamado transferido para ${user.name}`,
          icon,
          onClickAction: () => {},
        }),
      )
    }
    return
  }

  if (!data.userId && user.departments.map((department) => department.id).includes(data.departmentId)) {
    let contact = yield select((state) => state.entities.contacts[data.contactId])
    if (!contact) contact = yield call(waitForContact, data.contactId)

    if (contact) {
      const icon = yield call(addAccessTokenToUrl, contact.avatarUrl)
      yield put(
        actionsApp.showNotification({
          title: contact.name,
          body: 'Chamado transferido para fila de atendimento',
          icon,
          onClickAction: () => {},
        }),
      )
    }
  }

  if (Array.isArray(data)) {
    if (user.departments.map((department) => department.id).includes(data[0].departmentId)) {
      yield put(
        actionsApp.showNotification({
          title: 'Transferência em massa',
          body: 'Chamados transferidos para fila de atendimento',
          icon: '',
          onClickAction: () => {},
        }),
      )
      return
    }

    if (data[0].userId && data[0].userId === user.id) {
      yield put(
        actionsApp.showNotification({
          title: 'Transferência em massa',
          body: `Chamados transferidos para ${user.name}`,
          icon,
          onClickAction: () => {},
        }),
      )
      return
    }
  }
}

function* handleTicketMessageRemoval(event, data, currentContactId, user) {
  if ([TRANSFER, UPDATED].includes(event) && currentContactId !== data.contactId && data.userId !== user.id) {
    const messages = yield select((state) => state.entities.messages)
    const updatedMessages = omitBy(messages, (message) => message.contactId !== data.contactId)
    const paths = Object.values(updatedMessages).map((message) => `messages.${message.id}`)
    yield put(entitiesActions.unset(paths))
  }
}

function* handleTicketEvents(event, data, currentContactId) {
  const eventData = Array.isArray(data) ? data : [data]

  if ([CREATED, UPDATED, TRANSFER, CLOSED].includes(event)) {
    const tickets = yield call(parseTicketsFromServer, eventData)
    const normalized = yield call(normalizeList, tickets)
    yield setEntities(normalized.entities)
    yield put(actions.receivedMany({ tickets, normalized, event }))
    if ([TRANSFER, UPDATED].includes(event)) {
      if (currentContactId === eventData[0].contactId) {
        yield put(messageActions.fetch(...eventData))
      }
    }

    return
  }
  if (event === DESTROYED) {
    const paths = data.map((ticket) => `tickets.${ticket.id}`)
    yield put(entitiesActions.unset(paths))
    yield put(actions.destroyedMany({ tickets: data }))
  }
}

export function* handleEvent(event, data) {
  const user = yield select(authSelectors.getUser)
  const currentContactId = yield select(chatSelectors.getCurrentContactId)

  yield handleTicketNotification(event, data, user)
  yield handleTicketMessageRemoval(event, data, currentContactId, user)
  yield handleTicketEvents(event, data, currentContactId)
}

function* handleSocketEvents(channel) {
  try {
    while (true) {
      const payload = yield take(channel)
      const { event, data } = payload

      yield fork(handleEvent, event, data)
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

function* listenSocketSaga() {
  const socket = yield call(createSocket, 'ticket')
  const channel = yield call(socketToChannel, socket)

  yield fork(handleSocketEvents, channel)
}

export function* handleNotificationSaga(actions, many = false) {
  const { payload } = actions

  const ticket = many ? payload : payload.ticket

  const user = yield select(authSelectors.getUser)

  if (payload.event !== CREATED && payload.event !== TRANSFER) return

  const contact = yield select(contactsSelectors.getById, ticket.contactId)

  const matchingDepartment = user.departments.filter((department) => department.id === ticket.departmentId)

  if (user.account.settings.ticketOpenNotification && matchingDepartment.length !== 0) {
    const contactName = contact ? contact.name : 'Novo atendimento'
    const contactAvatar = contact ? yield call(addAccessTokenToUrl, contact.avatarUrl) : ''
    const departmentName = matchingDepartment[0].name
    const message = `Novo chamado${contact ? ` de ${contactName}` : ''} no departamento ${departmentName}`

    yield put(
      appActions.showNotification({
        title: contactName,
        body: message,
        icon: contactAvatar,
        onClickAction: messageActions.notificationClick({
          contactId: ticket.contactId,
        }),
      }),
    )
  }
}

export function manyHandleNotificationSaga(actions) {
  const { payload } = actions

  if (payload.event !== CREATED) return

  payload.tickets.map((ticket) => handleNotification(ticket, true))
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    yield takeLatest(servicesAllActions.fetchAllSuccess, listenSocketSaga)
  }

  yield takeLatest(actions.receivedOne, handleNotificationSaga)
  yield takeLatest(actions.receivedMany, manyHandleNotificationSaga)
}
