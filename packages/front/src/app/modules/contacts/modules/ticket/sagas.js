import identity from 'lodash/identity'
import pick from 'lodash/pick'
import pickBy from 'lodash/pickBy'
import map from 'lodash/map'
import { call, put, select, takeLatest } from 'redux-saga/effects'
import { normalizeList } from '../../../../resources/contact/schema'
import contactsApi from '../../../../resources/contact/apiAuthedSaga'
import { selectors as authSelectors } from '../../../auth'
import * as actions from './actions'

export const formatToApi = (contact) => ({
  ...pickBy(
    pick(contact, [
      'id',
      'name',
      'number',
      'mergeRelations',
      'unsubscribed',
      'currentDepartment',
      'currentDepartmentId',
    ]),
    identity,
  ),
  serviceId: contact.service.id,
  tagIds: map(contact.tags, 'id'),
})

export function* transferTicketSaga(action) {
  try {
    yield put(actions.transferTicketStarted())

    const { contact } = action.payload
    const { currentTicket } = contact

    const byUserId = yield select(authSelectors.getUserId)

    const data = {
      departmentId: currentTicket.department.id,
      userId: currentTicket.user ? currentTicket.user.id : null,
      comments: currentTicket?.transferComment ? currentTicket.transferComment : '',
      byUserId,
    }

    const serverContact = yield call(contactsApi.transferTicket, contact.id, data)

    const contacts = [serverContact]

    const normalized = normalizeList(contacts)

    yield put(actions.transferTicketSuccess({ contacts, normalized }))
  } catch (error) {
    console.log('error', error)
    yield put(actions.transferTicketFailed({ error, action }))
  }
}

export function* transferTicketsSaga(action) {
  try {
    yield put(actions.transferTicketStarted())

    const { currentTicket, ticketsSelectedId, allContactsSelected, query } = action.payload

    const byUserId = yield select(authSelectors.getUserId)
    const comments = currentTicket?.transferComment ? currentTicket.transferComment : ''

    const data = {
      departmentId: currentTicket?.department ? currentTicket?.department?.id : null,
      userId: currentTicket?.user ? currentTicket?.user?.id : null,
      comments,
      byUserId,
      ticketsSelectedId,
      allContactsSelected,
      query,
    }

    const serverContact = yield call(contactsApi.transferTickets, data)
    const contacts = [serverContact]
    const normalized = normalizeList(contacts)

    yield put(actions.transferTicketSuccess({ contacts, normalized }))
  } catch (error) {
    console.log('error', error)
    yield put(actions.transferTicketFailed({ error, action }))
  }
}

export function* closeTicketSaga(action) {
  try {
    yield put(actions.closeTicketStarted())

    const { id, comments, ticketTopicIds, aiSummaryRating } = action.payload

    const byUserId = yield select(authSelectors.getUserId)

    yield call(contactsApi.closeTicket, id, {
      byUserId,
      comments,
      ticketTopicIds,
      aiSummaryRating,
    })

    yield put(actions.closeTicketSuccess({ contactId: id }))
  } catch (error) {
    yield put(actions.closeTicketFailed({ error, action }))
  }
}

export function* updateTicketSaga(action) {
  try {
    yield put(actions.updateTicketStarted())

    const { contact } = action.payload
    const { currentTicket } = contact

    const data = {
      departmentId: currentTicket.department.id,
      userId: currentTicket.user ? currentTicket.user.id : null,
      comments: currentTicket.comments || '',
    }

    const serverContact = yield call(contactsApi.updateTicket, contact.id, data)

    const contacts = [serverContact]

    const normalized = normalizeList(contacts)

    yield put(actions.updateTicketSuccess({ contacts, normalized }))
  } catch (error) {
    console.log('error', error)
    yield put(actions.updateTicketFailed({ error, action }))
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.transferTicket, transferTicketSaga)
  yield takeLatest(actions.transferTickets, transferTicketsSaga)
  yield takeLatest(actions.closeTicket, closeTicketSaga)
  yield takeLatest(actions.updateTicket, updateTicketSaga)
}
