import { injectSaga } from '../../store/hooks'
import * as actions from './actions'
import * as constants from './constants'
import sagas from './sagas'
import * as selectors from './selectors'
import injectShow from './modules/show'
import injectCard from './card'

export { actions, constants, sagas, selectors }

export default (store) => {
  injectSaga(store)(sagas)
  injectShow(store)
  injectCard(store)
}
