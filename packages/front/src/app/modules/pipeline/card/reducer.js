import { createReducer } from 'redux-act-light'
import parseAxiosError from '../../../utils/parseAxiosError'
import { MODULE_NAME } from './constants'
import * as actions from './actions'
import uniq from '../../../utils/arrays/uniq'

export const KEY = MODULE_NAME

// ------------------------------------
// Reducer
// ------------------------------------

const initialState = {
  oportunities: {},
}

export default createReducer(
  {
    [actions.fetchStarted]: (state, { payload }) => ({
      ...state,
      oportunities: {
        ...state.oportunities,
        [payload.id]: {
          ...state.oportunities[payload.id],
          isLoading: true,
          error: null,
        },
      },
    }),

    [actions.fetchFailed]: (state, { payload }) => ({
      ...state,
      oportunities: {
        ...state.oportunities,
        [payload.id]: {
          ...state.oportunities[payload.id],
          isLoading: false,
          error: parseAxiosError(payload.error),
        },
      },
    }),

    [actions.fetchSuccess]: (state, { payload }) => ({
      ...state,
      oportunities: {
        ...state.oportunities,
        [payload.id]: {
          ...(state.oportunities[payload.id] || {}),
          isLoading: false,
          error: null,
        },
      },
    }),
  },
  initialState,
)
