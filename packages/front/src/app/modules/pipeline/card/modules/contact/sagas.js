import { all, call, cancelled, put, take, fork, takeEvery, takeLatest } from 'redux-saga/effects'
import { actions as entitiesActions } from '../../../../entities'
import { normalizeList } from '../../../../../resources/pipelines/card/schema'
import { actions as messagesActions } from '../../../../messages'
import pipelineApi from '../../../../../resources/pipelines/apiAuthedSaga'
import { parseCardFromServer } from '../../../sagas'
import * as actions from './actions'
import { reportErrorSaga } from '../../../../app/sagas'
import * as chatModule from '../../../../chat'
import * as authModule from '../../../../auth'
import { hasPermission } from '../../../../../components/common/connected/IfUserCan/IfUserCan'
import createSocket from '../../../../../utils/createSocket'
import socketToChannel, { CREATED, defaultEvents, DESTROYED, UPDATED } from '../../../../../utils/socketToChannel'
import { actions as appActions } from '../../../../app'
import { transactionSet } from '../../../../entities/actions'

// ------------------------------------
// Sub-routines
// ------------------------------------
export function* fetchAllSaga(action) {
  const { contactId } = action.payload

  if (!contactId) return

  try {
    yield put(actions.fetchAllStarted({ contactId }))

    const query = {
      attributes: [
        'id',
        'title',
        'organization',
        'organizationSegment',
        'description',
        'order',
        'pipelineStageId',
        'pipelineId',
        'contactId',
        'statusId',
        'createdAt',
        'finishedAt',
        'originChannel',
        'originCampaign',
        'ownerId',
      ],
      where: {
        contactId,
        isArchived: false,
      },
      include: [
        'stage_reason',
        'stage_status',
        'stage_reason',
        'owner',
        'products',
        'comments',
        {
          model: 'contact',
          include: ['avatar'],
        },
        {
          model: 'pipeline_stage',
          include: ['statuses'],
        },
        {
          model: 'pipeline',
          include: [
            {
              model: 'stages',
              include: ['statuses', 'reasons'],
            },
          ],
          required: true,
        },
        {
          model: 'movements',
          include: [
            {
              model: 'user',
              paranoid: false,
            },
            {
              model: 'from_pipeline_stage',
              paranoid: false,
            },
            {
              model: 'to_pipeline_stage',
              paranoid: false,
            },
            {
              model: 'from_stage_status',
              paranoid: false,
            },
            {
              model: 'to_stage_status',
              paranoid: false,
            },
            {
              model: 'from_owner',
              paranoid: false,
            },
            {
              model: 'to_owner',
              paranoid: false,
            },
          ],
          order: [['createdAt', 'DESC']],
        },
      ],
      order: [['finishedAt', 'DESC']],
      paginate: false,
    }

    const response = yield call(pipelineApi.getCards, query)

    const normalized = normalizeList(response)
    if (response.length) {
      yield put(
        entitiesActions.set({
          entities: normalized.entities,
        }),
      )
    }

    yield put(
      actions.fetchAllSuccess({
        contactId,
        data: response,
        normalized,
      }),
    )
  } catch (error) {
    yield put(actions.fetchAllFailed({ contactId, error }))
  }
}

function* handleCreatedOrUpdated(data) {
  const normalized = yield call(normalizeList, data)

  yield put(actions.receivedMany({ data, normalized }))
}

function* handleEvent(event, data, user) {
  return yield call(handleCreatedOrUpdated, data)
}

function* handleSocketEvents(channel, user) {
  try {
    while (true) {
      const payload = yield take(channel)
      const { event, data } = payload

      yield fork(handleEvent, event, data, user)
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

function* listenSocketSaga() {
  const socket = yield call(createSocket, 'card')
  const channel = yield call(socketToChannel, socket)

  yield fork(handleSocketEvents, channel)
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.fetchAll, fetchAllSaga)
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    yield takeLatest(appActions.ready, listenSocketSaga)
  }
  yield takeEvery(actions.fetchAllFailed, reportErrorSaga)
}
