import { createSelector } from 'reselect'
import intersectionBy from 'lodash/intersectionBy'
import orderBy from 'lodash/orderBy'
import { getEntities } from '../../../../entities/selectors'
import { denormalizeList } from '../../../../../resources/pipelines/card/schema'
import { KEY } from './reducer'

export const getStateSlice = (state) => state[KEY] || {}

const getCardIds = (state) => getStateSlice(state).cardIds

export const getCards = createSelector([getEntities, getCardIds], (entities, cardIds) =>
  denormalizeList(cardIds, entities).filter(Boolean),
)
