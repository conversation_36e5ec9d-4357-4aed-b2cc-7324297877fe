import { uniqBy, groupBy } from 'lodash'
import { createReducer } from 'redux-act-light'
import parseAxiosError from '../../../../../utils/parseAxiosError'
import * as actions from './actions'
import { SUB_MODULE_NAME } from './constants'
import { MODULE_NAME } from '../../constants'
import { object } from 'prop-types'

export const KEY = `${MODULE_NAME}.${SUB_MODULE_NAME}`

// ------------------------------------
// Reducer
// ------------------------------------

const initialState = {}

export default createReducer(
  {
    [actions.fetchAllStarted]: (state, { payload }) => ({
      ...state,
      [payload?.contactId]: {
        cards: state?.[payload?.contactId]?.cards ?? [],
        isLoading: true,
        error: null,
      },
    }),

    [actions.fetchAllFailed]: (state, { payload }) => ({
      ...state,
      [payload?.contactId]: {
        cards: state?.[payload?.contactId]?.cards ?? [],
        isLoading: false,
        error: parseAxiosError(payload.error),
      },
    }),

    [actions.fetchAllSuccess]: (state, { payload }) => {
      return {
        ...state,
        ...groupBy(uniqBy(payload?.data, 'id'), 'contactId'),
      }
    },

    [actions.receivedMany]: (state, { payload }) => {
      let cards = []
      if (Array.isArray(state?.[payload?.data?.contactId])) {
        cards = state?.[payload?.data?.contactId]?.filter((card) => card?.id !== payload?.data?.id)
      }

      if (!payload?.data?.isArchived) {
        cards.push(payload?.data)
      }

      return {
        ...state,
        [payload?.data?.contactId]: uniqBy(cards, 'id'),
      }
    },
  },

  initialState,
)
