import { uniqBy } from 'lodash'
import { createReducer } from 'redux-act-light'
import parseAxiosError from '../../../../../utils/parseAxiosError'
import * as actions from './actions'
import * as pipelineActions from '../../../actions'
import { SUB_MODULE_NAME } from './constants'
import { MODULE_NAME } from '../../constants'

export const KEY = `${MODULE_NAME}.${SUB_MODULE_NAME}`

// ------------------------------------
// Reducer
// ------------------------------------

const initialState = {}

export default createReducer(
  {
    [actions.fetchMoreStarted]: (state, { payload }) => ({
      ...state,
      [payload?.stageId]: {
        cards: state?.[payload?.stageId]?.cards ?? [],
        isLoading: true,
        error: null,
      },
    }),

    [actions.fetchMoreFailed]: (state, { payload }) => ({
      ...state,
      [payload?.stageId]: {
        cards: state?.[payload?.stageId]?.cards ?? [],
        isLoading: false,
        error: parseAxiosError(payload.error),
      },
    }),

    [actions.fetchMoreSuccess]: (state, { payload }) => {
      const { data } = payload

      const getCards = () => {
        if (payload?.refresh) return data

        const { cards } = state?.[payload?.stageId]

        data?.forEach((card) => {
          cards.push(card)
        })

        return uniqBy(cards, 'id')
      }

      return {
        ...state,
        [payload?.stageId]: {
          cards: getCards(),
          isLoading: false,
          error: null,
          total: (state?.[payload.stageId]?.total ?? 0) + (payload?.length ?? 0),
        },
      }
    },

    [actions.receivedMany]: (state, { payload }) => {
      if (!payload?.data || !state?.[payload?.data?.pipelineStageId]) return state

      let stageIdOld = null
      let cardIndex = null

      Object.keys(state).forEach((stageIndex) => {
        state?.[stageIndex]?.cards?.forEach((card, index) => {
          if (card?.id === payload.data.id) {
            stageIdOld = card?.pipelineStageId
            cardIndex = index
          }
        })
      })

      if (cardIndex > -1) {
        if (stageIdOld === payload.data.pipelineStageId) {
          const cards = state?.[payload.data.pipelineStageId]?.cards?.filter((_, i) => i !== cardIndex)

          if (!payload.data.isArchived) {
            cards.push(payload?.data)
          }

          return {
            ...state,
            [stageIdOld]: {
              cards: uniqBy(cards, 'id'),
              isLoading: false,
              error: null,
            },
          }
        }

        const cardsNewStage = state?.[payload.data.pipelineStageId]?.cards?.filter(
          (card) => card?.id !== payload.data.id,
        )

        if (!payload.data.isArchived) {
          cardsNewStage.push(payload?.data)
        }

        const cardsOldStage = state?.[stageIdOld]?.cards?.filter((_, i) => i !== cardIndex)

        return {
          ...state,
          [stageIdOld]: {
            cards: uniqBy(cardsOldStage, 'id'),
            isLoading: false,
            error: null,
          },
          [payload.data.pipelineStageId]: {
            cards: uniqBy(cardsNewStage, 'id'),
            isLoading: false,
            error: null,
          },
        }
      }

      return state
    },
  },

  initialState,
)
