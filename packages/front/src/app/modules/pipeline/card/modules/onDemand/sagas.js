import { all, call, cancelled, put, take, fork, takeEvery, takeLatest, select } from 'redux-saga/effects'
import moment from 'moment'
import { actions as entitiesActions } from '../../../../entities'
import { normalizeList } from '../../../../../resources/pipelines/card/schema'
import { actions as messagesActions } from '../../../../messages'
import pipelineApi from '../../../../../resources/pipelines/apiAuthedSaga'
import { parseCardFromServer } from '../../../sagas'
import * as actions from './actions'
import * as actionsPipeline from '../../../modules/show/actions'
import { reportErrorSaga } from '../../../../app/sagas'
import * as chatModule from '../../../../chat'
import * as authModule from '../../../../auth'
import { hasPermission } from '../../../../../components/common/connected/IfUserCan/IfUserCan'
import createSocket from '../../../../../utils/createSocket'
import socketToChannel, { CREATED, defaultEvents, DESTROYED, UPDATED } from '../../../../../utils/socketToChannel'
import { actions as appActions } from '../../../../app'
import { transactionSet } from '../../../../entities/actions'

export function* fetchMoreSaga(action) {
  const { limit = 10, refresh = false, pipelineId, stageId, orderBy = [['order', 'asc']], filters } = action.payload

  try {
    yield put(actions.fetchMoreStarted({ limit, refresh, pipelineId, stageId, orderBy }))

    let existingCardIds = []
    if (!refresh) {
      const state = yield select()
      const onDemandState = state['oportunities.stages'] || {}
      const existingCards = onDemandState[stageId]?.cards || []
      existingCardIds = existingCards.map((card) => card.id)
    }

    const query = {
      attributes: [
        'id',
        'contactId',
        'title',
        'description',
        'order',
        'pipelineStageId',
        'pipelineId',
        'success',
        'statusId',
        'reasonId',
        'ownerId',
        'organization',
        'organizationSegment',
        'originChannel',
        'originCampaign',
        'createdAt',
      ],
      include: [
        {
          model: 'pipeline_stage',
          include: ['statuses'],
        },
        'stage_reason',
        'comments',
        'products',
        'pipeline',
        {
          model: 'owner',
          attributes: ['id', 'name'],
        },
        {
          model: 'stage_status',
          ...(filters?.status && {
            where: {
              name: filters?.status?.id,
            },
          }),
        },
        {
          model: 'movements',
          include: [
            {
              model: 'user',
              paranoid: false,
            },
            {
              model: 'from_pipeline_stage',
              paranoid: false,
            },
            {
              model: 'to_pipeline_stage',
              paranoid: false,
            },
            {
              model: 'from_stage_status',
              paranoid: false,
            },
            {
              model: 'to_stage_status',
              paranoid: false,
            },
            {
              model: 'from_owner',
              paranoid: false,
            },
            {
              model: 'to_owner',
              paranoid: false,
            },
          ],
          ...((filters?.wonFrom || filters?.wonUntil) && {
            where: {
              ...(filters?.wonFrom && {
                createdAt: { $gte: `${moment(filters?.wonFrom).format('YYYY-MM-DD')} 00:00:59` },
              }),
              ...(filters?.wonUntil && {
                createdAt: { $lte: `${moment(filters?.wonUntil).format('YYYY-MM-DD')} 23:59:59` },
              }),
            },
          }),
          order: [['createdAt', 'DESC']],
        },
        {
          model: 'contact',
          include: [
            'avatar',
            'tags',
            'service',
            {
              model: 'schedules',
              include: ['department', 'user'],
            },
          ],
          ...((filters?.service || filters?.nameTitle) && {
            where: {
              ...(filters?.service && {
                serviceId: filters?.service?.id,
              }),
            },
          }),
          required: true,
        },
      ],
      order: orderBy,
      where: {
        pipelineId,
        isArchived: false,
        pipelineStageId: stageId,
        ...(existingCardIds?.length > 0 && {
          id: { $notIn: existingCardIds },
        }),
        $and: [
          { ...(filters?.owner && { ownerId: filters?.owner?.id }) },
          { ...(filters?.createdFrom && { createdAt: { $gte: filters?.createdFrom } }) },
          {
            ...(filters?.createdUntil && {
              createdAt: { $lte: `${moment(filters?.createdUntil).format('YYYY-MM-DD')} 23:59:59` },
            }),
          },
          {
            ...(filters?.nameTitle && {
              $or: [
                { title: { $iLike: `%${filters?.nameTitle}%` } },
                { '$contact.name$': { $iLike: `%${filters?.nameTitle}%` } },
                { '$contact.alternativeName$': { $iLike: `%${filters?.nameTitle}%` } },
                { '$contact.internalName$': { $iLike: `%${filters?.nameTitle}%` } },
              ],
            }),
          },
        ],
      },
      limit,
      paginate: false,
      withTotal: true,
    }

    const { total, data } = yield call(pipelineApi.getPostCards, query)

    const normalized = normalizeList(data)
    if (data.length) {
      yield put(
        entitiesActions.set({
          entities: normalized.entities,
        }),
      )
    }

    yield put(
      actions.fetchMoreSuccess({
        limit,
        data,
        total,
        normalized,
        refresh,
        stageId,
        pipelineId,
      }),
    )
  } catch (error) {
    yield put(
      actions.fetchMoreFailed({
        limit,
        refresh,
        error,
        action,
        stageId,
        pipelineId,
      }),
    )
  }
}

function* handleCreatedOrUpdated(data) {
  const normalized = yield call(normalizeList, data)

  yield put(actions.receivedMany({ data, normalized }))
}

function* handleEvent(event, data, user) {
  return yield call(handleCreatedOrUpdated, data)
}

function* handleSocketEvents(channel, user) {
  try {
    while (true) {
      const payload = yield take(channel)
      const { event, data } = payload

      yield fork(handleEvent, event, data, user)
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

function* listenSocketSaga() {
  const socket = yield call(createSocket, 'card')
  const channel = yield call(socketToChannel, socket)

  yield fork(handleSocketEvents, channel)
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeEvery(actions.fetchMore, fetchMoreSaga)
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    yield takeLatest(appActions.ready, listenSocketSaga)
  }
  yield takeEvery(actions.fetchMoreFailed, reportErrorSaga)
}
