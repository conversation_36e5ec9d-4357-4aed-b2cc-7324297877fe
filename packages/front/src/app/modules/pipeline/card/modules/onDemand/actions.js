/* eslint-disable no-multi-spaces */

import { createAction } from 'redux-act-light'
import { MODULE_NAME } from '../../constants'
import { SUB_MODULE_NAME } from './constants'

const NAME = `${MODULE_NAME}.${SUB_MODULE_NAME}`

export const receivedMany = createAction(`${NAME}/RECEIVED_MANY`)

export const fetchMore = createAction(`${NAME}/FETCH_MORE`)
export const fetchMoreStarted = createAction(`${NAME}/FETCH_MORE_STARTED`)
export const fetchMoreSuccess = createAction(`${NAME}/FETCH_MORE_SUCCESS`)
export const fetchMoreFailed = createAction(`${NAME}/FETCH_MORE_FAILED`)

export const reset = createAction(`${NAME}/RESET`)
