import { all, call, cancelled, fork, put, select, take, takeLatest } from 'redux-saga/effects'
import pick from 'lodash/pick'
import i18n from '../../../../client/i18n'
import { actions as entitiesActions } from '../../entities'
import { actions as appActions } from '../../app'
import { normalizeList } from '../../../resources/pipelines/card/schema'
import * as actions from './actions'
import * as selectors from './selectors'
import createSocket from '../../../utils/createSocket'
import toast from '../../../utils/toast'
import socketToChannel, { CREATED, defaultEvents, DESTROYED, UPDATED } from '../../../utils/socketToChannel'
import { transactionSet } from '../../entities/actions'

export function* parseCardFromServer(card) {
  const currentCard = yield select(selectors.getByIdNormalized, card.id)

  if (currentCard?.updatedAt && card?.updatedAt && currentCard.updatedAt > card.updatedAt) return currentCard

  return card
}

export function* parseCardsFromServer(cards) {
  return yield all(cards.map((card) => call(parseCardFromServer, card)))
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    //yield takeLatest(appActions.ready, listenSocketSaga)
  }
}
