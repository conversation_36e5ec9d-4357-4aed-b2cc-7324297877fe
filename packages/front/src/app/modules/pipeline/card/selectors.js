import { createSelector } from 'reselect'
import orderBy from 'lodash/orderBy'
import { getEntities } from '../../entities/selectors'
import { denormalizeList, denormalizeSingle } from '../../../resources/pipelines/schema'
import { KEY } from './reducer'
import { ENTITY_NAME } from '../../../resources/pipelines/constants'

const firstAttribute = (state, val) => val

export const getByIdNormalized = createSelector(
  [getEntities, (state, id) => id],
  (entities, id) => entities[ENTITY_NAME][id],
)
