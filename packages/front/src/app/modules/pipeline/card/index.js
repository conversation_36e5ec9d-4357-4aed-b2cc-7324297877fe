import { injectReducer, injectSaga } from '../../../store/hooks'
import * as actions from './actions'
import * as constants from './constants'
import sagas from './sagas'
import * as selectors from './selectors'
import injectOnDemand from './modules/onDemand'
import injectContact from './modules/contact'
import reducer, { KEY } from './reducer'

export { actions, constants, sagas, selectors, reducer }

export default (store) => {
  injectReducer(store)(KEY, reducer)
  injectSaga(store)(sagas)
  injectOnDemand(store)
  injectContact(store)
}
