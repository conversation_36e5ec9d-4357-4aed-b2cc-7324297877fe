import { createReducer } from 'redux-act-light'
import parseAxiosError from '../../utils/parseAxiosError'
import { MODULE_NAME } from './constants'
import * as actions from './actions'
import uniq from '../../utils/arrays/uniq'

export const KEY = MODULE_NAME

// ------------------------------------
// Reducer
// ------------------------------------

const initialState = {
  pipelines: {},
  currentPipelineId: null,
}

export default createReducer(
  {
    [actions.setCurrentPipeline]: (state, { payload }) => ({
      ...state,
      currentPipelineId: payload && (typeof payload === 'string' ? payload : payload.id),
    }),

    [actions.fetchStarted]: (state, { payload }) => ({
      ...state,
      pipelines: {
        ...state.pipelines,
        [payload.id]: {
          ...state.pipelines[payload.id],
          isLoading: true,
          error: null,
        },
      },
    }),

    [actions.fetchFailed]: (state, { payload }) => ({
      ...state,
      pipelines: {
        ...state.pipelines,
        [payload.id]: {
          ...state.pipelines[payload.id],
          isLoading: false,
          error: parseAxiosError(payload.error),
        },
      },
    }),

    [actions.fetchSuccess]: (state, { payload }) => ({
      ...state,
      pipelines: {
        ...state.pipelines,
        [payload.id]: {
          ...(state.pipelines[payload.id] || {}),
          isLoading: false,
          error: null,
        },
      },
    }),

    [actions.receivedOne]: (state, { payload }) => {
      return {
        ...state,
        isLoading: false,
        error: null,
        count: payload.count.count,
      }
    },

    [actions.receivedMany]: (state, { payload }) => {
      return {
        ...state,
        pipelines: uniq([...state.pipelines, ...payload.pipelines.map((i) => i.id)]),
      }
    },
  },
  initialState,
)
