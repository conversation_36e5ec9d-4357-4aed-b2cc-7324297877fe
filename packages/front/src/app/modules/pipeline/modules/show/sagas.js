import { call, put, takeLatest, takeEvery } from 'redux-saga/effects'
import { actions as entitiesActions } from '../../../entities'
import { normalizeList, normalizeSingle } from '../../../../resources/pipelines/schema'
import pipelinesApi from '../../../../resources/pipelines/apiAuthedSaga'
import * as actions from './actions'
import { setCurrentPipeline } from '../../actions'
import { reportErrorSaga } from '../../../app/sagas'

// ------------------------------------
// Sub-routines
// ------------------------------------
export function* fetchOnePipelineSaga(action) {
  try {
    yield put(actions.fetchOneStarted())

    const { id, query } = action.payload

    const res = yield call(pipelinesApi.getPipelinesbyId, id, query)
    const pipelines = [res]

    const normalized = normalizeList(pipelines)
    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.fetchOneSuccess({ pipelines, normalized }))
  } catch (error) {
    yield put(actions.fetchOneFailed({ error, action }))
    console.log(error)
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.fetchOne, fetchOnePipelineSaga)
  yield takeEvery(actions.fetchOneFailed, reportErrorSaga)
}
