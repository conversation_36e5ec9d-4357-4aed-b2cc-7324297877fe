import { createSelector } from 'reselect'
import { KEY } from './reducer'
import { getEntities } from '../../../entities/selectors'
import { denormalizeSingle } from '../../../../resources/departments/schema'

export const getStateSlice = (state) => state[KEY] || {}

export const getIsLoading = (state) => getStateSlice(state).isLoading

export const getError = (state) => getStateSlice(state).error

export const getById = createSelector([getEntities, (state, id) => id], (entities, id) =>
  denormalizeSingle(id, entities),
)
