/* eslint-disable no-multi-spaces */

import { createAction } from 'redux-act-light'
import { MODULE_NAME } from './constants'

const NAME = MODULE_NAME

export const setCurrentPipeline = createAction(`${NAME}/SET_CURRENT_PIPELINE`)

export const receivedOne = createAction(`${NAME}/RECEIVED_ONE`)
export const receivedMany = createAction(`${NAME}/RECEIVED_MANY`)

export const fetch = createAction(`${NAME}/FETCH`)
export const fetchStarted = createAction(`${NAME}/FETCH_STARTED`)
export const fetchSuccess = createAction(`${NAME}/FETCH_SUCCESS`)
export const fetchFailed = createAction(`${NAME}/FETCH_FAILED`)

export const reset = createAction(`${NAME}/RESET`)
