import { all, call, cancelled, fork, put, select, take, takeLatest } from 'redux-saga/effects'
import pick from 'lodash/pick'
import i18n from '../../../client/i18n'
import { actions as entitiesActions } from '../entities'
import { actions as appActions } from '../app'
import { normalizeList } from '../../resources/pipelines/schema'
import * as actions from './actions'
import * as selectors from './selectors'
import createSocket from '../../utils/createSocket'
import toast from '../../utils/toast'
import socketToChannel, { CREATED, defaultEvents, DESTROYED, UPDATED } from '../../utils/socketToChannel'
import { transactionSet } from '../entities/actions'

function* setEntities(entities) {
  yield put(
    transactionSet({
      entities,
      includes: {
        cards: entities.cards,
      },
    }),
  )
}

function* handleCreatedOrUpdated(data) {
  const pipelines = Array.isArray(data) ? data : [data]

  const normalized = yield call(normalizeList, pipelines)

  yield call(setEntities, normalized.entities)

  yield put(actions.receivedMany({ pipelines, normalized }))
}

function* handleEvent(event, data, user) {
  return yield call(handleCreatedOrUpdated, data)
}

function* handleSocketEvents(channel, user) {
  try {
    while (true) {
      const payload = yield take(channel)
      const { event, data } = payload

      yield fork(handleEvent, event, data, user)
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

export function* setCurrentPipelineSaga({ payload }) {
  if (!payload) return
  yield call(setEntities, payload)
}

function* listenSocketSaga() {
  const socket = yield call(createSocket, 'pipeline')
  const channel = yield call(socketToChannel, socket)

  yield fork(handleSocketEvents, channel)
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.setCurrentPipeline, setCurrentPipelineSaga)
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    yield takeLatest(appActions.ready, listenSocketSaga)
  }
}
