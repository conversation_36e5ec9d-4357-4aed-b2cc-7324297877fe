import { select, call, put, takeLatest, take, cancelled } from 'redux-saga/effects'
import { transactionSet } from '../../../entities/actions'
import { normalizeSingle } from '../../../../resources/notification/schema'
import notificationApi from '../../../../resources/notification/apiAuthedSaga'
import * as actions from './actions'
import createSocket from '../../../../utils/createSocket'
import { actions as appActions } from '../../../app'
import socketToChannel from '../../../../utils/socketToChannel'
import { showToast } from '../../../../modules/app/actions'
import * as selectorsAuth from '../../../auth/selectors'
import ptBR from '../../../../locales/ptbr/notification.json'
import en from '../../../../locales/en/notification.json'
import es from '../../../../locales/es/notification.json'
import toast from '../../../../utils/toast'
import i18n from '../../../../../client/i18n'
import { hasPermission } from '../../../../components/common/connected/IfUserCan/IfUserCan'
import { actions as chatActions } from '../../../chat'
import toastLimitReached from '../../../../components/App/Dashboard/Chat/ChatBox/InnerChatBox/SmartSummary/ToastLimitReached'
import toastAgentLimitReached from '../../../../components/App/Dashboard/Chat/ChatBox/InnerChatBox/Agent/ToastAgentLimitReached'
import config from '../../../../../../config'

const notificationLocales = {
  'pt-BR': ptBR,
  'en-US': en,
  es,
}

const notificationLabels = {
  default: 'LABEL_TITLE_DEFAULT',
  'credits-50': 'LABEL_TITLE_CREDITS_CONSUMED',
  'credits-75': 'LABEL_TITLE_CREDITS_CONSUMED',
  'credits-90': 'LABEL_TITLE_CREDITS_CONSUMED',
  'credits-100': 'LABEL_TITLE_CREDITS_CONSUMED',
  'credits-renewal': 'LABEL_CREDITS_RENEWAL',
  'credits-additional': 'LABEL_CREDITS_ADDITIONAL',
  'credits-system-enabled': 'LABEL_CREDITS_SYSTEM_ENABLED',
  'plan-update-success': 'LABEL_PLAN_UPDATE_SUCCESS_DESCRIPTION',
  'plan-update-failed': 'LABEL_PLAN_UPDATE_FAILED_DESCRIPTION',
  'new-services': 'LABEL_NEW_SERVICES_DESCRIPTION',
  'new-users': 'LABEL_NEW_USERS_DESCRIPTION',
  'warn-expired-password-30': 'LABEL_WARN_EXPIRED_PASSWORD_DAYS',
  'warn-expired-password-50': 'LABEL_WARN_EXPIRED_PASSWORD_DAYS',
  'stopped-funnel-opportunities': 'LABEL_STOPPED_OPPORTUNITY',
  'personal-access-token-expiration': 'LABEL_TOKEN_WILL_EXPIRE',
  'agent-credits-insufficient': 'LABEL_AGENT_CREDITS_INSUFFICIENT',
}

export function parseServiceFromServer(campaign) {
  return campaign
}

export const formatToApi = (campaign) => campaign

const CREATED = 'created'
const UPDATED = 'updated'
const DESTROYED = 'destroyed'

const messageToast = (notification, language) => {
  if (notification?.text && !notificationLabels[notification?.type]) {
    return notification.text
  }

  const messageType = notificationLabels[notification.type] || notificationLabels.default

  const messageLanguage = ['pt-BR', 'en-US', 'es'].includes(language) ? language : 'pt-BR'

  return notificationLocales[messageLanguage][messageType]
}

function* showAgentToast() {
  const currentUser = yield select(selectorsAuth.getUser)
  const isAdminUser = currentUser?.isSuperAdmin || currentUser.roles.find((role) => role.isAdmin)

  if (isAdminUser) {
    yield call(toastAgentLimitReached.showLimitReachedAdminAgent)
  } else {
    yield call(toastAgentLimitReached.showLimitReachedUserAgent)
  }
}

// ------------------------------------
// Sub-routines
// ------------------------------------

function* listenSocketSaga() {
  const socket = yield call(createSocket, 'notifications')
  const channel = yield call(socketToChannel, socket)

  const userAuthenticate = yield select(selectorsAuth.getUser)

  try {
    const payload = yield take(channel)

    const { event, data } = payload

    const setEntities = (entities) => put(transactionSet({ payload: { entities } }))

    const notification = yield call(parseServiceFromServer, data)
    const normalized = yield call(normalizeSingle, data)

    const count = yield call(notificationApi.countNotifications, {
      accountId: userAuthenticate.accountId,
      userId: userAuthenticate.id,
    })

    if (event === CREATED) {
      const user = yield select(selectorsAuth.getUser)
      yield setEntities(normalized.entities)
      const hasNowPermission = hasPermission(user.permissions, 'overview.now.view')
      const link = [i18n.t('servicesPage:LINK_NAME_TO_SERVICES_WABA'), '/now?initial_tab=waba']
      const toastOptions = {
        autoClose: false,
        hideProgressBar: false,
        progress: 1,
      }
      switch (notification.type) {
        case 'waba-health-warning':
          yield call(
            toast.warnWithLink,
            i18n.t('servicesPage:WARNING_SERVICES_WABA'),
            {},
            toastOptions,
            hasNowPermission ? link : [],
          )
          break
        case 'waba-health-block':
          yield call(
            toast.errorWithLink,
            i18n.t('servicesPage:BLOCK_SERVICES_WABA'),
            {},
            toastOptions,
            hasNowPermission ? link : [],
          )
          break
        case 'limit-reached':
          yield call(toastLimitReached.showLimitReachedAdmin)
          break
        case 'limit-reached-csat':
          yield call(toastLimitReached.showLimitReachedAdminCsat)
          break
        case 'agent-credits-insufficient':
          yield call(showAgentToast)
          break
        case 'warn-expired-password-30' || 'warn-expired-password-50':
          yield call(toast.nebula, {
            description: notification.text,
            action: {
              label: i18n.t('notifications:LABEL_WARN_EXPIRED_PASSWORD_ACTION'),
              onClick: () => window.location.replace('/settings'),
            },
          })
          break
        case 'copilot':
          yield put(chatActions.setCurrentContact({ id: notification?.contactId }))
          yield call(toast.nebulaWithLink, {
            action: {
              label: notification.text,
              link: i18n.t('notifications:LABEL_BUTTON_START_CHAT'),
              variant: 'success',
              path: '/',
            },
          })
          break
        case 'warn-service-reconnect':
          break // No toast for this notification type, the notification will be shown in a dialog
        default:
          const message = messageToast(notification, user.language)
          yield put(
            showToast({
              message,
              type: (['warn', 'success', 'error', 'info'].includes(notification.type) && notification.type) || 'info',
            }),
          )
          break
      }
      yield put(actions.receivedOne({ notification, normalized, count }))
    }

    if (event === UPDATED) {
      yield setEntities(normalized.entities)
      yield put(actions.updatedOne({ notification, normalized, count }))
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    yield takeLatest(appActions.ready, listenSocketSaga)
    yield takeLatest(actions.receivedOne, listenSocketSaga)
    yield takeLatest(actions.updatedOne, listenSocketSaga)
  }
}
