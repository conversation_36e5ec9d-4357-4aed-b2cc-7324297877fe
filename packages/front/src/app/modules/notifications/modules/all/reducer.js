import { createReducer } from 'redux-act-light'
import parseAxiosError from '../../../../utils/parseAxiosError'
import { MODULE_NAME } from '../../constants'
import * as actions from './actions'
import * as socketActions from '../socket/actions'
import { SUB_MODULE_NAME } from './constants'

export const KEY = `${MODULE_NAME}${SUB_MODULE_NAME}`

// ------------------------------------
// Reducer
// ------------------------------------

const initialState = {
  isLoading: false,
  error: null,
  count: {},
}

export default createReducer(
  {
    [actions.fetchAllStarted]: (state) => ({
      ...state,
      isLoading: true,
      error: null,
      count: {},
    }),

    [actions.fetchAllFailed]: (state, { payload }) => ({
      ...state,
      isLoading: false,
      error: parseAxiosError(payload.error),
      count: {},
    }),

    [actions.fetchAllSuccess]: (state, { payload }) => {
      return {
        ...state,
        isLoading: false,
        error: null,
        count: payload.count.count,
      }
    },

    [socketActions.receivedOne]: (state, { payload }) => {
      return {
        ...state,
        isLoading: false,
        error: null,
        count: payload.count.count,
      }
    },

    [socketActions.updatedOne]: (state, { payload }) => {
      return {
        ...state,
        isLoading: false,
        error: null,
        count: payload.count.count,
      }
    },

    [actions.setCountNotifications]: (state, { payload }) => {
      return {
        ...state,
        count: payload,
      }
    },
  },
  initialState,
)
