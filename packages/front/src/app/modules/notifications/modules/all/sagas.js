import { call, put, takeLatest, takeEvery, select } from 'redux-saga/effects'
import { actions as entitiesActions } from '../../../entities'
import { normalizeList } from '../../../../resources/notification/schema'
import notificationApi from '../../../../resources/notification/apiAuthedSaga'
import { actions as appActions } from '../../../app'
import * as actions from './actions'
import { reportErrorSaga } from '../../../app/sagas'
import putAndWait from '../../../../utils/sagas/putAndWait'
import * as selectorsAuth from '../../../auth/selectors'

// ------------------------------------
// Sub-routines
// ------------------------------------
export function* fetchAllSaga(action) {
  try {
    yield put(actions.fetchAllStarted())

    const userAuthenticate = yield select(selectorsAuth.getUser)

    let notificationFromServer = []
    let currentPage = 0

    const query = JSON.stringify({
      perPage: 100,
      page: currentPage + 1,
      where: {
        accountId: userAuthenticate.accountId,
        userId: userAuthenticate.id,
        read: {
          $or: [{ $eq: false }, { $eq: null }],
        },
      },
      include: [
        {
          model: 'schedule',
          include: ['contact'],
        },
        'user',
      ],
      order: [['createdAt', 'DESC']],
    })

    const res = yield call(notificationApi.fetchMany, query)

    const count = yield call(notificationApi.countNotifications, {
      accountId: userAuthenticate.accountId,
      userId: userAuthenticate.id,
    })

    notificationFromServer = notificationFromServer.concat(res.data)

    const notifications = notificationFromServer

    const normalized = normalizeList(notificationFromServer)

    if (notifications.length) yield put(entitiesActions.set(normalized.entities))

    yield put(actions.fetchAllSuccess({ notifications, normalized, count }))
  } catch (error) {
    yield put(actions.fetchAllFailed({ error, action }))
  }
}

export function* initialFetchSaga(action) {
  const { payload } = action

  return yield call(putAndWait, actions.fetchAll(payload), actions.fetchAllSuccess(), actions.fetchAllFailed())
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.fetchAll, fetchAllSaga)
  yield takeLatest(appActions.ready, initialFetchSaga)

  yield takeEvery(actions.fetchAllFailed, reportErrorSaga)
}
