import { createAction } from 'redux-act-light'
import { MODULE_NAME } from '../../constants'
import { SUB_MODULE_NAME } from './constants'

const NAME = `${MODULE_NAME}/${SUB_MODULE_NAME}`

export const fetchAll = createAction(`${NAME}/FETCH_ALL`)

export const fetchAllStarted = createAction(`${NAME}/FETCH_ALL_STARTED`)

export const fetchAllSuccess = createAction(`${NAME}/FETCH_ALL_SUCCESS`)

export const fetchAllFailed = createAction(`${NAME}/FETCH_ALL_FAILED`)

export const reset = createAction(`${NAME}/RESET`)

export const setCountNotifications = createAction(`${NAME}/READ_ALL`)
