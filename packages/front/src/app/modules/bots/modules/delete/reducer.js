import { createReducer } from 'redux-act-light'
import parseAxiosError from '../../../../utils/parseAxiosError'
import { MODULE_NAME } from '../../constants'
import * as actions from './actions'
import { SUB_MODULE_NAME } from './constants'

export const KEY = `${MODULE_NAME}${SUB_MODULE_NAME}`

const initialState = {
  isLoading: false,
  error: null,
  success: null,
}

export default createReducer(
  {
    [actions.deleteOneStarted]: (state) => ({
      ...state,
      isLoading: true,
      error: null,
      success: null,
    }),

    [actions.deleteOneSuccess]: (state) => ({
      ...state,
      isLoading: false,
      error: null,
      success: true,
    }),

    [actions.deleteOneFailed]: (state, { payload }) => ({
      ...state,
      isLoading: false,
      error: parseAxiosError(payload.error),
      success: false,
    }),
  },
  initialState,
)
