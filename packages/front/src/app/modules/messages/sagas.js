/* eslint-disable no-continue */
import { all, call, cancelled, fork, put, select, take, takeEvery, takeLatest } from 'redux-saga/effects'
import omit from 'lodash/omit'
import uniq from 'lodash/uniq'
import orderBy from 'lodash/orderBy'
import isEmpty from 'lodash/isEmpty'
import history from '../../../client/history'
import { actions as entitiesActions } from '../entities'
import { actions as contactsActions } from '../contacts/modules/show'
import { normalizeList, normalizeSingle } from '../../resources/message/schema'
import messagesApi from '../../resources/message/apiAuthedSaga'
import ticketsApi from '../../resources/ticket/apiAuthedSaga'
import filesApi from '../../resources/file/apiAuthedSaga'
import * as actions from './actions'
import * as chatActions from '../chat/actions'
import { serviceFeatures } from '../../components/App/Dashboard/serviceFeatures'
import * as selectors from './selectors'
import { getCurrentContact, getCurrentContactId } from '../chat/selectors'
import createSocket from '../../utils/createSocket'
import { actions as appActions } from '../app'
import { selectors as contactsSelectors } from '../contacts'
import { reportErrorSaga } from '../app/sagas'
import * as authModule from '../auth'
import { viewFilter } from '../contacts/helpers'
import socketToChannel, { CREATED, DESTROYED, UPDATED } from '../../utils/socketToChannel'
import { transactionSet } from '../entities/actions'
import readFileAsDataURL from '../../utils/readFileAsDataURL'
import { retryFetch } from '../../utils/retryFetch'
import { processMessageWithMention } from '../../utils/process-message-with-mention'
import i18n from '../../../client/i18n'

export const getIncludeByContact = (contact) => {
  const type = contact?.service?.type

  return [
    ...(!type || type !== 'sms-wavy' ? ['file', 'preview', 'thumbnail'] : []),
    ...(!type || type === 'email' ? ['files'] : []),
    ...(!type || type === 'whatsapp-business' ? ['hsmFile', 'hsm'] : []),
    {
      model: 'from',
      attributes: ['id', 'name', 'alternativeName', 'internalName', 'data'],
    },
    {
      model: 'quotedMessage',
      include: [
        ...(!type || type !== 'sms-wavy' ? ['file', 'thumbnail', 'preview'] : []),
        ...(!type || type === 'email' ? ['files'] : []),
        ...(!type || type === 'whatsapp-business' ? ['hsmFile', 'hsm'] : []),
        'from',
      ],
    },
    {
      model: 'reactions',
      include: ['from', 'user'],
    },
  ]
}

export function* parseMessageFromServer(message) {
  const currentMessage = yield select(selectors.getByIdNormalized, message.id)

  if (currentMessage?.updatedAt && message?.updatedAt && currentMessage.updatedAt > message.updatedAt)
    return currentMessage

  const user = message.user || (message.userId && { id: message.userId })
  const from = omit(message.from || (message.fromId && { id: message.fromId }), ['lastMessage'])

  const contact = message.contact || { id: message.contactId }

  const currentTicketTransfer =
    message.ticket?.ticketTransfers?.length &&
    orderBy(
      message.ticket.ticketTransfers.filter((tt) => tt.createdAt < message.createdAt),
      (tt) => tt.createdAt,
      'desc',
    )[0]

  return {
    ...currentMessage,
    ...message,
    currentTicketTransfer,
    ack: currentMessage && currentMessage.ack > message.ack ? currentMessage.ack : message.ack,
    user,
    contact,
    from,
    quotedMessage:
      message.quotedMessage && omit(yield call(parseMessageFromServer, message.quotedMessage), ['quotedMessage']),
  }
}

export function* parseMessagesFromServer(messages) {
  return yield all(messages.map((message) => call(parseMessageFromServer, message)))
}

// ------------------------------------
// Sub-routines
// ------------------------------------

export function* handleNotification(message) {
  let contact = yield select(contactsSelectors.getById, message.contactId)
  if (!contact) {
    yield put(contactsActions.fetchOne({ id: message.contactId }))
    yield take(contactsActions.fetchOneSuccess)
    contact = yield select(contactsSelectors.getById, message.contactId)
  }
  const currentContactId = yield select(getCurrentContactId)
  if (!contact || contact.isSilenced || contact.id === currentContactId || message.isFromMe || !message.data.isNew) {
    return
  }

  const user = yield select(authModule.selectors.getUser)

  if (
    !user.account.settings.newMessageNotification ||
    !contact ||
    (!viewFilter(contact, user, 'MINE') && !viewFilter(contact, user, 'QUEUE') && contact?.service?.type !== 'email')
  ) {
    return
  }

  const map = {
    image: 'Enviou uma imagem',
    audio: 'Enviou um audio',
    video: 'Enviou um vídeo',
    document: 'Enviou um documento',
    location: 'Enviou uma localização',
    vcard: 'Enviou um contato',
    reaction: 'Enviou uma reação',
    sticker: 'Enviou uma figurinha',
    default: 'Enviou um arquivo',
  }

  const body = message.obfuscated
    ? 'Você não tem permissão para ler esta mensagem'
    : message.type === 'chat' || message.type === 'comment' || message.type === 'email'
      ? message.text
      : map[message.type] || map.default

  const contactName = (contact && contact.internalName) || contact.name || contact.alternativeName || ''

  yield put(
    appActions.showNotification({
      title: contactName,
      body,
      icon: contact.avatarUrl,
      timestamp: new Date(message.timestamp),
      onClickAction: actions.notificationClick({
        contactId: message.contactId,
      }),
    }),
  )
}

function* setEntities(entities) {
  yield put(transactionSet({ entities }))
}

function* handleCreated(messages, normalized, dataArray) {
  yield call(setEntities, normalized.entities)
  yield put(actions.receivedMany({ messages, normalized }))

  if (dataArray.length === 1) {
    yield call(handleNotification, messages[0])
  }
}

function* handleUpdated(messages, normalized) {
  yield call(setEntities, normalized.entities)
  yield put(actions.updatedMany({ messages, normalized }))
}

function* handleDestroyed(messages, dataArray) {
  const paths = dataArray.map((message) => `messages.${message.id}`)
  yield put(entitiesActions.unset(paths))
  yield put(actions.destroyedMany({ messages }))
}

function* handleEvent(event, data) {
  const dataArray = Array.isArray(data) ? data : [data]
  const messages = yield call(parseMessagesFromServer, dataArray)
  const normalized = yield call(normalizeList, messages)

  if (event === CREATED) return yield call(handleCreated, messages, normalized, dataArray)
  if (event === UPDATED) return yield call(handleUpdated, messages, normalized)
  if (event === DESTROYED) return yield call(handleDestroyed, messages, dataArray)
}

function* handleSocketEvents(channel) {
  try {
    while (true) {
      const payload = yield take(channel)
      const { event, data } = payload

      yield fork(handleEvent, event, data)
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

function* listenSocketSaga() {
  const socket = yield call(createSocket, 'message')
  const channel = yield call(socketToChannel, socket)

  yield fork(handleSocketEvents, channel)
}

export function* fetchManyMessagesSaga(action) {
  const { contactId, beforeTimestamp, resetLoaded, limit = 20 } = action.payload || {}
  try {
    yield put(actions.fetchStarted({ contactId }))

    if (resetLoaded) {
      const loadedMessages = yield select(selectors.getContactMessages, contactId)
      const paths = loadedMessages.map((message) => `messages.${message.id}`)
      yield put(entitiesActions.unset(paths))
    }

    const currentContact = yield select(getCurrentContact)

    const query = {
      where: {
        contactId,
        visible: true,
        type: {
          $ne: 'reaction',
        },
      },
      limit,
      order: [['timestamp', 'DESC']],
      include: getIncludeByContact(currentContact),
      subQuery: true,
      paginate: false,
    }

    if (beforeTimestamp) {
      query.where.timestamp = { $lt: beforeTimestamp }
    }

    let serverMessages = yield call(messagesApi.fetchMany, {
      includeTicketTransfer: true,
      ...query,
    })

    if (serverMessages.some((m) => m.type === 'ticket' || m.isComment)) {
      const { data: tickets } = yield call(ticketsApi.fetchMany, {
        attributes: [
          'id',
          'comments',
          'isOpen',
          'startedAt',
          'endedAt',
          'protocol',
          'userId',
          'departmentId',
          'updatedAt',
          'contactId',
        ],
        where: {
          id: {
            $in: uniq(serverMessages.map((sm) => sm.ticketId).filter(Boolean)),
          },
        },
        include: [
          {
            model: 'department',
            attributes: ['id', 'name'],
          },
          {
            model: 'ticketTransfers',
            ...(!serverMessages.some((sm) => sm.isComment) && {
              where: {
                transferredMessageId: {
                  $in: serverMessages.filter((sm) => sm.type === 'ticket').map((m) => m.id),
                },
              },
            }),
            include: [
              {
                model: 'fromUser',
                attributes: ['id', 'name'],
              },
              {
                model: 'toUser',
                attributes: ['id', 'name'],
              },
              {
                model: 'fromDepartment',
                attributes: ['id', 'name'],
              },
              {
                model: 'toDepartment',
                attributes: ['id', 'name'],
              },
              {
                model: 'byUser',
                attributes: ['id', 'name'],
              },
            ],
          },
        ],
      })

      if (tickets.length) {
        serverMessages = serverMessages.map((serverMessage) => {
          const ticket = tickets.find((t) => t.id === serverMessage.ticketId)
          let currentTicketTransfer = null

          if (ticket) {
            currentTicketTransfer =
              ticket?.ticketTransfers.length &&
              orderBy(
                ticket?.ticketTransfers.filter((tt) => tt.createdAt < serverMessage.createdAt),
                (tt) => tt.createdAt,
                'desc',
              )[0]
          }

          return {
            ...serverMessage,
            currentTicketTransfer,
            ticket,
            ticketTransfer: ticket?.ticketTransfers.find((tt) => tt.transferredMessageId === serverMessage.id),
          }
        })
      }
    }

    const messages = yield call(parseMessagesFromServer, serverMessages)

    const normalized = normalizeList(messages)

    const noMore = !messages.length

    const pagination = {
      noMore,
      ...(!noMore ? { lastTimestamp: messages[messages.length - 1].timestamp } : null),
    }

    yield put(
      entitiesActions.set({
        entities: normalized.entities,
        includes: {
          contacts: {
            lastMessage: false,
          },
        },
      }),
    )
    yield put(
      actions.fetchSuccess({
        contactId,
        messages,
        normalized,
        pagination,
      }),
    )
  } catch (error) {
    yield put(actions.fetchFailed({ contactId, error, action }))
  }
}

export function* sendMessageSaga(action) {
  const { payload } = action

  const isEdit = Boolean(payload.editMessage?.id)

  const { mentionedList, modifiedText } = processMessageWithMention(payload.text ?? '')

  const messageData = { ...payload, mentionedList, text: modifiedText ?? payload.text }

  if (!mentionedList?.length) delete messageData.mentionedList

  try {
    yield put(actions.sendStarted(messageData))

    const currentContact = yield select(getCurrentContact)
    const type = currentContact.service?.type

    const include = [
      'preview',
      'thumbnail',
      {
        model: 'ticket',
        attributes: ['id', 'comments', 'isOpen', 'startedAt', 'endedAt', 'protocol', 'userId', 'departmentId'],
        include: [
          {
            model: 'department',
            attributes: ['id', 'name'],
          },
          {
            model: 'ticketTransfers',
            attributes: ['id', 'toUserId', 'toDepartmentId', 'createdAt'],
          },
        ],
      },
    ]

    if (!type || type !== 'sms-wavy') {
      include.push('file')
    }

    if (type && type === 'email') {
      include.push('files')
    }

    if (type && type === 'whatsapp-business') {
      include.push('hsm')
      include.push('hsmFile')
    }

    if (type && serviceFeatures[type].messageOptionReply) {
      const includeQuotedMessage = ['preview', 'thumbnail']

      if (!type || type !== 'sms-wavy') {
        includeQuotedMessage.push('file')
      }

      if (type && type === 'email') {
        includeQuotedMessage.push('files')
      }

      if (type && type === 'whatsapp-business') {
        includeQuotedMessage.push('hsmFile')
        includeQuotedMessage.push('hsm')
      }

      include.push({
        model: 'quotedMessage',
        include: includeQuotedMessage,
      })
    }

    const files = !isEmpty(messageData.file)
      ? [messageData.file]
      : !isEmpty(messageData.attachments)
        ? messageData.attachments
        : []

    const serviceId = currentContact.serviceId || currentContact.service?.id

    if (files?.length) {
      const filesIds = yield all(
        files.map(function* (file) {
          const [fileType] = file.mimetype.split('/')

          const createdFile = yield call(filesApi.createToUpload, {
            ...(fileType === 'audio' && {
              base64: file.base64 || (yield readFileAsDataURL(file.blob)).split('base64,')[1],
              isPtt: file.isPtt,
            }),
            name: file.name,
            mimetype: file.mimetype,
            serviceId,
            attachedType: messageData.attachments?.length ? 'message.files' : 'message.file',
          })

          const { id, mimetype, urlToUpload } = createdFile

          if (urlToUpload) {
            try {
              if (file?.blob?.fileId) {
                yield call(filesApi.duplicateFile, { fileId: file?.blob?.fileId, urlToUpload, serviceId })
                return id
              }

              yield retryFetch(urlToUpload, {
                method: 'PUT',
                body: file.blob,
                headers: {
                  'Content-Type': mimetype,
                },
              })
            } catch (error) {
              yield put(
                appActions.showToast({
                  message: 'Falha ao enviar arquivo após várias tentativas.',
                  type: 'error',
                }),
              )
            }
          }

          return id
        }),
      )

      if (messageData.attachments || filesIds.length > 1) {
        messageData.filesIds = filesIds
      } else if (filesIds.length === 1) {
        messageData.fileId = filesIds[0]

        if (files[0].isPtt) {
          messageData.isPtt = files[0].isPtt
        }
      }

      delete messageData.file
      delete messageData.attachments
      delete messageData.interactiveMessage?.file
    } else {
      try {
        if (messageData.hsmFileId) {
          messageData.fileId = yield call(filesApi.duplicateFile, { fileId: messageData.hsmFileId, serviceId })
          delete messageData.hsmFileId
        }
      } catch (error) {
        yield put(
          appActions.showToast({
            message: 'Falha ao enviar arquivo.',
            type: 'error',
          }),
        )
      }
    }

    let serverMessage
    if (isEdit) {
      serverMessage = yield call(
        messagesApi.edit,
        {
          mentionedList,
          id: payload?.editMessage?.id,
          contactId: messageData.contactId,
          text: messageData.text,
        },
        {},
      )
    } else {
      serverMessage = yield call(messagesApi.create, messageData, {
        params: {
          include,
        },
      })
    }

    const message = yield call(parseMessageFromServer, serverMessage)
    const normalized = normalizeSingle(message)
    const callback = action[0]

    if (typeof callback === 'function') {
      callback()
    }

    yield put(
      entitiesActions.set({
        entities: normalized.entities,
        includes: {},
      }),
    )

    yield put(
      actions.sendSuccess({
        contactId: messageData.contactId,
        // normalized,
        // message,
        action,
      }),
    )
  } catch (error) {
    if ((error.response || {}).status === 400) {
      yield put(
        appActions.showToast({
          message: i18n.t('message:MESSAGE_SEND_ERROR'),
          type: 'warn',
        }),
      )

      if (error.response.data?.error === 'contact_blocked') {
        yield put(
          appActions.showToast({
            message: i18n.t('message:CONTACT_BLOCKED'),
            type: 'warn',
          }),
        )
      }

      error.dontReport = true
    }

    if ((error.response || {}).status === 402) {
      yield put(
        appActions.showToast({
          message: 'Créditos insuficientes para enviar essa mensagem.',
          type: 'warn',
        }),
      )
      error.dontReport = true
    }

    if ((error.response || {}).status === 500) {
      yield put(
        appActions.showToast({
          message: i18n.t('message:MESSAGE_SEND_ERROR'),
          type: 'warn',
        }),
      )

      error.dontReport = true
    }

    yield put(
      actions.sendFailed({
        contactId: messageData.contactId,
        error,
        action,
      }),
    )
  }
}

export function* revokeMessageSaga(action) {
  const { payload } = action

  const message = payload

  try {
    yield put(actions.revokeStarted(message))

    yield call(messagesApi.revokeById, message.id)

    yield put(
      actions.revokeSuccess({
        contactId: message.contactId,
        action,
      }),
    )
  } catch (error) {
    yield put(actions.revokeFailed({ contactId: message.contactId, error, action }))
  }
}

export function* forwardMessageSaga(action) {
  const { payload } = action

  const messageData = payload

  try {
    yield put(actions.forwardMessageStarted(messageData))

    yield call(messagesApi.forwardMessages, messageData)

    yield put(actions.forwardMessageSuccess({ action }))
  } catch (error) {
    yield put(actions.forwardMessageFailed({ error, action }))
  }
}

export function* syncFileSaga(action) {
  const { payload } = action

  const message = payload

  try {
    yield put(actions.forwardMessageStarted(message))

    yield call(messagesApi.syncFile, message.id)

    yield put(actions.forwardMessageSuccess({ action }))
  } catch (error) {
    yield put(actions.forwardMessageFailed({ error, action }))
  }
}

export function* notificationClickSaga({ payload }) {
  const { contactId } = payload

  yield put(chatActions.setCurrentContact({ id: contactId }))
  yield window.focus()
  yield history.push('/')
}

export function* incrementLoadingSaga({ payload }) {
  const { contactId } = payload

  yield put(actions.incrementLoading({ contactId }))
}

function* decrementLoading({ contactId, count }) {
  const loadingMessagesCount = yield select(selectors.getLoadingMessageCount, contactId)
  if (loadingMessagesCount < 1) return

  yield put(actions.decrementLoading({ contactId, count }))
}

export function* decrementLoadingSaga({ payload }) {
  const { contactId } = payload

  yield call(decrementLoading, { contactId, count: 1 })
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.fetch, fetchManyMessagesSaga)
  yield takeEvery(actions.send, sendMessageSaga)
  yield takeEvery(actions.forwardMessage, forwardMessageSaga)
  yield takeEvery(actions.syncFile, syncFileSaga)

  yield takeEvery(actions.revoke, revokeMessageSaga)

  yield takeEvery(actions.fetchFailed, reportErrorSaga)
  yield takeEvery(actions.sendFailed, reportErrorSaga)
  yield takeEvery(actions.revokeFailed, reportErrorSaga)

  yield takeEvery(actions.sendStarted, incrementLoadingSaga)
  yield takeEvery(actions.sendSuccess, decrementLoadingSaga)
  yield takeEvery(actions.sendFailed, decrementLoadingSaga)
  yield takeEvery(actions.notificationClick, notificationClickSaga)

  if (process.env.BUILD_FLAG_IS_CLIENT === 'true') {
    yield takeLatest(appActions.ready, listenSocketSaga)
  }
}
