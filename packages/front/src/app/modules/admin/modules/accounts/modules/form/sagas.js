import pick from 'lodash/pick'
import { call, put, takeLatest } from 'redux-saga/effects'
import { actions as entitiesActions } from '../../../../../entities'
import { normalizeList } from '../../../../../../resources/admin/account/schema'
import accountsApi from '../../../../../../resources/admin/account/apiAuthedSaga'
import * as actions from './actions'
import { actions as accountsFormUiActions } from './index'

export const formatToApi = (account) => ({
  ...pick(account, [
    'id',
    'name',
    'isCampaignActive',
    'isActive',
    'settings',
    'correlationId',
    'promptAiFinalize',
    'promptAiTransfer',
    'promptAiCsat',
  ]),
  data: {
    managerNumber: account.managerNumber,
  },
  plan: {
    users: account.users,
    services: {
      ...[
        'whatsapp',
        'whatsapp-business',
        'whatsapp-remote',
        'webchat',
        'telegram',
        'sms-wavy',
        'email',
        'facebook-messenger',
        'instagram',
        'google-business-message',
        'reclame-aqui',
      ].reduce((aggr, item) => ({ ...aggr, [item]: parseInt(account[item], 10) }), {}),
    },
    ai: account?.plan?.ai || {},
    renewDate: account?.plan?.renewDate,
    hsmLimit: Number(account.hsmLimit || 0),
    hsmUsedLimit: account.hsmUsedLimit || 0,
  },
  expiresAt: account.expiresAt || null,
  byFront: true,
})

export function* createOneAccountSaga(action) {
  try {
    yield put(actions.createOneStarted())

    const { account } = action.payload
    const formattedAccount = formatToApi(account)

    const serverAccount = yield call(accountsApi.create, formattedAccount)
    const accounts = [serverAccount]

    const normalized = normalizeList(accounts)

    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.createOneSuccess({ accounts, normalized }))
  } catch (error) {
    yield put(actions.createOneFailed({ error, action }))
  }
}

export function* updateOneAccountSaga(action) {
  try {
    yield put(actions.updateOneStarted())

    const { id, account } = action.payload

    const formattedAccount = formatToApi(account)

    const serverAccount = yield call(accountsApi.updateById, id, formattedAccount)
    const accounts = [serverAccount]

    const normalized = normalizeList(accounts)

    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.updateOneSuccess({ accounts, normalized }))
  } catch (error) {
    yield put(actions.updateOneFailed({ error, action }))
  }
}

export function* saveOneAccountSaga(action) {
  try {
    yield put(actions.saveOneStarted())

    const {
      account,
      account: { id },
    } = action.payload

    yield put(id ? actions.updateOne({ id, account }) : actions.createOne({ account }))

    // TODO handle this, currently always success
    yield put(actions.saveOneSuccess())
  } catch (error) {
    yield put(actions.saveOneFailed({ error, action }))
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(accountsFormUiActions.saveOne, saveOneAccountSaga)
  yield takeLatest(accountsFormUiActions.createOne, createOneAccountSaga)
  yield takeLatest(accountsFormUiActions.updateOne, updateOneAccountSaga)
}
