import { call, put, takeLatest, takeEvery, select } from 'redux-saga/effects'
import { actions as entitiesActions } from '../../../../../entities'
import { normalizeList } from '../../../../../../resources/ticket/schema'
import ticketsApi from '../../../../../../resources/ticket/apiAuthedSaga'
import { selectors as authSelectors } from '../../../../../auth'
import * as actions from './actions'
import { reportErrorSaga } from '../../../../../app/sagas'
import parsePaginationFromResponse from '../../../../../../utils/parsePaginationFromResponse'

// ------------------------------------
// Sub-routines
// ------------------------------------
export function* fetchManySaga(action) {
  try {
    yield put(actions.fetchManyStarted())

    const {
      filters = {},
      pagination: currentPagination = {
        page: 1,
        perPage: 15,
      },
    } = action.payload || {}

    const buildQuery = {
      order: [['updatedAt', 'DESC']],
      where: {
        ...(filters.status &&
          filters.status.value !== 'all' && {
            isOpen: filters.status.value === 'open',
          }),
        ...(filters.protocolNumber && {
          protocol: filters.protocolNumber,
        }),
        ...(filters.periodType &&
          filters.from &&
          filters.to && {
            ...(filters.periodType.value === 'all' && {
              $or: {
                startedAt: {
                  $gt: filters.from,
                  $lt: filters.to,
                },
                endedAt: {
                  $gt: filters.from,
                  $lt: filters.to,
                },
              },
            }),
            ...(filters.periodType.value !== 'all' &&
              filters.periodType.value === 'openDate' && {
                startedAt: {
                  $gt: filters.from,
                  $lt: filters.to,
                },
              }),
            ...(filters.periodType.value !== 'all' &&
              filters.periodType.value === 'closeDate' && {
                endedAt: {
                  $gt: filters.from,
                  $lt: filters.to,
                },
              }),
          }),
      },
      include: [
        ...(filters.contactName || filters.service || filters.contactNumber
          ? [
              {
                model: 'contact',
                where: {
                  ...(filters.contactName && {
                    $or: {
                      name: { $iLike: `%${filters.contactName}%` },
                      internalName: { $iLike: `%${filters.search}%` },
                      alternativeName: { $iLike: `%${filters.search}%` },
                    },
                  }),

                  ...(filters.service && {
                    serviceId: filters.service.id,
                  }),

                  ...(filters.contactNumber && {
                    'data.number': { $iLike: `%${filters.contactNumber}%` },
                  }),
                },
                include: ['avatar', 'thumbAvatar', 'service'],
                required: true,
              },
            ]
          : [
              {
                model: 'contact',
                include: ['avatar', 'thumbAvatar', 'service'],
                required: true,
              },
            ]),

        ...(filters.contactName || filters.service || filters.contactNumber
          ? [
              {
                model: 'contact',
                where: {
                  ...(filters.contactName && {
                    $or: {
                      name: { $iLike: `%${filters.contactName}%` },
                      internalName: { $iLike: `%${filters.search}%` },
                      alternativeName: { $iLike: `%${filters.search}%` },
                    },
                  }),

                  ...(filters.service && {
                    serviceId: filters.service.id,
                  }),

                  ...(filters.contactNumber && {
                    'data.number': { $iLike: `%${filters.contactNumber}%` },
                  }),
                },
                include: ['avatar', 'thumbAvatar', 'service'],
                required: true,
              },
            ]
          : [
              {
                model: 'contact',
                include: ['avatar', 'thumbAvatar', 'service'],
                required: true,
              },
            ]),

        ...(filters.user
          ? [
              {
                model: 'user',
                where: {
                  id: filters.user.id,
                },
                required: true,
              },
            ]
          : ['user']),

        ...(filters.department
          ? [
              {
                model: 'department',
                where: {
                  id: filters.department.id,
                },
                required: true,
              },
            ]
          : ['department']),
        ...(filters.ticketTopics.length
          ? [
              {
                model: 'ticketTopics',
                where: {
                  id: {
                    $in: filters.ticketTopics.map((topic) => topic.id),
                  },
                },
                required: true,
              },
            ]
          : ['ticketTopics']),
      ],
      ...currentPagination,
    }

    const data = yield call(ticketsApi.fetchMany, buildQuery)
    const tickets = data.data
    const normalized = normalizeList(tickets)
    const pagination = parsePaginationFromResponse(data)

    yield put(entitiesActions.set(normalized.entities))

    yield put(
      actions.fetchManySuccess({
        tickets,
        normalized,
        pagination,
      }),
    )
  } catch (error) {
    yield put(
      actions.fetchManyFailed({
        error,
        action,
      }),
    )
  }
}

function* updateSaga(action) {
  const byUserId = yield select(authSelectors.getUserId)

  const data = {
    ...action.payload,
    ticketTopicIds: action.payload.ticketTopics.map((item) => item.id),
    ...(action.payload.aiSummaryRating && { aiSummaryRating: action.payload.aiSummaryRating.value }),
    byUserId,
  }

  try {
    yield put(actions.updateStarted())

    yield call(ticketsApi.closeTickets, data)

    yield put(
      actions.updateSuccess({
        data: action.payload,
      }),
    )
  } catch (error) {
    yield put(
      actions.updateFailed({
        error,
        action,
      }),
    )
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeEvery(actions.fetchMany, fetchManySaga)
  yield takeEvery(actions.fetchManyFailed, reportErrorSaga)
  yield takeEvery(actions.update, updateSaga)
}
