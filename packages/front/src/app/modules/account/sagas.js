/* eslint-disable func-names */
import { call, cancelled, put, take, takeEvery, takeLatest, select } from 'redux-saga/effects'
import { useSelector } from 'react-redux'
import pick from 'lodash/pick'
import * as actions from './actions'
import { actions as authActions, selectors } from '../auth'
import { normalizeList, normalizeSingle } from '../../resources/account/schema'
import accountApi from '../../resources/account/apiAuthedSaga'
import apiAccount from './services/accountApiSaga'
import { reportErrorSaga } from '../app/sagas'
import { actions as entitiesActions } from '../entities'
import { actions as servicesAllActions } from '../services/modules/all'
import createSocket from '../../utils/createSocket'
import socketToChannel, { CREATED, DESTROYED, UPDATED } from '../../utils/socketToChannel'
import { getIsImpersonating } from '../admin/modules/users/modules/impersonate/selectors'

// ------------------------------------
// Sub-routines
// ------------------------------------
export const formatToApi = (account) => ({
  ...pick(account, ['id', 'name', 'defaultDepartment', 'wizardProgress']),
  settings: pick(account.settings, [
    'absence',
    'ticketsEnabled',
    'encryptionDisabled',
    'ticketInactiveTime',
    'userAwayMinutesTime',
    'ticketOpenNotification',
    'ticketTransferNotification',
    'userNameInMessages',
    'newMessageNotification',
    'showSupportInfo',
    'allowDuplicateNames',
    'changeUserPasswordOnFirstAccess',
    'userPasswordCreationMethod',
    'topicRequired',
    'isUserEmailRequired',
    'isQueueNotificationActive',
    'disableDefaultTicketTransfer',
    'timezone',
    'workPlan',
    'protocolFormat',
    'ipRestriction',
    'allowedIps',
    'isPasswordExpirationActive',
    'expirationPasswordTime',
    'twoFactorAuthActive',
    'twoFactorAuthMandatory',
    'twoFactorAuthMandatorySchedule',
    'showTagsInChat',
    'userCanChangeVisibilityTagsOnChat',
    'autoGenerateSummaryOnTransfer',
    'autoGenerateSummaryOnClosure',
  ]),
})

const parseAccountFromServer = async (account) => account
const parseAccountsFromServer = (accounts) => Promise.all(accounts.map(parseAccountFromServer))

function* listenSocketSaga() {
  const query = {
    include: ['defaultDepartment'],
  }

  const socket = yield call(createSocket, 'account', query)
  const channel = yield call(socketToChannel, socket)

  try {
    while (true) {
      const payload = yield take(channel)

      const { event, data } = payload

      const setEntities = (entities) => put(entitiesActions.transactionSet({ entities }))

      if (event === CREATED || event === UPDATED) {
        if (Array.isArray(data)) {
          const accounts = yield call(parseAccountsFromServer, data)
          const normalized = yield call(normalizeList, accounts)
          yield setEntities(normalized.entities)
          yield put(actions.receivedMany({ accounts, normalized }))

          continue
        }

        const account = yield call(parseAccountFromServer, data)
        const normalized = yield call(normalizeSingle, account)
        yield setEntities(normalized.entities)
        yield put(actions.receivedOne({ account, normalized }))

        const localUser = yield select(selectors.getUser)
        if (account.id === localUser.accountId) {
          const user = {
            ...localUser,
            account: {
              ...localUser.account,
              ...account,
              creditsControlEnabled: account.creditsControlEnabled,
              withoutCreditsAvailable: account.withoutCreditsAvailable,
            },
          }

          const normalizedUser = normalizeList([user])
          yield put(entitiesActions.set(normalizedUser.entities))
          yield put(authActions.updateUserSuccess({ user }))
        }
        continue
      }

      if (event === DESTROYED) {
        if (Array.isArray(data)) {
          const paths = data.map((account) => `accounts.${account.id}`)
          yield put(entitiesActions.unset(paths))
          yield put(actions.destroyedMany({ accounts: data }))

          continue
        }

        yield put(entitiesActions.unset(`accounts.${data.id}`))
        yield put(actions.destroyedOne({ account: data }))

        continue
      }
    }
  } finally {
    if (yield cancelled()) channel.close()
  }
}

export function* updateSaga(action) {
  try {
    yield put(actions.updateStarted())

    const formattedAccount = formatToApi(action.payload)

    const query = { params: { include: ['defaultDepartment'] } }
    const serverAccount = yield call(accountApi.update, formattedAccount, query)
    const accounts = [serverAccount]

    const normalized = normalizeList(accounts)
    yield put(entitiesActions.set(normalized.entities))

    yield put(actions.updateSuccess({ accounts, normalized }))
  } catch (error) {
    yield put(actions.updateFailed({ error, action }))
  }
}

export function* accountBlockSaga(action) {
  const impersonate = useSelector(getIsImpersonating)

  const token = yield select((state) => state.auth.accessToken)
  try {
    yield put(actions.accountBlockStarted())

    yield call(
      apiAccount.accountBlock({
        headers: {
          authorization: `Bearer ${token}`,
          impersonate,
        },
      }),
    )

    yield put(actions.accountBlockSuccess())

    yield window.location.reload()
  } catch (error) {
    yield put(actions.accountBlockFailed({ error, action }))
  }
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(servicesAllActions.fetchAllSuccess, listenSocketSaga)
  yield takeLatest(actions.update, updateSaga)
  yield takeEvery(actions.updateFailed, reportErrorSaga)
  yield takeEvery(actions.accountBlock, accountBlockSaga)
}
