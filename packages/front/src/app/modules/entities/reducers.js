/* eslint-disable no-param-reassign */
import omit from 'lodash/omit'
import pickBy from 'lodash/pickBy'
import pick from 'lodash/pick'
import identity from 'lodash/identity'
import { createReducer } from 'redux-act-light'
import * as entitiesActions from './actions'
import { NAME } from './constants'

// ------------------------------------
// Form Entity Reducer
// ------------------------------------
export const entitiesKey = NAME

const initialState = {
  accounts: {},
  bots: {},
  campaigns: {},
  contacts: {},
  pipelines: {},
  departments: {},
  messages: {},
  permissions: {},
  plans: {},
  quickReplies: {},
  roles: {},
  services: {},
  tags: {},
  tickets: {},
  tokens: {},
  users: {},
  webhooks: {},
  questions: {},
  notifications: {},
}

const onlyFalse = (items) =>
  Object.entries(items)
    .filter(([_, val]) => !val)
    .map(([key]) => key)

const onlyTrue = (items) =>
  Object.entries(items)
    .filter(([_, val]) => val)
    .map(([key]) => key)

const setHandler = (oldState, { payload = {} }) => {
  const normalized = payload.entities || payload
  const includes = payload.includes || {}

  const newState = { ...oldState }

  Object.entries(normalized).forEach(([entityName, entities]) => {
    Object.entries(entities).forEach(([id, entity]) => {
      const include = includes[entityName]

      const newModel = include
        ? {
            ...omit(entity, onlyFalse(include)),
            ...pick(entity, onlyTrue(include)),
          }
        : entity

      newState[entityName] = { ...newState[entityName] }
      newState[entityName][id] = { ...newState[entityName][id], ...newModel }
    })
  })

  return newState
}

export const entitiesReducer = createReducer(
  {
    [entitiesActions.set]: setHandler,
    [entitiesActions.bufferedSetFlush]: (state, { payload = [] }) =>
      payload.reduce((s, p) => setHandler(s, { payload: p }), state),
    [entitiesActions.clean]: (
      state,
      {
        payload = [
          {
            bots: {},
            campaigns: {},
            contacts: {},
            pipelines: {},
            departments: {},
            messages: {},
            quickReplies: {},
            tags: {},
            tokens: {},
            users: {},
            webhooks: {},
            questions: {},
            notifications: {},
          },
        ],
      },
    ) => payload.reduce((s, p) => setHandler(s, { payload: [] }), state),
    [entitiesActions.transactionSetFlush]: (state, { payload = [] }) =>
      payload.reduce((s, p) => setHandler(s, { payload: p }), state),
    [entitiesActions.unset]: (state, { payload }) => omit(state, payload),
  },
  initialState,
)
