import injectEntities from './entities'
import injectAuth from './auth'
import injectBlockMessageRule from './blockMessageRule'
import injectPermissions from './permissions'
import injectRoles from './roles'
import injectStickers from './stickers'
import injectUsers from './users'
import injectServices from './services'
import injectContacts from './contacts'
import injectAccount from './account'
import injectDepartments from './departments'
import injectMessages from './messages'
import injectTags from './tags'
import injectApp from './app'
import injectChat from './chat'
import injectAdmin from './admin'
import injectCampaigns from './campaigns'
import injectBots from './bots'
import injectQuickReplies from './quickReplies'
import injectTickets from './tickets'
import injectOverview from './overview'
import injectWizard from './wizard'
import injectUi from './ui'
import injectNotifications from './notifications'
import injectNavbar from './navbar'
import injectPipeline from './pipeline'
import injectCard from './pipeline/card'

export default (store) => {
  injectApp(store)
  injectEntities(store)
  injectAuth(store)
  injectBlockMessageRule(store)
  injectPermissions(store)
  injectRoles(store)
  injectStickers(store)
  injectUsers(store)
  injectServices(store)
  injectContacts(store)
  injectMessages(store)
  injectTags(store)
  injectChat(store)
  injectAdmin(store)
  injectCampaigns(store)
  injectDepartments(store)
  injectAccount(store)
  injectBots(store)
  injectQuickReplies(store)
  injectTickets(store)
  injectWizard(store)
  injectOverview(store)
  injectUi(store)
  injectNotifications(store)
  injectNavbar(store)
  injectPipeline(store)
  injectCard(store)
}
