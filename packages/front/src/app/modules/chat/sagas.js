import { call, put, takeLatest, select, take, fork, delay } from 'redux-saga/effects'
import omit from 'lodash/omit'
import * as actions from './actions'
import { actions as messagesActions, selectors as messagesSelectors } from '../messages'
import { actions as contactsActions } from '../contacts/modules/show'
import { actions as authActions } from '../auth'
import contactsApi from '../../resources/contact/apiAuthedSaga'
import putAndWait from '../../utils/sagas/putAndWait'

// ------------------------------------
// Sub-routines
// ------------------------------------
export function* setCurrentContactBeforeLoad(action) {
  const contactId = action.payload.id

  yield put(contactsActions.fetchOne({ id: contactId }))
  yield take(contactsActions.fetchOneSuccess)

  const contact = yield select((state) => state.entities.contacts[contactId])

  yield put(actions.setCurrentContact(contact))
  yield put(actions.sync(contact))
}

export function* setCurrentContactSaga({ payload }) {
  yield put(authActions.updateSocketUiState())

  if (!payload) return
  const contactId = payload?.id || payload

  const pagination = yield select(messagesSelectors.getPagination, contactId)
  if (pagination) return

  const foundContact = yield select((state) => !!state.entities.contacts[contactId])

  if (!foundContact) {
    yield put(contactsActions.fetchOne({ id: contactId }))
  }

  const contact = yield select((state) => state.entities.contacts[contactId])

  if (!contact?.service) {
    yield put(contactsActions.fetchOne({ id: contactId }))
  }

  yield put(messagesActions.fetch({ contactId }))
}

export function* loadMoreMessagesSaga(action) {
  const { payload } = action

  try {
    yield put(actions.loadMoreMessagesStarted(payload))

    const contactId = payload.id

    const limit = payload.limit || 20

    let { lastTimestamp: beforeTimestamp } = yield select(messagesSelectors.getPagination, contactId)

    beforeTimestamp = beforeTimestamp ? new Date(beforeTimestamp) : new Date()

    beforeTimestamp.setMilliseconds(beforeTimestamp.getMilliseconds() + 1)

    const response = yield call(
      putAndWait,
      messagesActions.fetch({ contactId, beforeTimestamp, limit }),
      messagesActions.fetchSuccess,
      messagesActions.fetchFailed,
    )

    if (response.messages.length < limit) {
      yield call(contactsApi.loadEarlierMessages, contactId, {
        timestamp: beforeTimestamp,
      })

      yield call(
        putAndWait,
        messagesActions.fetch({ contactId, beforeTimestamp, limit }),
        messagesActions.fetchSuccess,
        messagesActions.fetchFailed,
      )
    }

    yield put(actions.loadMoreMessagesSuccess(payload))
    if (payload.callback) {
      payload.callback()
    }
  } catch (error) {
    yield put(actions.loadMoreMessagesFailed({ error, action }))
  }
}

export function* syncSaga(action) {
  const payload = omit(action.payload, 'forceSync')

  try {
    yield put(actions.syncStarted(payload))

    const contactId = payload.id

    yield call(contactsApi.syncById, contactId, {
      forceSync: action.payload.forceSync,
    })

    yield put(actions.syncSuccess(payload))
  } catch (error) {
    yield put(actions.syncFailed({ error, action }))
  }
}

export function* setFiltersSaga() {
  yield put(authActions.updateSocketUiState())
}

function* gcSaga() {
  yield delay(60 * 1000)
  yield put(actions.gc())
  yield call(gcSaga)
}

// ------------------------------------
// Watchers
// ------------------------------------
export default function* () {
  yield takeLatest(actions.setCurrentContact, setCurrentContactSaga)
  yield takeLatest(actions.setCurrentContactBeforeLoad, setCurrentContactBeforeLoad)
  yield takeLatest(actions.sync, syncSaga)
  yield takeLatest(actions.loadMoreMessages, loadMoreMessagesSaga)
  yield takeLatest(actions.setFilters, setFiltersSaga)
  yield fork(gcSaga)
}
