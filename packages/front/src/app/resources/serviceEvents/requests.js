import { useFetchManyRequestWithGlobalState } from '../../hooks/useFetchManyRequest'
import { useFetchOneRequestWithLocalState } from '../../hooks/useFetchOneRequest'
import serviceEventsApi from './api'
import { denormalizeList, denormalizeSingle, normalizeList, normalizeSingle } from './schema'
import { useSaveRequestWithLocalState } from '../../hooks/useSaveRequest'
import { useRequest } from '../../hooks/useRequest'

export const useFetchManyServiceEvents = (query) =>
  useFetchManyRequestWithGlobalState(
    'serviceEventsIndex',
    serviceEventsApi.fetchMany,
    query,
    normalizeList,
    denormalizeList,
  )

export const useFetchManyBlockDDD = (query) =>
  useFetchManyRequestWithGlobalState('blockDDDIndex', serviceEventsApi.fetchMany, query, normalizeList, denormalizeList)

export const useFetchOneServiceEvents = (id, query) =>
  useFetchOneRequestWithLocalState(serviceEventsApi.fetchById, id, query, normalizeSingle, denormalizeSingle)

export const useCreateServiceEvents = () =>
  useSaveRequestWithLocalState(serviceEventsApi.create, normalizeSingle, denormalizeSingle)

export const useUpdateServiceEvents = () =>
  useSaveRequestWithLocalState(serviceEventsApi.updateById, normalizeSingle, denormalizeSingle)

export const useDeleteServiceEvents = (id) => useRequest(serviceEventsApi.deleteById, id)
