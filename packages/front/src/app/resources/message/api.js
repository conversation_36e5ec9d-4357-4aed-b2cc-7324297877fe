import createApi, { extractData, getAxios } from '../../utils/apiResource'

const endpoint = '/messages'

export default {
  ...createApi(endpoint),
  edit: (data, axiosConfig) => {
    const { id, ...rest } = data
    return getAxios().patch(`${endpoint}/${id}`, rest, axiosConfig).then(extractData)
  },
  revokeById: (id, axiosConfig) => getAxios().post(`${endpoint}/${id}/revoke`, {}, axiosConfig),
  syncFile: (id, axiosConfig) => getAxios().post(`${endpoint}/${id}/sync-file`, {}, axiosConfig).then(extractData),
  sendVCards: (data, axiosConfig) => getAxios().post(`${endpoint}/send-vcards`, data, axiosConfig),
  forwardMessages: (data, axiosConfig) => getAxios().post(`${endpoint}/forward`, data, axiosConfig).then(extractData),
  transcription: (id, axiosConfig) => getAxios().get(`transcripts/${id}`, axiosConfig).then(extractData),
  sendReaction: (id, data, axiosConfig) =>
    getAxios().post(`${endpoint}/${id}/send-reaction`, data, axiosConfig).then(extractData),
  revokeReaction: (id, data, axiosConfig) =>
    getAxios().put(`${endpoint}/${id}/revoke-reaction`, data, axiosConfig).then(extractData),
  summarize: (id, axiosConfig) => getAxios().post(`${endpoint}/${id}/summarize`, {}, axiosConfig),
  magicText: (data, axiosConfig) => getAxios().post(`${endpoint}/magic-text`, data, axiosConfig),
  outOfRange: ({ id, limit }, axiosConfig) =>
    getAxios()
      .get(`${endpoint}/${id}/out-of-range`, { ...axiosConfig, params: { limit } })
      .then(extractData),
  checkSummaryMessage: (ticketId, axiosConfig) =>
    getAxios().post(`${endpoint}/check-summary`, { ticketId }, axiosConfig).then(extractData),
}
