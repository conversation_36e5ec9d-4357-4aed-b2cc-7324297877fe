import createApi, { extractData, getAxios } from '../../utils/apiResource'

const endpoint = '/credit-movements'

export default {
  ...createApi(endpoint),

  balance: (data, axiosConfig) => getAxios().post(`${endpoint}/balance`, data, axiosConfig).then(extractData),
  balances: (query, axiosConfig) =>
    getAxios()
      .get(`${endpoint}/balances`, { ...axiosConfig, params: query })
      .then(extractData),
  balancesV2: (query, axiosConfig) =>
    getAxios()
      .get(`${endpoint}/v2/balances`, { ...axiosConfig, params: query })
      .then(extractData),
  export: (query, axiosConfig) =>
    getAxios()
      .get(`${endpoint}/export`, {
        ...axiosConfig,
        params: query,
        responseType: 'blob',
      })
      .then(extractData),
  totalServices: (query, axiosConfig) =>
    getAxios()
      .get(`${endpoint}/total-services`, { ...axiosConfig, params: query })
      .then(extractData),
}
