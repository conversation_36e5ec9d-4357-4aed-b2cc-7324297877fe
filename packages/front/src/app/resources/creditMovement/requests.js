import { useFetchManyRequestWithGlobalState } from '../../hooks/useFetchManyRequest'
import { useFetchOneRequestWithLocalState } from '../../hooks/useFetchOneRequest'
import creditMovementApi from './api'
import { denormalizeList, denormalizeSingle, normalizeList, normalizeSingle } from './schema'
import { useSaveRequestWithLocalState } from '../../hooks/useSaveRequest'
import { useRequest } from '../../hooks/useRequest'

export const useFetchManyCreditMovements = (query, setEntitiesIncludes) =>
  useFetchManyRequestWithGlobalState(
    'creditMovementsIndex',
    creditMovementApi.fetchMany,
    query,
    normalizeList,
    denormalizeList,
    setEntitiesIncludes,
  )

export const useFetchManyCreditMovementsServices = (query, setEntitiesIncludes) =>
  useFetchManyRequestWithGlobalState(
    'creditMovementsServicesIndex',
    creditMovementApi.totalServices,
    query,
    normalizeList,
    denormalizeList,
    setEntitiesIncludes,
  )

export const useFetchOneCreditMovement = (id, query) =>
  useFetchOneRequestWithLocalState(creditMovementApi.fetchById, id, query, normalizeSingle, denormalizeSingle)

export const useCreateCreditMovement = () =>
  useSaveRequestWithLocalState(creditMovementApi.create, normalizeSingle, denormalizeSingle)

export const useUpdateCreditMovement = () =>
  useSaveRequestWithLocalState(creditMovementApi.updateById, normalizeSingle, denormalizeSingle)

export const useDeleteCreditMovement = (id) => useRequest(creditMovementApi.deleteById, id)
