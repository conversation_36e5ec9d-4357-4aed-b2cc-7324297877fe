import { getAxios } from '../../utils/apiResource'

const endpoint = '/provider'

export default {
  mmliteEnabled: (serviceId, axiosConfig) =>
    getAxios()
      .get(`${endpoint}/mmlite/${serviceId}/enabled`, axiosConfig)
      .then((response) => response.data),
  dialogSetWabaAccount: (serviceId, axiosConfig) =>
    getAxios()
      .post(`${endpoint}/dialog/waba-account/${serviceId}`, {}, axiosConfig)
      .then((response) => response.data),
}
