import { getAxios } from '../../utils/apiResource'
import { BotSimulatorContact, BotSimulatorMessage } from '../../types/BotSimulator'

const endpoint = '/bot-simulators'

interface GetContactMessagesByBotIdParams {
  botId: string
}

interface GetContactMessagesByBotIdResponse {
  contact: BotSimulatorContact
  messages: BotSimulatorMessage[]
}

interface SendEventParams {
  botId: string
  data: {
    type: 'text_contact' | 'select_trigger' | 'select_condition'
    text?: string
    id?: string
    selectedValue?: string | boolean
  }
}

interface SendEventResponse {}

interface RestartSimulatorParams {
  botId: string
}

interface RestartSimulatorResponse {
  restartDate: string
}

export default {
  getContactMessagesByBotId: ({ botId }: GetContactMessagesByBotIdParams) =>
    getAxios()
      .get<GetContactMessagesByBotIdResponse>(`${endpoint}/contact-messages/${botId}`)
      .then((res) => res.data),
  sendEvent: ({ botId, data }: SendEventParams) =>
    getAxios()
      .post<SendEventResponse>(`${endpoint}/send-event/${botId}`, data)
      .then((res) => res.data),
  restartSimulator: ({ botId }: RestartSimulatorParams) =>
    getAxios()
      .post<RestartSimulatorResponse>(`${endpoint}/restart-simulator/${botId}`, {})
      .then((res) => res.data),
}
