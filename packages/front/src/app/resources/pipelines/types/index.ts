export type Stage = {
  name: string
  position: number
  accountId: string
  id: string
  cards: Array<{
    id: string
    title: string
    contactId: string
    pipelineId: string
    order: number
    pipelineStageId: string
    description: string
    contact: {
      updatedAt: Date
      name: string
      lastMessage: any
      avatar: any
      thumbAvatar: any
      internalName: string
    }
    isArchived: boolean
    movements: any
  }>
  statuses: Status[]
}

export type Status = {
  name: string
  position: number
}

export type Reason = {
  name: string
  position: number
}

export type Department = {
  accountId: string
  archiveAt: Date | null
  createdAt: Date
  distributionId: string
  id: string
  name: string
  updatedAt: Date | null
}

export interface Product {
  name: string
  ammount: number
  value: number
}

export interface CreatePipelineRequest {
  name: string
  stages: Stage[]
  departments: Department[]
}

export interface CardRequest {
  title: string
  contactId: string
  pipelineId: string
  order: number
  pipelineStageId: string
  description: string
  products: Product[]
}

export interface CreateStageRequest {
  name: string
  pipelineId: string
  position: number
  statuses?: Status[]
  reasons?: Reason[]
}

export interface PipelineItem {
  name: string
  id: string
}

export interface PipelinesResponse {
  data: Array<{
    name: string
    id: string
  }>
  total: number
  currentPage: number
}
