import { denormalize, normalize, schema } from 'normalizr'
import { ENTITY_NAME } from './constants'

const pipelineSchema = new schema.Entity(ENTITY_NAME)
const pipelinesListSchema = new schema.Array(pipelineSchema)

const normalizeList = (pipelines) => normalize(pipelines, pipelinesListSchema)
const normalizeSingle = (pipeline) => normalizeList([pipeline])
const denormalizeList = (ids, entities) => denormalize(ids, pipelinesListSchema, entities)
const denormalizeSingle = (id, entities) => denormalize(id, pipelineSchema, entities)

export { pipelineSchema, pipelinesListSchema, normalizeList, normalizeSingle, denormalizeList, denormalizeSingle }
