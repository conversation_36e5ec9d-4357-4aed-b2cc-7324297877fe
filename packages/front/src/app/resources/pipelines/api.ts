import createApi, { extractData, getAxios } from '../../utils/apiResource'
import { CardRequest, CreatePipelineRequest, PipelinesResponse, CreateStageRequest } from './types'

const ENDPOINT = '/pipelines'

export default {
  ...createApi(ENDPOINT),
  getPipelines: (query, axiosConfig) =>
    getAxios()
      .get<PipelinesResponse>(`${ENDPOINT}/withTotals`, { ...axiosConfig, params: query })
      .then(extractData),
  getPipelinesbyId: (id, payload, axiosConfig) =>
    getAxios().post(`${ENDPOINT}/${id}`, payload, axiosConfig).then(extractData),
  deletePipeline: (id: number, axiosConfig) =>
    getAxios()
      .delete(`${ENDPOINT}/${id}`, { ...axiosConfig })
      .then(extractData),
  exportPipeline: (id: number, payload, axiosConfig) =>
    getAxios().post(`${ENDPOINT}/${id}/export`, payload, axiosConfig).then(extractData),
  archivePipeline: (id: number, payload, axiosConfig) =>
    getAxios().post(`${ENDPOINT}/${id}/archive`, payload, axiosConfig).then(extractData),
  createPipeline: (payload: CreatePipelineRequest, axiosConfig) =>
    getAxios().post(`${ENDPOINT}`, payload, axiosConfig).then(extractData),
  updatePipeline: (id: string, payload: CreatePipelineRequest, axiosConfig) =>
    getAxios().put(`${ENDPOINT}/${id}`, payload, axiosConfig).then(extractData),
  createStage: (payload: CreateStageRequest, axiosConfig) =>
    getAxios().post(`${ENDPOINT}/stage`, payload, axiosConfig).then(extractData),
  updateStage: (id: string, payload: CreateStageRequest, axiosConfig) =>
    getAxios().put(`${ENDPOINT}/stage/${id}`, payload, axiosConfig).then(extractData),
  deleteStage: (id: string, payload, axiosConfig) =>
    getAxios().put(`${ENDPOINT}/delete-stage/${id}`, payload, axiosConfig).then(extractData),
  createCard: (payload: CardRequest, axiosConfig) => getAxios().post(`/cards`, payload, axiosConfig).then(extractData),
  getCards: (query, axiosConfig) =>
    getAxios()
      .get(`/cards`, { ...axiosConfig, params: query })
      .then(extractData),
  getPostCards: (query, axiosConfig) => getAxios().post(`/cards/getCards`, query, axiosConfig),
  getCardById: (id: string, query, axiosConfig) =>
    getAxios()
      .get(`/cards/${id}`, { ...axiosConfig, params: query })
      .then(extractData),
  updateCard: (id: string, payload, axiosConfig) =>
    getAxios().put(`/cards/${id}`, payload, axiosConfig).then(extractData),
  updateCardOrder: (id: string, payload, axiosConfig) =>
    getAxios().put(`/cards/order/${id}`, payload, axiosConfig).then(extractData),
  deleteCard: (id: number, axiosConfig) =>
    getAxios()
      .delete(`cards/${id}`, { ...axiosConfig })
      .then(extractData),
  getStatuses: (id: number, axiosConfig) =>
    getAxios()
      .get(`${ENDPOINT}/statuses/${id}`, { ...axiosConfig })
      .then(extractData),
  comment: (payload, axiosConfig) => getAxios().post(`/cards/comment`, payload, axiosConfig).then(extractData),
  deleteComment: (id: number, axiosConfig) => getAxios().delete(`/cards/comment/${id}`, axiosConfig).then(extractData),
  owners: (payload, axiosConfig) => getAxios().post(`/cards/owners`, payload, axiosConfig).then(extractData),
  createAutomation: (payload, axiosConfig) =>
    getAxios().post(`${ENDPOINT}/automations`, payload, axiosConfig).then(extractData),
  updateAutomation: (id: string, payload, axiosConfig) =>
    getAxios().put(`${ENDPOINT}/automations/${id}`, payload, axiosConfig).then(extractData),
  getAutomation: (id: string, axiosConfig) =>
    getAxios()
      .get(`${ENDPOINT}/automations/${id}`, { ...axiosConfig })
      .then(extractData),
  duplicate: (id: number, axiosConfig) =>
    getAxios()
      .post(`${ENDPOINT}/duplicate/${id}`, { ...axiosConfig })
      .then(extractData),
  getStatus: (id: string, payload, axiosConfig) =>
    getAxios()
      .get(`${ENDPOINT}/status/${id}`, { ...axiosConfig })
      .then(extractData),
  deleteStatus: (id: string, payload, axiosConfig) =>
    getAxios().post(`${ENDPOINT}/delete-status/${id}`, payload, axiosConfig).then(extractData),
}
