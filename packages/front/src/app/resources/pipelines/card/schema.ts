import { denormalize, normalize, schema } from 'normalizr'
import { ENTITY_NAME } from './constants'

const cardSchema = new schema.Entity(ENTITY_NAME)
const cardsListSchema = new schema.Array(cardSchema)

const normalizeList = (cards) => normalize(cards, cardsListSchema)
const normalizeSingle = (card) => normalizeList([card])
const denormalizeList = (ids, entities) => denormalize(ids, cardsListSchema, entities)
const denormalizeSingle = (id, entities) => denormalize(id, cardSchema, entities)

export { cardSchema, cardsListSchema, normalizeList, normalizeSingle, denormalizeList, denormalizeSingle }
