import { denormalize, normalize, schema } from 'normalizr'
import { ENTITY_NAME } from './constants'

const cardOnDemandSchema = new schema.Entity(ENTITY_NAME)
const cardsListSchema = new schema.Array(cardOnDemandSchema)

const normalizeList = (cards) => normalize(cards, cardsListSchema)
const normalizeSingle = (card) => normalizeList([card])
const denormalizeList = (ids, entities) => denormalize(ids, cardsListSchema, entities)
const denormalizeSingle = (id, entities) => denormalize(id, cardOnDemandSchema, entities)

export { cardOnDemandSchema, cardsListSchema, normalizeList, normalizeSingle, denormalizeList, denormalizeSingle }
