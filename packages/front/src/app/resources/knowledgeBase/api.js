import createApi, { extractData, getAxios } from '../../utils/apiResource'

const ENDPOINT = '/knowledge-base'

export default {
  ...createApi(ENDPOINT),
  suggestByMessage: (messageId, outsideTheKnowledgeBase, axiosConfig) =>
    getAxios()
      .post(`${ENDPOINT}/suggest-by-message`, { messageId, outsideTheKnowledgeBase }, axiosConfig)
      .then(extractData),
  suggestByTicket: (ticketId, outsideTheKnowledgeBase, axiosConfig) =>
    getAxios()
      .post(`${ENDPOINT}/suggest-by-ticket`, { ticketId, outsideTheKnowledgeBase }, axiosConfig)
      .then(extractData),
  verifyCopilot: (query, axiosConfig) =>
    getAxios()
      .get(`${ENDPOINT}/`, { ...axiosConfig, params: query })
      .then(extractData),
}
