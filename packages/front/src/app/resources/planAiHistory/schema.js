import { denormalize, normalize, schema } from 'normalizr'
import { ENTITY_NAME } from './constants'

const planAiHistorySchema = new schema.Entity(ENTITY_NAME)
const planAiHistoryListSchema = new schema.Array(planAiHistorySchema)

const normalizeList = (planAiHistory) => normalize(planAiHistory, planAiHistoryListSchema)
const normalizeSingle = (planAiHistory) => normalizeList([planAiHistory])
const denormalizeList = (ids, entities) => denormalize(ids, planAiHistoryListSchema, entities)
const denormalizeSingle = (id, entities) => denormalize(id, planAiHistorySchema, entities)

export {
  planAiHistorySchema,
  planAiHistoryListSchema,
  normalizeList,
  normalizeSingle,
  denormalizeList,
  denormalizeSingle,
}
