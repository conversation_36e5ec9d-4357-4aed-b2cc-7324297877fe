import React, { useMemo, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'
import { Input, InputProps } from '../../ui/input'
import { Numeric, getNumericErrorMessageKey, getNumericWarningMessageKey } from '../../../../../utils/Numeric'

type MaskedNumericInputProps = Omit<InputProps, 'value'> & {
  value: { value: string }
  showInnerErrorMessage?: boolean
  showInnerWarningMessage?: boolean
  min?: number
  max?: number
}

function MaskedNumericInput({
  showInnerErrorMessage = true,
  showInnerWarningMessage = true,
  min,
  max,
  ...props
}: MaskedNumericInputProps) {
  const { t } = useTranslation('customFields')

  const errorRef = useRef<HTMLElement>(null)
  const warningRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  function clearWarning() {
    if (!warningRef.current) {
      return
    }
    warningRef.current.innerHTML = ''
  }

  function setWarning() {
    if (showInnerWarningMessage && (min || max)) {
      warningRef.current.innerHTML = t(getNumericWarningMessageKey(min, max), { min, max })
    }
  }

  function verifyValue() {
    if (typeof props.value === 'string') return props.value
    return props.value?.value ?? ''
  }

  function handleInputChange(event: React.ChangeEvent<HTMLInputElement>) {
    clearWarning()
    clearError()

    const { value } = event.target

    if (!value.length) return setWarning()

    if (!errorRef.current || !showInnerErrorMessage) {
      return
    }

    const number = new Numeric(Number(value), min, max)

    if (!number.isValid()) return (errorRef.current.innerHTML = t(getNumericErrorMessageKey(min, max), { min, max }))
  }

  useEffect(() => {
    setWarning()
  }, [])

  const formatedValue = useMemo(() => verifyValue() ?? '', [props.value])

  return (
    <>
      <Input
        {...props}
        type="number"
        value={formatedValue}
        onChange={(event) => {
          handleInputChange(event)

          const customEvent = {
            ...event,
            min,
            max,
          }
          props.onChange?.(customEvent)
        }}
      />
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
      {showInnerWarningMessage && (
        <FormFeedback>
          <span style={{ color: '#6C757D' }} ref={warningRef} />
        </FormFeedback>
      )}
    </>
  )
}
export default MaskedNumericInput
