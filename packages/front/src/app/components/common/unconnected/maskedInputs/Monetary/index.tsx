import React, { useState, useRef, useMemo, useEffect } from 'react'
import styled from 'styled-components'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'
import { Input, InputProps } from '../../ui/input'
import Select from '../../Select'
import currencyOptions from './currency-options.json'
import {
  formatMonetaryValue,
  getMonetaryErrorMessageKey,
  getMonetaryWarningMessageKey,
  Monetary,
} from '../../../../../utils/Monetary'
import { SmallGroupSelect } from '../styles'

type MaskedMonetaryInputProps = Omit<InputProps, 'value'> & {
  value: { value: string; identifier: string }
  showInnerErrorMessage?: boolean
  showInnerWarningMessage?: boolean
  min?: number
  max?: number
}

function MaskedMonetaryInput({
  showInnerErrorMessage = true,
  showInnerWarningMessage = true,
  min,
  max,
  ...props
}: MaskedMonetaryInputProps) {
  const { t } = useTranslation('customFields')

  const [currency, setCurrency] = useState(
    currencyOptions.find((option) => option.code === props.value?.identifier) ?? currencyOptions[0],
  )
  const [changeValue, setChangeValue] = useState(false)

  const errorRef = useRef<HTMLElement>(null)
  const warningRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  function clearWarning() {
    if (!warningRef.current) {
      return
    }
    warningRef.current.innerHTML = ''
  }

  function setWarning() {
    if (showInnerWarningMessage && (min || max))
      warningRef.current.innerHTML = t(getMonetaryWarningMessageKey(min, max), {
        min: formatMonetaryValue(min.toString(), currency.code),
        max: formatMonetaryValue(max.toString(), currency.code),
        symbol: '',
      })
  }

  function verifyValue() {
    if (typeof props.value === 'string') return props.value
    return props.value?.value ?? ''
  }

  function formatCurrency(value: string) {
    const numericValue = value.replace(/\D/g, '')

    if (!numericValue.length) return { formatted: '', numeric: '' }

    const integerPart = numericValue.slice(0, -2)
    const decimalPart = numericValue.slice(-2)
    const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, currency.thousandSeparator)
    return {
      formatted: `${currency.symbol} ${formattedIntegerPart}${currency.decimalSeparator}${decimalPart}`,
      numeric: `${integerPart}.${decimalPart}`,
    }
  }

  function handleCurrencyChange(event: React.ChangeEvent<HTMLSelectElement>) {
    setChangeValue(true)

    const selectedCurrency = currencyOptions.find((option) => option.code === event.target.value['value'])

    if (selectedCurrency) {
      setCurrency(selectedCurrency)
    }
  }

  function handleInputChange(event: React.ChangeEvent<HTMLInputElement>) {
    clearWarning()
    clearError()

    const { value } = event.target

    const formattedValue = formatCurrency(value)
    event.target.value = formattedValue.formatted

    if (value.length <= 4) return setWarning()

    if (!errorRef.current || !showInnerErrorMessage) {
      return
    }

    const monetary = new Monetary(Number(formattedValue.numeric), min, max)

    if (!monetary.isValid())
      return (errorRef.current.innerHTML = t(getMonetaryErrorMessageKey(min, max), {
        min: formatMonetaryValue(min.toString(), currency.code),
        max: formatMonetaryValue(max.toString(), currency.code),
        symbol: '',
      }))
  }

  function handleCustomFieldChange() {
    const customInputChangeEvent = {
      target: {
        value: formattedValue,
        ...(props.id && { id: props.id }),
      },
      identifier: currency.code,
      min,
      max,
    }

    props.onChange?.(customInputChangeEvent as unknown as React.ChangeEvent<HTMLInputElement>)
  }

  useEffect(() => {
    setCurrency(currencyOptions.find((option) => option.code === props.value?.identifier) ?? currencyOptions[0])
  }, [props.value])

  useEffect(() => {
    setWarning()
  }, [currency])

  const formattedValue = useMemo(() => formatCurrency(verifyValue()).formatted, [props.value, currency])

  useEffect(() => {
    if (changeValue) {
      setChangeValue(false)
      handleCustomFieldChange()
    }
  }, [formattedValue])

  return (
    <>
      <div style={{ display: 'flex' }}>
        <Input
          {...props}
          style={{ width: '98%' }}
          value={formattedValue}
          onChange={(event) => {
            handleInputChange(event)

            if (!event.target.value?.length) clearError()

            const customEvent = {
              ...event,
              identifier: currency.code,
              min,
              max,
            }
            props.onChange?.(customEvent)
          }}
        />

        <div style={{ width: 128 }}>
          <SmallGroupSelect>
            <Select
              style={{ paddingLeft: 0 }}
              options={currencyOptions.map((option) => ({ value: option.code, label: option.code }))}
              value={{ value: currency.code, label: currency.code }}
              onChange={(value) => handleCurrencyChange({ target: { value } } as React.ChangeEvent<HTMLSelectElement>)}
              onBlur={(event) => {
                props.onBlur?.(event)
              }}
              getOptionLabel={(o) => o.label}
              getOptionValue={(o) => o.value}
              valueKey="value"
              labelKey="label"
              isClearable={false}
            />
          </SmallGroupSelect>
        </div>
      </div>
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
      {showInnerWarningMessage && (
        <FormFeedback>
          <span style={{ color: '#6C757D' }} ref={warningRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedMonetaryInput
