import React, { useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'
import Select from '../../Select'

interface Option {
  value: string
  label: string
}

function MaskedSelectInput({ options, showInnerErrorMessage = true, ...props }) {
  const { t } = useTranslation('customFields')

  const errorRef = useRef<HTMLElement>(null)

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  const handleChange = (selectedOption: Option | Option[] | null) => {
    if (!selectedOption && errorRef.current && props.required) {
      return (errorRef.current.innerHTML = t('KEY_SELECT_OPTION'))
    }

    clearError()

    if (props.onChange) {
      props.onChange(selectedOption)
    }
  }

  return (
    <>
      <Select {...props} options={options} onChange={handleChange} data-testid={'lista'} />
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedSelectInput
