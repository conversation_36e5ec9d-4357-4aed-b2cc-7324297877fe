import React, { useMemo, useRef, useState, useEffect } from 'react'
import { format, useMask } from '@react-input/mask'
import { useTranslation } from 'react-i18next'
import { FormFeedback } from 'reactstrap'
import { SmallGroupSelect } from '../styles'
import Select from '../../Select'

import { Input, InputProps } from '../../ui/input'
import { Phone } from '../../../../../utils/Phone'
import countryCodes from './codes.json'

type MaskedPhoneInputProps = Omit<InputProps, 'value'> & {
  value: { value: string; identifier: string }
  showInnerErrorMessage?: boolean
}

function MaskedPhoneInput({ showInnerErrorMessage = true, ...props }: MaskedPhoneInputProps) {
  const { t } = useTranslation('customFields')
  const errorRef = useRef<HTMLElement>(null)

  const [selectedCountryCode, setSelectedCountryCode] = useState(
    props.value?.identifier?.trim() ? props.value.identifier : '+55',
  )

  const [maskOptions, setMaskOptions] = useState({
    mask: countryCodes.find((country) => country.code === selectedCountryCode)?.mask,
    replacement: { '#': /\d/ },
  })
  const [changeValue, setChangeValue] = useState(false)

  useEffect(() => {
    setSelectedCountryCode(props.value?.identifier || '+55')
  }, [props.value])

  useEffect(() => {
    const selectedCountry = countryCodes.find((country) => country.code === selectedCountryCode)
    if (selectedCountry) {
      setMaskOptions({
        mask: selectedCountry.mask,
        replacement: { '#': /\d/ },
      })
    }
  }, [selectedCountryCode])

  function clearError() {
    if (!errorRef.current) {
      return
    }
    errorRef.current.innerHTML = ''
  }

  function verifyValue() {
    if (typeof props.value === 'string') return props.value
    return props.value?.value ?? ''
  }

  function handleCountryCodeChange(code: string) {
    setChangeValue(true)
    setSelectedCountryCode(code)
  }

  const inputRef = useMask({
    ...maskOptions,
    onMask({ detail }) {
      if (selectedCountryCode === '+55') {
        let mask = '(##) ####-#####'
        if (detail.value.length > 14) {
          mask = '(##) #####-####'
        }
        setMaskOptions({
          mask,
          replacement: { '#': /\d/ },
        })
      }

      if (!errorRef.current || !showInnerErrorMessage) {
        return
      }

      const phone = new Phone(detail.value, maskOptions.mask, selectedCountryCode)

      if (!phone.isValid()) return (errorRef.current.innerHTML = t('KEY_INVALID_PHONE_NUMBER'))

      clearError()
    },
  })

  function handleCustomFieldChange() {
    const customInputChangeEvent = {
      target: {
        value: '',
        ...(props.id && { id: props.id }),
      },
      identifier: selectedCountryCode,
    }

    props.onChange?.(customInputChangeEvent as unknown as React.ChangeEvent<HTMLInputElement>)
  }

  const formattedValue = useMemo(() => format(verifyValue() ?? '', maskOptions), [props.value, maskOptions])

  useEffect(() => {
    if (changeValue) {
      setChangeValue(false)
      handleCustomFieldChange()
    }
  }, [selectedCountryCode])

  return (
    <>
      <div style={{ display: 'flex' }}>
        <div style={{ width: 128 }}>
          <SmallGroupSelect>
            <Select
              style={{ paddingLeft: 0 }}
              options={countryCodes.map((country) => ({ value: country.code, label: country.code }))}
              value={{ value: selectedCountryCode, label: selectedCountryCode }}
              onChange={(option) => handleCountryCodeChange(option.value)}
              onBlur={(event) => {
                props.onBlur?.(event)
              }}
              getOptionLabel={(option) => option.label}
              getOptionValue={(option) => option.value}
              valueKey="value"
              labelKey="label"
              isClearable={false}
              placeholder=""
            />
          </SmallGroupSelect>
        </div>

        <Input
          {...props}
          style={{ marginLeft: '2%', width: '98%' }}
          ref={inputRef}
          value={formattedValue}
          onChange={(event) => {
            if (!event.target.value?.length) clearError()

            const customEvent = {
              ...event,
              identifier: selectedCountryCode,
            }
            props.onChange?.(customEvent)
          }}
        />
      </div>
      {showInnerErrorMessage && (
        <FormFeedback>
          <span ref={errorRef} />
        </FormFeedback>
      )}
    </>
  )
}

export default MaskedPhoneInput
