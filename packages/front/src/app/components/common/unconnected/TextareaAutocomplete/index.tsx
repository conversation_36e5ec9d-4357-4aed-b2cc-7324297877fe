import React from 'react'
import { createGlobalStyle } from 'styled-components'
import TextInput from 'react-autocomplete-input'
import { Textarea } from '../../../common/unconnected/ui/textarea'

export const TEMPLATE_VARIABLES = [
  '{{nome_contato}} - resgata o nome do cliente',
  '{{nome_contato_digisac}} - resgata o nome do cliente salvo na plataforma Digisac',
  '{{nome_contato_agenda}} - resgata o nome do cliente salvo na sua agenda',
  '{{nome_contato_whatsapp}} - resgata o nome do cliente salvo no WhatsApp',
  '{{numero_protocolo}} - resgata o protocolo de atendimento',
  '{{numero_contato}} - resgata o numero do contato',
  '{{saudacao}} - insere uma saudação na mensagem',
  '{{saudacao_maiuscula}} - insere uma saudação na mensagem começando com a letra maiúscula',
]

export const handleAutocompleteSelect = (_: string, slug: string) => {
  const match = slug.match(/\{\{\s*([a-zA-Z0-9_]+)\s*\}\}/)

  return `{{${match[1]}}}` || ''
}

const AutocompleteGlobalStyle = createGlobalStyle`
  ul.react-autocomplete-input {
    position: absolute;
    top: auto !important;
    left: 0px !important;
    z-index: 20000;

    margin: 0;
    padding: 0;
    width: 100% !important;
    min-width: 100%;
    max-height: 275px !important;

    border: 1px solid #e5e5e5;
    border-radius: 16px;
    background: #ffffff;
    box-shadow: 0px 4px 8px 0px rgba(166, 184, 230, 0.32);

    font-size: 14px;
    line-height: 150%;
    list-style: none;

    clip-path: inset(0 0 0 0 round 16px);
    overflow-y: auto;
  }

  ul.react-autocomplete-input::before {
    content: "Lista de variáveis";

    font-size: 12px;
    padding: 8px 16px 4px;
  }
  
  ul.react-autocomplete-input > li {
    cursor: pointer;

    padding: 12px 16px;
    color: #24272d;
  }

  ul.react-autocomplete-input > li.active {
    background: #e1edf8;
  }
`
export function TextareaAutocomplete(props: React.ComponentProps<typeof TextInput>) {
  return (
    <div style={{ position: 'relative' }}>
      <AutocompleteGlobalStyle />
      <TextInput matchAny={true} Component={Textarea} {...props} maxOptions={20} />
    </div>
  )
}
