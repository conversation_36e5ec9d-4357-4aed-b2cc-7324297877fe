/**
 * Based on <PERSON><PERSON>'s sample:
 * https://github.com/facebook/lexical/blob/main/packages/lexical-playground/src/nodes/MentionNode.ts
 */

import {
  $applyNodeReplacement,
  type DOMConversionMap,
  type DOMConversionOutput,
  type DOMExportOutput,
  type EditorConfig,
  type LexicalNode,
  type NodeKey,
  type SerializedTextNode,
  type Spread,
  TextNode,
} from 'lexical'

export type SerializedMentionNode = Spread<
  {
    id: string
  },
  SerializedTextNode
>

function $convertMentionElement(domNode: HTMLElement): DOMConversionOutput | null {
  const textContent = domNode.textContent
  const id = domNode.getAttribute('data-lexical-mention-id') ?? ''

  if (textContent !== null) {
    const node = $createMentionNode(id, textContent)
    return {
      node,
    }
  }

  return null
}

const mentionStyle = 'color: rgb(60, 102, 185);'
export class MentionNode extends TextNode {
  __id: string

  static getType(): string {
    return 'mention'
  }

  static clone(node: MentionNode): MentionNode {
    return new MentionNode(node.__id, node.__text, node.__key)
  }

  static importJSON(serializedNode: SerializedMentionNode): MentionNode {
    const node = $createMentionNode(serializedNode.id, serializedNode.text)
    return node.updateFromJSON(serializedNode)
  }

  constructor(id: string = '', text: string, key?: NodeKey) {
    super(text, key)
    this.__id = id
  }

  exportJSON(): SerializedMentionNode {
    return {
      ...super.exportJSON(),
      id: this.__id,
    }
  }

  exportText(): string {
    return `<${this.__id}:[${this.__text}]>`
  }

  createDOM(config: EditorConfig): HTMLElement {
    const dom = super.createDOM(config)
    dom.style.cssText = mentionStyle
    dom.className = 'mention'
    dom.spellcheck = false
    return dom
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement('span')
    element.setAttribute('data-lexical-mention', 'true')
    element.setAttribute('data-lexical-mention-id', this.__id)
    element.textContent = this.__text
    return { element }
  }

  static importDOM(): DOMConversionMap | null {
    return {
      span: (domNode: HTMLElement) => {
        if (!domNode.hasAttribute('data-lexical-mention')) {
          return null
        }
        return {
          conversion: $convertMentionElement,
          priority: 1,
        }
      },
    }
  }

  isTextEntity(): true {
    return true
  }

  canInsertTextBefore(): boolean {
    return false
  }

  canInsertTextAfter(): boolean {
    return false
  }
}

export function $createMentionNode(id: string = '', text: string): MentionNode {
  const mentionNode = new MentionNode(id, text)
  mentionNode.setMode('segmented').toggleDirectionless()
  return $applyNodeReplacement(mentionNode)
}

export function $isMentionNode(node: LexicalNode | null | undefined): node is MentionNode {
  return node instanceof MentionNode
}
