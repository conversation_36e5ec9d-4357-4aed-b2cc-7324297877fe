import { $createParagraphNode, $createTextNode, $getRoot, $isElementNode } from 'lexical'
import { $createMentionNode, MentionNode } from './mention-node'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { useEffect } from 'react'

export const serializeNode = (node: any): string => {
  if (node instanceof MentionNode) {
    return node.exportText()
  }
  if ($isElementNode(node)) {
    return node.getChildren().map(serializeNode).join('')
  }
  return node.getTextContent()
}

const parsePlainTextToNodes = (text: string): Array<any> => {
  const regex = /<([^:\[\]>]+):\[([^\]]+)\]>/g
  const nodes: any[] = []
  let lastIndex = 0
  let match

  while ((match = regex.exec(text)) !== null) {
    const [fullMatch, id, display] = match
    const matchStart = match.index
    const matchEnd = matchStart + fullMatch.length

    if (lastIndex < matchStart) {
      nodes.push($createTextNode(text.slice(lastIndex, matchStart)))
    }

    nodes.push($createMentionNode(id, display))
    lastIndex = matchEnd
  }

  if (lastIndex < text.length) {
    nodes.push($createTextNode(text.slice(lastIndex)))
  }

  return nodes
}

export const SetValuePlugin = ({ value }: { value: string }): JSX.Element | null => {
  const [editor] = useLexicalComposerContext()

  useEffect(() => {
    if (typeof value !== 'string') return

    editor.getEditorState().read(() => {
      const root = $getRoot()
      const currentSerialized = serializeNode(root)

      if (value !== currentSerialized) {
        editor.update(() => {
          const root = $getRoot()
          root.clear()

          const paragraph = $createParagraphNode()
          const nodes = parsePlainTextToNodes(value)
          paragraph.append(...nodes)
          root.append(paragraph)

          paragraph.select()
        })
      }
    })
  }, [value, editor])

  return null
}
