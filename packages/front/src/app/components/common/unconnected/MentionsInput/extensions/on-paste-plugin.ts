import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import {
  $createLineBreakNode,
  $createTabNode,
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_CRITICAL,
  LexicalEditor,
  PASTE_COMMAND,
  PASTE_TAG,
} from 'lexical'
import { useEffect } from 'react'

const onPasteText = (clipboardData: DataTransfer, editor: LexicalEditor) => {
  editor.update(
    () => {
      const selection = $getSelection()
      if (clipboardData != null && selection !== null) {
        const text = clipboardData.getData('text/plain') || clipboardData.getData('text/uri-list')
        if (text != null) {
          if ($isRangeSelection(selection)) {
            const parts = text.split(/(\r?\n|\t)/)
            if (parts[parts.length - 1] === '') {
              parts.pop()
            }
            for (let i = 0; i < parts.length; i++) {
              const currentSelection = $getSelection()
              if ($isRangeSelection(currentSelection)) {
                const part = parts[i]
                if (part === '\n' || part === '\r\n') {
                  currentSelection.insertNodes([$createLineBreakNode()])
                } else if (part === '\t') {
                  currentSelection.insertNodes([$createTabNode()])
                } else {
                  currentSelection.insertText(part)
                }
              }
            }
          } else {
            selection.insertRawText(text)
          }
        }
      }
    },
    {
      tag: PASTE_TAG,
    },
  )
}

export const OnPastePlugin = ({ onPaste }) => {
  const [editor] = useLexicalComposerContext()

  useEffect(() => {
    return editor.registerCommand(
      PASTE_COMMAND,
      (event: ClipboardEvent) => {
        const { clipboardData } = event
        if (!clipboardData || clipboardData.types.includes('application/x-lexical-editor')) {
          return false
        }

        if (clipboardData.files.length > 0) {
          onPaste(event)
          return false
        } else {
          event.preventDefault()
          onPasteText(clipboardData, editor)
          return true
        }
      },
      COMMAND_PRIORITY_CRITICAL,
    )
  }, [editor, onPaste])

  return null
}
