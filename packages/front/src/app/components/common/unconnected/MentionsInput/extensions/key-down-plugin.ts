import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { KEY_DOWN_COMMAND } from 'lexical'
import { useEffect } from 'react'

export const KeyDownPlugin = ({ onKeyDown, isMenuOpenRef }) => {
  const [editor] = useLexicalComposerContext()

  useEffect(() => {
    return editor.registerCommand(
      KEY_DOWN_COMMAND,
      (event) => {
        if (event.key === 'Enter' && isMenuOpenRef.current) {
          return false
        }
        onKeyDown(event)
        return event.key === 'Enter'
      },
      4,
    )
  }, [editor, onKeyDown])

  return null
}
