/**
 * Based on <PERSON><PERSON>'s sample:
 * https://github.com/facebook/lexical/blob/main/packages/lexical-playground/src/plugins/MentionsPlugin/index.tsx
 */

import type { JSX } from 'react'

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import {
  LexicalTypeaheadMenuPlugin,
  MenuOption,
  MenuTextMatch,
  useBasicTypeaheadTriggerMatch,
} from '@lexical/react/LexicalTypeaheadMenuPlugin'
import { TextNode } from 'lexical'
import { useCallback, useEffect, useState } from 'react'
import * as React from 'react'
import * as ReactDOM from 'react-dom'
import { $createMentionNode } from './mention-node'
import { useTranslation } from 'react-i18next'
import Avatar from '../../../connected/Avatar'

const LENGTH_LIMIT = 75
const ALIAS_LENGTH_LIMIT = 50

const PUNC = '\\.,\\+\\*\\?\\$\\@\\|#{}\\(\\)\\^\\-\\[\\]\\\\/!%\'"~=<>_:;'
const VALID_CHARS = '[^@' + PUNC + '\\s]'
const VALID_JOINS = '(?:' + '\\.[ |$]|' + ' |' + '[' + PUNC + ']|' + ')'
const AtSignMentionsRegex = new RegExp(
  '(^|\\s|\\()(@((?:' + VALID_CHARS + VALID_JOINS + '){0,' + LENGTH_LIMIT + '})' + ')$',
)
const AtSignMentionsRegexAliasRegex = new RegExp(
  '(^|\\s|\\()(@((?:' + VALID_CHARS + '){0,' + ALIAS_LENGTH_LIMIT + '})' + ')$',
)

function checkForAtSignMentions(text: string): MenuTextMatch | null {
  let match = AtSignMentionsRegex.exec(text) || AtSignMentionsRegexAliasRegex.exec(text)
  if (match !== null) {
    const maybeLeadingWhitespace = match[1]
    const matchingString = match[3]
    return {
      leadOffset: match.index + maybeLeadingWhitespace.length,
      matchingString,
      replaceableString: match[2],
    }
  }
  return null
}

function getPossibleQueryMatch(text: string): MenuTextMatch | null {
  return checkForAtSignMentions(text)
}

class MentionTypeaheadOption extends MenuOption {
  id: string
  display: string
  picture: string

  constructor(id: string, display: string, picture: string) {
    super(display)
    this.id = id
    this.display = display
    this.picture = picture
  }
}

function MentionsTypeaheadMenuItem({
  index,
  isSelected,
  onClick,
  onMouseEnter,
  option,
}: {
  index: number
  isSelected: boolean
  onClick: () => void
  onMouseEnter: () => void
  option: MentionTypeaheadOption
}) {
  const { key, display, picture } = option
  const className = `item${isSelected ? ' selected' : ''}`
  const contact = {
    avatar: {
      url: picture,
    },
  }

  return (
    <li
      key={key}
      tabIndex={-1}
      className={className}
      ref={option.setRefElement}
      role="option"
      aria-selected={isSelected}
      id={'typeahead-item-' + index}
      onMouseEnter={onMouseEnter}
      onClick={onClick}
    >
      <Avatar contact={contact} className="avatar" />
      <span>{display}</span>
    </li>
  )
}

export default function MentionsPlugin({ data: handleFetch, isMenuOpenRef }): JSX.Element | null {
  const { t } = useTranslation(['chatPage'])
  const [editor] = useLexicalComposerContext()
  const [queryString, setQueryString] = useState<string | null>(null)
  const [options, setOptions] = useState([])
  const slashTriggerMatch = useBasicTypeaheadTriggerMatch('/', { minLength: 0 })

  const checkForMentionMatch = useCallback(
    (text: string) => {
      const slashMatch = slashTriggerMatch(text, editor)
      if (slashMatch) return null
      return getPossibleQueryMatch(text)
    },
    [editor],
  )

  useEffect(() => {
    if (queryString === null || typeof handleFetch !== 'function') return
    handleFetch(queryString, (results) => {
      const opts = results.map(
        (contact) => new MentionTypeaheadOption(contact.id, contact.display, contact?.avatar?.url),
      )
      setOptions(opts)
    })
  }, [queryString, handleFetch])

  const onSelectOption = useCallback(
    (selectedOption: MentionTypeaheadOption, nodeToReplace: TextNode | null, closeMenu: () => void) => {
      editor.update(() => {
        const mentionNode = $createMentionNode(selectedOption.id, `@${selectedOption.display}`)
        if (nodeToReplace) {
          nodeToReplace.replace(mentionNode)
        }
        mentionNode.select()
        closeMenu()
      })
    },
    [editor],
  )

  return (
    <LexicalTypeaheadMenuPlugin<MentionTypeaheadOption>
      onOpen={() => {
        isMenuOpenRef.current = true
      }}
      onClose={() => {
        isMenuOpenRef.current = false
      }}
      onQueryChange={setQueryString}
      onSelectOption={onSelectOption}
      triggerFn={checkForMentionMatch}
      options={options}
      menuRenderFn={(anchorElementRef, { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex }) =>
        anchorElementRef.current && options.length
          ? ReactDOM.createPortal(
              <div className="mentions-typeahead">
                <ul>
                  {options.map((option, i: number) => (
                    <MentionsTypeaheadMenuItem
                      key={option.key}
                      index={i}
                      isSelected={selectedIndex === i}
                      option={option}
                      onClick={() => {
                        setHighlightedIndex(i)
                        selectOptionAndCleanUp(option)
                      }}
                      onMouseEnter={() => {
                        setHighlightedIndex(i)
                      }}
                    />
                  ))}
                </ul>
                <div
                  style={{
                    fontSize: 12,
                    color: '#999',
                    textAlign: 'center',
                    paddingBlockStart: 10,
                  }}
                >
                  {t('FILTER_CONTACTS_BY_NAME')}
                </div>
              </div>,
              anchorElementRef.current,
            )
          : null
      }
    />
  )
}
