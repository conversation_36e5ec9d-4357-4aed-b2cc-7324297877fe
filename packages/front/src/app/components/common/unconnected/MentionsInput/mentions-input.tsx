import React, { useRef } from 'react'
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin'
import { LexicalComposer } from '@lexical/react/LexicalComposer'
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin'
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin'
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary'
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin'
import { $getRoot, $isElementNode, LexicalEditor } from 'lexical'
import { MentionNode } from './extensions/mention-node'
import MentionsPlugin from './extensions/mentions-plugin'
import { ContentEditable } from '@lexical/react/LexicalContentEditable'
import { MaxLengthPlugin } from './extensions/max-length-plugin'
import { serializeNode, SetValuePlugin } from './extensions/set-value-plugin'
import { KeyDownPlugin } from './extensions/key-down-plugin'
import { OnPastePlugin } from './extensions/on-paste-plugin'
import { useMentionsFetcher } from './mentions-fetcher'
import('./MentionsInput.scss')

const initialConfig = {
  namespace: 'ChatInput',
  theme: {},
  nodes: [MentionNode],
  onError: (_error: Error, _editor: LexicalEditor) => {},
}

export const MentionsInput = ({
  placeholder,
  onChange,
  onPaste,
  value,
  inputRef,
  onKeyDown,
  maxLength,
  currentContact,
}) => {
  const isMenuOpenRef = useRef(false)
  const { id: contactId, serviceId, accountId } = currentContact
  const handleFetch = useMentionsFetcher(contactId, serviceId, accountId)

  const handleChange = (editorState: any) => {
    editorState.read(() => {
      const root = $getRoot()
      const plain = serializeNode(root)
      if (plain === value) return
      onChange({ target: { value: plain } })
    })
  }

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <RichTextPlugin
        contentEditable={
          <div className="editor-wrapper">
            <ContentEditable
              ref={inputRef}
              className={'editor-input'}
              aria-placeholder={placeholder}
              placeholder={<div className={'editor-placeholder'}>{placeholder}</div>}
              role="textbox"
            />
          </div>
        }
        ErrorBoundary={LexicalErrorBoundary}
      />
      <OnPastePlugin onPaste={onPaste} />
      <MaxLengthPlugin maxLength={maxLength} />
      <MentionsPlugin data={handleFetch} isMenuOpenRef={isMenuOpenRef} />
      <HistoryPlugin delay={100} />
      <AutoFocusPlugin />
      <OnChangePlugin onChange={handleChange} />
      <KeyDownPlugin onKeyDown={onKeyDown} isMenuOpenRef={isMenuOpenRef} />
      <SetValuePlugin value={value} />
    </LexicalComposer>
  )
}
