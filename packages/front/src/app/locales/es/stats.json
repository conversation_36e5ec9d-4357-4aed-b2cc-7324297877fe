{"TITLE_STATS_SERVICE": "Estadísticas de servicio", "CREATE_STATS_LABEL_GENERAL": "Promedio general", "CREATE_STATS_LABEL_PER_PERIOD": "<PERSON><PERSON>", "CREATE_STATS_LABEL_USER": "<PERSON>r as<PERSON>", "CREATE_STATS_LABEL_DEPARTMENT": "Por departamento", "CREATE_STATS_LABEL_TOPICS": "Por asunto", "CREATE_STATS_LABEL_SERVICE": "<PERSON><PERSON>", "CREATE_STATS_LABEL_RECEIVED_MESSAGE": "Men<PERSON><PERSON><PERSON>", "CREATE_STATS_LABEL_SENDED_MESSAGE": "Mensajes enviados", "CREATE_STATS_LABEL_TOTAL_MESSAGE": "Total de mensajes", "CREATE_STATS_LABEL_QTD_TOTAL_MESSAGE": "Cantidad total de mensajes (enviados + recibidos)", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_MESSAGE": "Exhibe la cantidad total de mensajes (enviados + recibidos), conforme a los filtros", "CREATE_STATS_LABEL_TICKET_TOTAL": "Total de llamados (abiertos + cerrados)", "CREATE_STATS_LABEL_QTD_TICKET_TOTAL": "Cantidad total de llamados (abiertos + cerrados)", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_TICKET": "Exhibe la cantidad total de llamados (abiertos + cerrados) conforme a los filtros", "CREATE_STATS_LABEL_QTD_SENDED_MESSAGE": "Cantidad de mensajes enviados", "CREATE_STATS_LABEL_QTD_RECEIVED_MESSAGE": "Cantidad de mensajes recibidos", "CREATE_STATS_LABEL_QTD_TICKET_OPEN": "Cantidad de llamados abiertos", "CREATE_STATS_LABEL_QTD_TICKET_CLOSED": "Cantidad de llamados cerrados", "CREATE_STATS_LABEL_AVERAGE_CALL_TIME": "Tiempo promedio de llamados", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME": "Promedio del primer tiempo de espera", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_AVG": "Promedio del tiempo de espera", "CREATE_STATS_LABEL_AVERAGE_CALL_TIME_IN_MINUTES": "Tiempo promedio de llamados en minutos", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_IN_MINUTES": "Tiempo promedio de espera en minutos", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_SUPPORT_IN_MINUTES": "Tiempo promedio de soporte en minutos", "CREATE_STATS_LABEL_PERCENT_MESSAGE_SENT": "Porcentaje de mensajes enviados", "CREATE_STATS_LABEL_PERCENT_MESSAGE_RECEIVER": "Porcentaje de mensajes recibidos", "CREATE_STATS_LABEL_TICKET_OPEN": "Llamados abiertos", "CREATE_STATS_LABEL_TICKET_CLOSED": "Llamados cerrados", "CREATE_STATS_LABEL_MESSAGE_TOTAL": "Total de mensajes del departamento (en los llamados)", "CREATE_STATS_LABEL_MESSAGE_TOTAL_USER": "Total de mensajes del asistente (en los llamados)", "CREATE_STATS_LABEL_AVERAGE_MESSAGE_SENT": "Promedio de mensajes enviados (por llamado)", "CREATE_STATS_LABEL_CONTACTS_TOTAL": "Total de contactos", "CREATE_STATS_LABEL_QTD_CONTACTS_TOTAL": "Cantidad total de contactos", "CREATE_STATS_MESSAGE_AVERAGE_MESSAGE_TICKET": "Exhibe la media del tiempo de duración (desde la apertura hasta el cierre) de cada llamado, conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_PERIOD": "Período entre el primer mensaje del cliente hasta la primera respuesta del asistente, sin contar respuestas automáticas (Bot), conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_TME": "Promedio del tiempo de llamado (derivado de las transferencias del mismo), conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_MESSAGE_SENDED": "Exhibe la cantidad total de mensajes enviados desde la plataforma conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_MESSAGE_RECEIVED": "Exhibe la cantidad total de mensajes recibidos conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_TICKET_OPEN": "Exhibe la cantidad total de llamados abiertos conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_TICKET_CLOSED": "Exhibe la cantidad total de llamados cerrados conforme a los filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_CONTACTS": "Exhibe la cantidad total de contactos conforme a los filtros", "FILTERS_STATS_LAST_DEPARTMENT": "Último departamento", "FILTERS_STATS_LAST_ATTENDANT": "Fue el último asistente de la llamada", "FILTERS_STATS_OPEN_DATE": "Fecha de apertura", "FILTERS_STATS_MESSAGE_INTERVAL_DATES": "Fecha final debe ser mayor que la inicial", "FILTERS_STATS_LABEL_DEPARTMENT": "Departamento", "FILTERS_STATS_LABEL_WITHOUT_DEPARTMENT": "Sin departamento", "FILTERS_STATS_REQUIRED_FIELD_CALL_IN_PROGRESS": "Campo obligatorio porque (Progreso del llamado) está como (Atendido por el departamento)", "FILTER_STATS_REQUIRED_FIELD_ATTENDANT_PARTICIPATION": "Campo obligatorio porque (Participación del asistente) está como (Atendió en el llamado)", "FILTERS_STATS_ATTENDANT_PARTICIPATION": "Participación del asistente", "EXPORT_FILE_NAME": "reporte_estadistica_atencion", "SHOW_NULL_DEPARTMENTS": "Mostrar departamentos nulos", "SHOW_NULL_SUBJECTS": "<PERSON><PERSON> asuntos nulos", "LABEL_SHOW_NULL_CONNECTIONS": "Mostrar conexiones nulas", "LABEL_CONNECTIONS": "Conexión", "LABEL_SUBJECT_MATTER": "<PERSON><PERSON><PERSON>", "LABEL_SHOW_NULL_ATTENDANTS": "<PERSON><PERSON> asist<PERSON> nulos", "LABEL_WITHOUT_ATTENDANT": "Sin asistente", "LABEL_USER": "Usuario", "TMA_AVERAGE_SERVICE_TIME": "Tiempo promedio de atención", "AVERAGE_SERVICE_TIME_SUBTITLE": "TMA", "AVERAGE_SERVICE_TIME": "Tiempo promedio de atención", "TME_AVERAGE_WAITING_TIME": "Promedio del primer tiempo de espera", "AVERAGE_WAITING_TIME_SUBTITLE": "PTE", "AVERAGE_WAITING_TIME": "Promedio del primer tiempo de espera", "TME_AVERAGE_WAITING_TIME_AVG": "Promedio del tiempo de espera", "AVERAGE_WAITING_TIME_AVG_SUBTITLE": "TME", "AVERAGE_WAITING_TIME_AVG": "Promedio del tiempo de espera", "QME_QMR_NUMBER_OF_MESSAGES_SENT_AND_RECEIVED": "Cantidad de mensajes", "NUMBER_OF_MESSAGES_SENT_AND_RECEIVED": "Cantidad de mensajes enviados y recibidos", "QME_SUBTITLE": "Mensajes enviados", "QMR_SUBTITLE": "Men<PERSON><PERSON><PERSON>", "QMT_SUBTITLE": "Total de mensajes", "QCA_QCF_NUMBER_OF_OPEN_AND_CLOSED_CALLS": "Cantidad de llamados", "NUMBER_OF_OPEN_AND_CLOSED_TICKETS": "Cantidad de llamados abiertos y cerrados", "QCA_SUBTITLE": "Llamados abiertos", "QCF_SUBTITLE": "Llamados cerrados", "QCT_SUBTITLE": "Total de llamados", "QTC_NUMBER_OF_UNIQUE_CONTACTS": "Cantidad de contactos únicos", "NUMBER_OF_UNIQUE_CONTACTS": "Cantidad de contactos únicos", "NUMBER_OF_UNIQUE_CONTACTS_SUBTITLE": "QTC", "QAU_NUMBER_OF_UNIQUE_ISSUES": "Cantidad de asuntos únicos", "NUMBER_OF_UNIQUE_SUBJECTS": "Cantidad de asuntos únicos", "NUMBER_OF_UNIQUE_SUBJECTS_SUBTITLE": "QAU", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_AFTER_BOT": "Promedio del primer tiempo de espera después de la finalización del bot", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_AVG_AFTER_BOT": "Promedio del tiempo de espera promedio después de la finalización del bot", "CREATE_STATS_MESSAGE_POPOVER_PERIOD_AFTER_BOT": "Período entre la finalización de la atención hecha por el bot y el primer mensaje enviado por el asistente, conforme a los filtros", "TMEFB_AVERAGE_SERVICE_TIME_AFTER_BOT": "TMEFB - Tiempo promedio de espera después de la finalización del bot", "AVERAGE_SERVICE_TIME_SUBTITLE_AFTER_BOT": "TMEFB", "AVERAGE_SERVICE_TIME_AFTER_BOT": "Tiempo promedio de espera después de la finalización del bot", "TMEFB_AVERAGE_WAITING_TIME_AFTER_BOT": "Promedio del primer tiempo de espera después de la finalización del bot", "TOTAL": "Total", "TOOLTIP_SUFFIX_SECOND_SINGULAR": " segundo", "TOOLTIP_SUFFIX_SECOND_PLURAL": " segundos", "TOOLTIP_SUFFIX_CALL_SINGULAR": " llamado", "TOOLTIP_SUFFIX_CALL_PLURAL": " llamados", "TOOLTIP_SUFFIX_CONTACT_SINGULAR": " contacto", "TOOLTIP_SUFFIX_CONTACT_PLURAL": " contactos", "TOOLTIP_SUFFIX_MESSAGE_SINGULAR": " mensaje", "TOOLTIP_SUFFIX_MESSAGE_PLURAL": " mensa<PERSON><PERSON>", "LABEL_FOR_DESCRIPTION_FILTER": "Mostrando datos para el período de {{dias}} días ", "DATES_SELECTED_BY_FILTER": "{{string_date}}", "AND_FILTER_BY": " y filtrando por ", "ATTENDANTS": "asistentes", "DEPARTMENTS": "departamentos", "ALL_ATTENDANTS": "Todos los asistentes", "ALL_CONNECTIONS": "Todas las conexiones", "ALL_TICKET_TOPICS": "Todos los asuntos del llamado", "ALL_TAGS": "Todas las etiquetas", "SUFIX_MESSAGE": "mensa<PERSON><PERSON>", "SUFIX_CONTACT": "contactos", "BUTTON_TEXT_OLD_VERSION": "Acceder a la versión antigua", "BUTTON_TEXT_NEW_VERSION": "Accede a la nueva versión"}