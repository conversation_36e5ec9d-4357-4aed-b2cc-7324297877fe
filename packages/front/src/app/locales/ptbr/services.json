{"TITLE_SERVICES": "Conexões", "TITLE_SERVICE": "Conexão", "BUTTON_NEW_SERVICE": "Nova conexão", "TITLE_ACCOUNTS": "<PERSON><PERSON>", "TOOTIP_CHOICE_SERVICE": "Escolha o canal que deseja iniciar uma conexão.", "LABEL_WEBCHAT_NAME_PLUGIN": "Nome para o plugin", "POPOVER_WEBCHAT_NAME_PLUGIN": "Insira o nome que será exibido no chat de atendimento.\nEx: <PERSON><PERSON><PERSON>, <PERSON>dor ou afins.", "LABEL_WEBCHAT_BODY_PLUGIN": "Inserir o nome que será exibido no cabeçalho do plugin do site. Ex.:Atendente", "LABEL_WEBCHAT_PICTURE_PLUGIN": "Foto perfil para o plugin", "LABEL_WEBCHAT_WHATSAPP_PLUGIN": "Whatsapp para o plugin", "POPOVER_WEBCHAT_WHATSAPP_PLUGIN": "Insira o número do WhatsApp (DDD + número de telefone) para que o seu cliente seja redirecionado através do webchat.\nEx: 11 99999-9999", "MESSAGE_SHOW_NOT_IS_IMAGE": "Selecione um formato do tipo imagem", "LABEL_WEBCHAT_TOGGLE_FORM": "Iniciar conversa sem o formulário", "LABEL_WEBCHAT_IS_START_OPEN_DESKTOP": "Em desktops, iniciar com chat aberto", "LABEL_WEBCHAT_IS_START_OPEN_MOBILE": "Em dispositivos móveis, iniciar com chat aberto", "LABEL_GET_DATA_CLIENT": "Capturar dados do cliente", "LABEL_ACTIVATE_GET_DATA_CLIENT": "Ativar captura de dados do cliente", "LABEL_WEBCHAT_BODY_WHATSAPP_PLUGIN": "Inserir o número de contato Whatsapp a ser redirecionado a partir do plugin. Ex.: ************", "LABEL_WEBCHAT_TELEGRAM": "Telegram para o plugin", "POPOVER_WEBCHAT_TELEGRAM": "Insira o nome do bot/grupo do Telegram para que o seu cliente seja redirecionado através do webchat.", "LABEL_WEBCHAT_TELEGRAM_BODY": " Inserir o bot/grupo do Telegram a ser redirecionado a partir do plugin", "LABEL_INPUT_BOT_TOKEN_TELEGRAM": "BOT Token", "LABEL_WHATSAPP_BUSINESS_PROVIDER_TYPE": "<PERSON><PERSON><PERSON>", "LABEL_WHATSAPP_BUSINESS_ACCOUNT_KEY": "<PERSON><PERSON>", "LABEL_WHATSAPP_BUSINESS_APP_NAME": "Nome do app", "LABEL_EMAIL_PROVIDER": "<PERSON><PERSON><PERSON>", "LABEL_EMAIL_JUNK": "Incluir caixa de spam", "LABEL_EMAIL_SERVER_SMTP": "Servidor SMTP", "LABEL_EMAIL_PORT_SMTP": "Porta SMTP", "LABEL_EMAIL_SERVER_IMAP": "Servidor IMAP", "LABEL_EMAIL_PORT_IMAP": "Porta IMAP", "LABEL_EMAIL_PRIVATE_KEY": "Chave privada", "LABEL_EMAIL_CLIENT_ID": "Id do cliente", "LABEL_EMAIL_CLIENT_SECRET": "Segredo do cliente", "LABEL_EMAIL_SERVICE_EMAIL": "Email de serviço", "LABEL_EMAIL_ACCOUNT_EMAIL": "Conta de email", "LABEL_EMAIL_PASSWORD_EMAIL": "Senha da conta de email", "LABEL_MODAL_NAME": "Nome", "LABEL_NAME_WEBCHAT": "<PERSON><PERSON> da <PERSON>", "LABEL_MODAL_TYPE": "Tipo", "LABEL_MODAL_STANDARD_DEPARTMENT": "Departamento padrão para chamados", "LABEL_MODAL_BOT": "Bot", "ERROR_BOT_INTERACTIVE_MESSAGE": "Esse robô contém ações no fluxo que são suportadas apenas por conexões de Whatsapp Business, utilize outro robô nessa conexão.", "ERROR_BOT_WEBCHAT_TRIGGER": "Esse robô contem ações no fluxo que só funcionam em conexões de Webchat, selecione outro robô.", "LABEL_MODAL_ERROR": "Ocorreu um erro ao salvar a conexão...", "LABEL_SWITCH_READ_CONFIRMATION": "Confirmação de leitura?", "LABEL_SWITCH_DISPLAY_TYPING": "Exibir digitando antes de enviar?", "LABEL_SWITCH_BLOCK_MESSAGE_RULES_ACTIVE": "Não enviar mensagens fora do horário por DDD", "LABEL_SWITCH_UNBLOCK_BY_RECEIVE_MESSAGE": "Desbloquear o chat ao receber mensagem durante horário de bloque<PERSON>", "LABEL_TOOLTIP_BLOCK_MESSAGE_RULES_ACTIVE": "Ao marcar essa opção, serão consideradas as restrições de horário de cada estado e feriados cadastrados na plataforma para o envio de mensagens e campanhas.", "LABEL_SWITCH_OPEN_CALL": "<PERSON><PERSON><PERSON> chamado para mensagens de grupo?", "LABEL_SWITCH_LOW_LEVEL_API": "API baixo nível?", "LABEL_SWITCH_USE_SANDBOX": "Utilizar sandbox para esse canal?", "TEXT_MODAL_DELETE_SERVICE": "Para excluir conexões, por gentileza, entre em contato com o suporte", "TEXT_MODAL_DONT_HAVE_PERMISSION": "Você não tem permissão para criar esse tipo conexão", "LABEL_CONNECTED": "Conectado", "LABEL_DISCONNECTED": "Desconectado", "LABEL_CONNECTION_INACTIVE": "Conexão arquivada", "LABEL_STARTING": "Iniciando...", "LABEL_CONNECT_YOUR_PHONE_TO_THE_CHARGER": "Conecte o celular no carregador", "LABEL_CELL_PHONE_CHARGING": "<PERSON><PERSON><PERSON> carregando", "LABEL_NO_MOBILE_CONNECTION": "Sem conexão com o celular", "LABEL_SYNCHRONIZING_CONTACTS": "Sincronizando contatos e mensagens", "LABEL_QR_CODE_NUMBER_INVALID": "Tentativa de escanear o QR com um número diferente do cadastrado", "LABEL_CONNECTED_ELSEWHERE": "  Conectado em outro local", "LABEL_RECONNECT": "Reconectar", "LABEL_CHOOSE_SERVICE": "Escolha o canal que deseja iniciar uma conexão.", "LABEL_CONNECTION_DISCONNECTED": "Conexão desligada", "LABEL_START_CONNECTION": "<PERSON><PERSON><PERSON>", "LABEL_CONNECTION_TOKEN": "<PERSON><PERSON> de cone<PERSON>", "LABEL_WAITING_QR": "Aguardando QR Code", "LABEL_SCAN_QR_CODE": "Escanear QR Code", "LABEL_SCAN_THE_QR_CODE": "Escaneie o QR Code", "MODAL_HEADER_MONITOR_UPDATED": "Monitor (atualizado a cada 2s)", "TITLE_ARCHIVE_SERVICE": "Você tem certeza que deseja {{ item }} essa conexão?", "LABEL_UNARCHIVE": "Ativar <PERSON>", "LABEL_TO_FILE": "<PERSON><PERSON><PERSON><PERSON>", "BODY_TO_FILE_SERVICE": "Ao arquivar a conexão não poderá ser mais usada!", "ERROR_LIMIT_TYPE_CONNECTION": "O limite desse tipo de conexão foi excedido! Arquive uma conexão desse tipo ou contate o administrador", "MESSAGE_MODAL_AUTHORIZATION": "Para ativar conex<PERSON>, por gentileza, entre em contato com o suporte", "THIS_ACTION_COULD_NOT_BE_PERFORMED": "Não foi possível realizar essa ação", "LABEL_RESTART": "Reiniciar", "LABEL_TO_SWITCH_OFF": "<PERSON><PERSON><PERSON>", "LABEL_LOGOUT": "Logout", "LABEL_MONITOR": "Monitorar", "LABEL_START": "Iniciar", "LABEL_WEBHOOK_LINK": "Link do webhook", "LABEL_WEBHOOK_LINK_COPIED": "Copiado!", "LABEL_WEBCHAT_INSTABILITY": "Instabilidade no servidor do webchat", "LABEL_DETAILS": "<PERSON><PERSON><PERSON>", "LABEL_CLOSE": "<PERSON><PERSON><PERSON>", "LABEL_WABA_HEALTH": "Saúde WABA", "SERVICE_HISTORIC": "Hist<PERSON><PERSON><PERSON>", "SERVICE_CONNECTION_HEALTH": "<PERSON><PERSON><PERSON>", "STATUS_WABA_BLOCKED": "BLOQUEADA", "STATUS_WABA_LIMITED": "AÇÃO NECESSÁRIA", "STATUS_WABA_AVAILABLE": "ATIVA", "SERVICE_EVENT_HISTORY": "Histórico de eventos", "SERVICE_HISTORIC_DESCRIPTION": "Acompanhe os eventos da conexão com data e hora de início e fim das instabilidades da conexão", "SERVICE_HISTORIC_TABLE_HEAD_START": "Início", "SERVICE_HISTORIC_TABLE_HEAD_END": "Fim", "SERVICE_HISTORIC_TABLE_HEAD_ONLINE": "Online", "SERVICE_HISTORIC_CHANNEL_IS_CONNECTED": "Indica se o canal está conectado", "SERVICE_HISTORIC_TABLE_HEAD_REASON": "Motivo", "SERVICE_HISTORIC_NO_RECORDS": "Sem Registros", "SERVICE_HISTORIC_DEVICE_OFFLINE": "Dispositivo offline", "SERVICE_HISTORIC_WAITING_QR_CODE": "Esperando código QR", "SERVICE_HISTORIC_CONNECTED_ELSEWHERE": "Conectado em outro lugar", "SERVICE_HISTORIC_SHUTDOWN": "Des<PERSON><PERSON>", "SERVICE_HISTORIC_CONNECTED": "Conectado", "SERVICE_HISTORIC_ARCHIVED": "Arquivado", "SERVICE_HISTORIC_UNKNOWN": "Desconhecido", "SERVICE_SCRIPT_INIT_OPEN_CHAT": "Ao ativar a função “Iniciar com chat aberto”, sempre que o usuário entrar no site o chat estará aberto, podendo iniciar o atendimento, sem a necessidade do click no balão de conversa.", "AUTHENTICATE_BUTTON": "Autenticar", "WHATSAPP_ERROR_MESSAGE_QR_WITH_DIFFERENT_NUMBER": "Tentativa de escanear o QR com um número diferente do cadastrado", "MODAL_FACEBOOK_INFO": "Para vincular uma página com a Digisac é necessário algumas permissões, para isso faça o login com o facebook utilizando um usuário que é administrador da pagina que será vinculada.", "MODAL_FACEBOOK_PAGE": "p<PERSON><PERSON><PERSON>", "MODAL_FACEBOOK_CATEGORY": "Categoria(s)", "MODAL_FACEBOOK_TASK": "<PERSON><PERSON><PERSON>(s)", "MODAL_FACEBOOK_NO_PAGES": "Esse facebook não tem nenhuma página, crie uma clicando", "MODAL_FACEBOOK_HERE": "aqui", "MODAL_FACEBOOK_THERE_IS_NO_CONNECTION": "Nenhuma página encontrada pois não tem facebook conectado, entre com facebook para carregar as páginas.", "MODAL_FACEBOOK_WITHOUT_INSTAGRAM": "Nenhuma conta de instagram vinculada a essa página.", "MODAL_FACEBOOK_ATTACH_PAGE": "Vinculação da página", "MODAL_FACEBOOK_ERROR_PAGE": "Já existe uma conexão vinculada a essa página", "MODAL_FACEBOOK_ERROR_NUMBER": "Já existe uma conexão vinculada a esse número de telefone", "MODAL_FACEBOOK_ATTACH_PAGE_BTN": "Vincular uma página", "FACEBOOK_MODAL_TITLE_STEP_1": "Vinculação da página", "FACEBOOK_MODAL_SUBTITLE_STEP_1": "Ao vincular uma página do Facebook com a Digisac, certifique se o perfil logado corresponde ao administrador da página.", "FACEBOOK_MODAL_TITLE_STEP_2": "Facebook", "FACEBOOK_MODAL_SUBTITLE_STEP_2": "Certifique se o perfil logado abaixo corresponde ao administrador da página.", "FACEBOOK_MODAL_CONTENT_AUTHENTICATE": "Ao vincular sua conta com a Digisac, o Facebook registrará quando acessarmos informações referentes ao compartilhamento. Reservamo-nos o direito de realizar análises (ou contratar profissionais para fazê-la em nosso nome) do conteúdo da sua Conta Empresarial do Instagram e/ou Conta de Criador de Conteúdo no tocante à sua conformidade com os nossos Padrões da Comunidade e com as Diretrizes da Comunidade do Instagram usando meios manuais ou automatizados (ou ambos). Em caso de dúvidas, leia nossa política de privacidade e termos listados nos links a seguir.", "FACEBOOK_MODAL_PRIVACY_POLICY": "Política de Privacidade", "FACEBOOK_MODAL_TERMS_DIGISAC": "<PERSON><PERSON><PERSON>", "FACEBOOK_MODAL_IS_NOT_USER": "Não é", "FACEBOOK_MODAL_ENTER_OTHER_ACCOUNT": "Entrar em outra conta.", "FACEBOOK_MODAL_SELECT_PAGE": "Selecione uma página do Facebook para permitir o acesso à sua conta", "FACEBOOK_MODAL_SELECT_NUMBER": "Selecione um número do Facebook para permitir o acesso à sua conta", "FACEBOOK_MODAL_PAGE_NOT_FOUND": "<PERSON>enhuma página encontrada", "FACEBOOK_MODAL_INSTAGRAM_NOT_FOUND": "Nenhuma conta de instagram vinculada a essa página.", "FACEBOOK_MODAL_LABEL_BUTTON": "Vincular uma página", "FACEBOOK_MODAL_SELECT_NUMBER_HEADER_COMPANY": "Empresa", "FACEBOOK_MODAL_SELECT_NUMBER_HEADER_NUMBER": "Número", "FACEBOOK_MODAL_SELECT_NUMBER_HEADER_STATUS": "Status", "FACEBOOK_MODAL_SELECT_NUMBER_HEADER_QUALITY": "Qualidade", "FACEBOOK_MODAL_QUALITY_NAME_GREEN": "VERDE", "FACEBOOK_MODAL_QUALITY_NAME_UNKNOWN": "DESCONHECIDO", "FACEBOOK_MODAL_STATUS_NAME_VERIFIED": "Verificado", "FACEBOOK_MODAL_STATUS_NAME_NOT_VERIFIED": "Não verificado", "FACEBOOK_MODAL_STATUS_NAME_UNKNOWN": "Desconhecido", "GOOGLE_BUSINESS_MODAL_LABEL_ATTACH_FILE": "Insira o arquivo JSON", "GOOGLE_BUSINESS_MODAL_INSTRUCTIONS": "Para configurar o recebimento de mensagens do Google Business Message, acesse sua conta parceiro Google > Configuracoes > Webhook. Copie o código abaixo e cole na página.", "GOOGLE_BUSINESS_MODAL_SECURITY_KEY": "<PERSON>ve de <PERSON>", "GOOGLE_BUSINESS_MODAL_HOW_TO_GET_THE_SECURITY_KEY": "Acesse sua conta parceiro Google > Conta de serviço > C<PERSON>r chave, faça o download do arquivo JSON e anexe-o aqui.", "GOOGLE_BUSINESS_ERROR_MESSAGE": "Sem o arquivo JSON para autenticação", "TELEGRAM_ERROR_MESSAGE_INVALID_TOKEN": "<PERSON><PERSON>", "TELEGRAM_ERROR_MESSAGE_TOKEN_NULL": "Token não informado", "TELEGRAM_ERROR_MESSAGE_CONNECTION_ERROR": "Erro ao conectar ao Telegram", "LABEL_RECLAME_AQUI_USER": "<PERSON><PERSON><PERSON><PERSON>", "LABEL_RECLAME_AQUI_PASS": "<PERSON><PERSON>", "MODAL_DUPLICATE_NAME": "Já existe uma conexão do mesmo tipo com esse nome.", "TOOLTIP_ACCESS_TOKEN": "Caso não possua um token válido de acesso, deixe o campo em branco e após salvar a conexão realize o Integrated Onboarding, para obter um novo acesso.", "BUTTON_INTEGRATED_ONBOARDING_360": "Acesso 360Dialog", "LABEL_EMAIL_REQUIRE_TLS": "RequireTLS", "LABEL_EMAIL_IGNORE_TLS": "IgnoreTLS", "LABEL_EXTERNAL_BILLING": "Cobrança externa", "PHONE_NUMBER": "Número de telefone", "API_KEY": "<PERSON><PERSON> da <PERSON>pi da aplicação", "TAG_GOOGLE": "Tag do google", "TAG_GOOGLE_DESCRIPTION": "Tag utilizada pelo google analytics para coletar dados de utilização dos usuários. Exemplo: G-LK4CZ2D664", "WEBCHAT_PREVIEW_PICKER_TITLE": "Pré-visualização do seu webchat:", "WEBCHAT_PREVIEW_BUBBLE_AND_CALL": "Balão e chamada do webchat", "WEBCHAT_PREVIEW_FORM_FIELDS": "Campos do formulário", "WEBCHAT_PREVIEW_ONGOING_CONVERSATION": "Conversa em andamento", "WEBCHAT_PREVIEW_SEND_MESSAGE": "Digite sua mensagem", "WEBCHAT_PREVIEW_MESSAGE": "Lorem ipsum dolor sit amet consectetur. Risus nisl aliquet montes amet. Sem tincidunt nulla gravida pharetra ornare. Faucibus at lorem accumsan nunc id laoreet laoreet quam nullam.", "WEBCHAT_PREVIEW_SUBTITLE": "Preencha o formulário para começar a conversar com um de nossos atendentes.", "WEBCHAT_PREVIEW_NAME": "Qual é o seu nome?", "WEBCHAT_PREVIEW_EMAIL": "Qual é o seu e-mail?", "WEBCHAT_PREVIEW_PHONE": "Qual é o seu telefone?", "WEBCHAT_PREVIEW_START": "Iniciar bate papo", "WEBCHAT_PREVIEW_DEVELOPED_BY": "Desenvolvido por", "WEBCHAT_PREVIEW_DEVELOPED_BY_NAME": "<PERSON><PERSON><PERSON>", "WEBCHAT_PREVIEW_REQUIRED_FIELD": "Campo obrigatório", "WEBCHAT_PREVIEW_BUBBLE_TEXT": "<PERSON><PERSON><PERSON>, em que posso ajudar?", "WEBCHAT_PREVIEW_ONLINE": "Online", "WEBCHAT_GENERAL_CONFIG": "Configurações gerais da conexão", "WEBCHAT_OPERATION_CONFIG": "Funcionamento do chat", "WEBCHAT_OPERATION_CONFIG_DEVICES": "Funcionamento do chat em dispositivos", "WEBCHAT_OPERATION_FIELDS": "Selecione os campos do formulário", "WEBCHAT_LABEL_INCLUDE_NAME": "Incluir ”Nome”", "WEBCHAT_LABEL_INCLUDE_EMAIL": "Incluir ”E-mail”", "WEBCHAT_LABEL_INCLUDE_PHONE": "Incluir ”Telefone”", "WEBCHAT_LABEL_INCLUDE_CUSTOM_FIELD": "Incluir campo personalizado", "WEBCHAT_TEXT_REQUIRED_FIELDS": "O campo “E-mail” tem o seu preenchimento obrigatório e não podem ser desativados. Eles serão usados para pegar o histórico de conversa do seu usuário e facilitar o atendimento.", "WEBCHAT_TEXT_CUSTOM_FIELDS": "Os campos personalizados são campos abertos, ou seja, não é possível confirmar se as informações inseridas estão corretas. Para criar campos personalizados acesse Mais opções > Campos personalizados", "WEBCHAT_CUSTOM_CONFIG": "Personalização e customização", "WEBCHAT_CHAT_BALLOON": "Chamada do balão de chat", "WEBCHAT_ENABLE_CHAT_BALLOON": "Ativar chamada no widget de chat", "WEBCHAT_CHAT_BALLOON_TEXT": "Insira uma mensagem com até 32 caracteres", "WEBCHAT_LABEL_COLOR": "Cor base do chat de atendimento", "WEBCHAT_LABEL_ICON": "Ícone do widget de atendimento", "LABEL_SELECT": "Selecionar", "LABEL_UPLOAD_ICON": "Upload de ícone personalizado", "WEBCHAT_LABEL_LAYOUT": "Defina a aparência do seu chat", "WEBCHAT_LAYOUT_DESCRIPTION": "Anexe as imagens no formato PNG, JPEG, JPG ou BMP, com ao menos 150 x 150px e tamanho máximo de 2MB.", "WEBCHAT_LABEL_LAYOUT_LOGO": "Anexe a logo da empresa", "WEBCHAT_LABEL_LAYOUT_PROFILE_PICTURE": "Anexe a foto de perfil", "BUTTON_ATTACH_LOGO": "Anexar logo", "BUTTON_ATTACH_PROFILE_PICTURE": "Anexar foto de perfil", "WEBCHAT_LAYOUT_LOGO_DESCRIPTION": "A logo da sua empresa aparece no topo do chat quando ele está aberto. Caso você não anexe um arquivo o ícone padrão do chat será utilizado.", "WEBCHAT_LAYOUT_PROFILE_PICTURE_DESCRIPTION": "A foto de perfil é a imagem que aparece como foto do atendente, podendo ser uma foto, logo ou ícone. Caso você não anexe um arquivo o ícone padrão será utilizado.", "MAX_IMAGE_SIZE": "A imagem selecionada possui mais de 2MB", "WISH_GET_OUT": "Deseja realmente sair?", "GET_OUT_DESCRIPTION": "Se sair agora perderá todas as configurações já realizadas nessa conexão de Webchat.", "GET_OUT_ACCEPT_BUTTON": "Descartar e sair", "GET_OUT_CANCEL_BUTTON": "Continuar configurando", "WARNING_SERVICES_WABA": "Você tem conexões que precisam de atenção. Clique no link para visualizar: ", "BLOCK_SERVICES_WABA": "Você tem conexões bloqueadas. Clique no link para visualizar: ", "LINK_NAME_TO_SERVICES_WABA": "<PERSON>r para Saúde WABA", "MESSAGE_INFO_DESCRIPTION": "Verifique o painel da Meta para entender as limitações e ajustes necessários.", "MESSAGE_HEALTH_INFO_DESCRIPTION": "Esta conta está habilitada e sem restrições", "MESSAGE_BLOCK_INFO_DESCRIPTION": "Entre em contato com o nosso suporte para mais orientações.", "LABEL_CLOSE_TICKET": "Fechamento de chamados por parte do cliente", "LABEL_CLOSE_TICKET_BY_CLIENT": "Per<PERSON><PERSON> que o cliente feche seus chamados pelo ícone de fechar", "INFO_CLOSE_TICKET_BY_CLIENT": "Ao ativar a permissão de fechamento de chamados por parte do cliente o ícone “X” aparece, possibilitando que o cliente feche o chamado em andamento.", "ARCHIVE_CONNECTION_MODAL_TICKETS_TITLE": "Você tem certeza que deseja arquivar essa conexão?", "ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION": "A conexão possui {{countOpenTickets}} chamado(s) em aberto. Para prosseguir com o arquivamento, é necessário encerrar todos os chamados inseridos nesta conexão. Para isso, você deverá: <br /> ", "ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP1": "1 . Acessar a página de ", "ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP1_LINK": "Históricos de chamados ", "ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP2": "2. Se<PERSON><PERSON>ar todos os chamados em aberto;<br />", "ARCHIVE_CONNECTION_MODAL_TICKETS_DESCRIPTION_STEP3": "3 . Ir ao topo da página e clicar em <strong>Ações em massa > <PERSON><PERSON><PERSON></strong>;", "ARCHIVE_CONNECTION_MODAL_TICKETS_BUTTON_TICKETS_HISTORY": "Ir para Históricos de chamados", "ARCHIVE_CONNECTION_MODAL_BUTTON_CANCEL": "<PERSON><PERSON><PERSON>", "ARCHIVE_CONNECTION_MODAL_CAMPAIGNS_TITLE": "Você tem certeza que deseja arquivar essa conexão?", "ARCHIVE_CONNECTION_MODAL_CAMPAIGNS_DESCRIPTION": "<PERSON><PERSON>, a(s) <strong>{{countActiveCampaigns}} campanha(s) ativa(s)</strong> desta conexão será(ão) cancelada(s). A conexão não poderá mais ser usada.", "TRACK_MESSAGE_BLOCK_STATUS": "Acompanhe o status da funcionalidade de bloqueio de mensagens por DDD e horário.", "MESSAGE_BLOCK": "Bloqueio do chat por horário", "CHAT_UNLOCK": "Desbloqueio com mensagem receptiva", "CONNECTION_EVENTS": "Eventos da conexão", "BLOCK_STATUS_BY_AREA_CODE": "Status do bloqueio por DDD", "EXPORT_BLOCK_STATUS_FILENAME": "relatorio_status_bloqueio_ddd", "EXPORT_BLOCK_STATUS_SUCCESS": "Relatório exportado com sucesso.", "EXPORT_BLOCK_STATUS_ERROR": "Ocorreu um erro na exportação.", "MODAL_QR_CODE_CONNECT_NUMBER": "Conecte um número nessa conexão", "MODAL_QR_CODE_FOLLOW_STEPS": "Para conectar essa conexão siga o passo a passo:", "MODAL_QR_CODE_STEP_1": "Pegue o aparelho que contém o número que será vinculado nessa conex<PERSON>.", "MODAL_QR_CODE_STEP_2": "Entre no aplicativo do WhatsApp através desse aparelho.", "MODAL_QR_CODE_STEP_3": "Vá em Configurações > Dispositivos conectados > Conectar dispositivo.", "MODAL_QR_CODE_STEP_4": "Escaneie o QR Code ao lado.", "MODAL_QR_CODE_ALT_TEXT": "QR Code para conectar WhatsApp", "MODAL_QR_CODE_PLACEHOLDER": "QR Code", "MODAL_QR_CODE_RECONNECT_NUMBER": "Esse número foi desconectado", "MODAL_QR_CODE_RECONNECT_FOLLOW_STEPS": "Para iniciar novamente a conexão siga o passo a passo:", "MODAL_QR_CODE_RECONNECT_STEP_1": "Pegue o aparelho que contém o número {{phoneNumber}}.", "MODAL_QR_CODE_CANCEL": "<PERSON><PERSON><PERSON>", "RECONNECT_SERVICES_ALERT_MESSAGE": "Existem conexões que foram desconectadas involuntariamente. Clique no botão ao lado para ver os números que foram desconectados e reiniciá-los.", "RECONNECT_SERVICES_ALERT_BUTTON": "<PERSON><PERSON>", "CONNECTION_START_ERROR": "Falha ao iniciar conex<PERSON>. Tente novamente.", "STARTING_CONNECTION": "Iniciando <PERSON>...", "RETRY_CONNECTION": "Tentar novamente", "START_CONNECTION": "<PERSON><PERSON><PERSON>"}