import common from './common.json'
import login from './login.json'
import chatPage from './chat.json'
import accountPage from './account.json'
import botsPage from './bots.json'
import blockMessageRulesPage from './blockMessageRules.json'
import campaignPage from './campaign.json'
import chartsStats from './charts-stats.json'
import companyPage from './company.json'
import contactPage from './contact.json'
import conversationStats from './conversation-stats.json'
import creditsControlPage from './credits-control.json'
import creditsPage from './credits.json'
import customFields from './custom-fields.json'
import departmentStats from './department-stats.json'
import department from './department.json'
import distribution from './distribution.json'
import evaluationStats from './evaluation-stats.json'
import evaluationPage from './evaluation.json'
import holiday from './holiday.json'
import integrationPage from './integration.json'
import menu from './menu.json'
import multipleInput from './multiple-input.json'
import navbar from './navbar.json'
import now from './now.json'
import organizationPage from './organization.json'
import componentPagination from './pagination.json'
import peoplePage from './people.json'
import permissions from './permissions.json'
import quickRepliesPage from './quick-replies.json'
import resetPasswordTokenPage from './reset-password-token.json'
import resetPasswordPage from './reset-password.json'
import rolesPage from './roles.json'
import schedulesPage from './schedules.json'
import selectAccountPage from './selectAccount.json'
import servicesPage from './services.json'
import statsGeneral from './stats-general.json'
import stats from './stats.json'
import tagsPage from './tags.json'
import takeoverPage from './takeover.json'
import ticketHistory from './ticket-history.json'
import term from './term.json'
import ticketTopicsPage from './ticket-topics.json'
import timeTablePage from './time-table.json'
import whatsappBusinessTemplatesPage from './whatsapp-business-templates.json'
import interactiveMessagesPage from './interactive-messages.json'
import knowledgeBase from './knowledge-base.json'
import userStats from './user-stats.json'
import usersPage from './users.json'
import webHooks from './webhooks.json'
import workPlan from './work-plan.json'
import hsmPage from './hsm.json'
import tutorials from './tutorials.json'
import notifications from './notification.json'
import wizardPage from './wizard.json'
import acceptancetermsPage from './acceptance-terms.json'
import statsHsm from './hsm-stats.json'
import showOutOfHours from './show-out-of-hours.json'
import absenceManagement from './absence-management.json'
import contactsImport from './contacts-import.json'
import myplan from './myplan.json'
import contactBlockListsPage from './contact-block-lists.json'
import pipelines from './pipelines.json'
import passwordStrength from './password-strength.json'
import authHistoryPage from './authHistory.json'
import archivedUser from './archived-user.json'
import aIConsumption from './ai-consumption.json'
import message from './message.json'
import marketplace from './marketplace.json'
import smartSummary from './smart-summary.json'
import marketingIntegration from './marketing-integration.json'
import internalChat from './internal-chat.json'

export default {
  acceptancetermsPage,
  common,
  login,
  chatPage,
  now,
  menu,
  navbar,
  accountPage,
  botsPage,
  blockMessageRulesPage,
  campaignPage,
  chartsStats,
  companyPage,
  contactPage,
  conversationStats,
  creditsControlPage,
  creditsPage,
  customFields,
  departmentStats,
  department,
  distribution,
  evaluationStats,
  evaluationPage,
  integrationPage,
  multipleInput,
  organizationPage,
  componentPagination,
  peoplePage,
  webHooks,
  permissions,
  rolesPage,
  quickRepliesPage,
  resetPasswordTokenPage,
  resetPasswordPage,
  schedulesPage,
  selectAccountPage,
  servicesPage,
  statsGeneral,
  stats,
  takeoverPage,
  tagsPage,
  ticketHistory,
  term,
  ticketTopicsPage,
  timeTablePage,
  whatsappBusinessTemplatesPage,
  interactiveMessagesPage,
  knowledgeBase,
  userStats,
  usersPage,
  workPlan,
  hsmPage,
  tutorials,
  notifications,
  wizardPage,
  statsHsm,
  showOutOfHours,
  holiday,
  absenceManagement,
  contactsImport,
  myplan,
  contactBlockListsPage,
  pipelines,
  passwordStrength,
  authHistoryPage,
  archivedUser,
  aIConsumption,
  message,
  marketplace,
  smartSummary,
  marketingIntegration,
  internalChat,
}
