{"TITLE_COMPANY": "Empresa", "TITLE_MENU_GENERAL": "G<PERSON>", "TITLE_MENU_SAC": "SAC", "TITLE_MENU_TIME_OPERATION": "Horário de funcionamento", "LABEL_NAME_COMPANY": "Nome da empresa", "LABEL_TIMETABLE_COMPANY": "Tabela de <PERSON>", "LABEL_TIMEZONE": "<PERSON><PERSON>", "LABEL_IPS_RESTRICTION": "Restrição de IPs", "LABEL_IPS_ACCEPT": "IPs aceitos", "LABEL_WHATSAPP_BUSINESS_HSM_AVAILABLE": "Template disponível", "LABEL_SAC_NAME_ATTENDANT_BEGINNING_MESSAGE": "Nome do atendente no começo das mensagens", "LABEL_SAC_DISABLE_AUTOMATIC_TRANSFER": "Desabilitar transferência automática para departamento/atendente padrão do contato", "LABEL_SAC_NOTIFY_OPENING_OF_TICKETS": "Notificar abertura de chamados", "LABEL_SAC_NOTIFY_TRANSFER_OF_TICKETS": "Notificar transferência de chamados", "LABEL_SAC_NOTIFY_NEW_MESSAGE": "Notificar nova mensagem", "LABEL_SAC_SHOW_SUPPORT_INFORMATION": "Mostrar informações do suporte", "LABEL_SAC_MAKE_SUBJECT_REQUIRED": "Tornar assunto obrigatório", "LABEL_VALIDATE_NOTIFICATION_QUEUE": "Exibir notificação por departamentos no aplicativo.", "MESSAGE_GREATER_THAN_LESS_THAN": "Deve ser maior que {{min}} e menor que {{max}}", "MESSAGE_GREATER_THAN_EQUAL_LESS_THAN": "<PERSON>e ser maior ou igual a {{min}} e menor ou igual a {{max}}", "LABEL_SAC_CALL_DOWNTIME_MINUTES": "Tempo de inatividade de chamado (minutos)", "LABEL_USER_AWAY_MINUTES_TIME": "Tempo do operador sem interação com o sistema (minutos)", "HELPER_USER_AWAY_MINUTES_TIME": "Defina o limite de tempo até que o operador seja considerado ausente", "TOOLTIP_TITLE_USER_AWAY_MINUTES_TIME": "O que é tempo de ausência do operador?", "TOOLTIP_TEXT_USER_AWAY_MINUTES_TIME": "É o intervalo de tempo, em minutos, sem interações do operador com o sistema antes de ser classificado como ausente.", "LABEL_SAC_PROTOCOL_NUMBER_FORMAT": "Formato de número de protocolo", "LABEL_SAC_AVAILABLE_VARIABLES": "Variáveis disponíveis", "LABEL_SAC_DATE_IN_FORMAT": "Data no formato", "LABEL_SAC_INCREMENTAL_CALL_COUNTER": "Contador incremental de chamados", "LABEL_SAC_STANDARD_DEPARTMENT_TICKETS": "Departamento padrão para chamados", "TOAST_MESSAGE_SAVE": "Alterações salvas", "TITLE_ABSENCE": "Controle de ausência", "ABSENCE_INFO": "Controle das ausências dos operadores durante o expediente", "ENABLE_ABSENCE_MANAGEMENT": "Ativar controle de ausência", "ADD_REASONS_INFO": "Cadastre motivos para usar no intervalo", "REASON_NAME_INPUT": "Motivo", "REGISTERED_REASONS": "Motivos cadastrados", "SELECT_ITEMS_TO_LEAVE": "Selecione quais itens serão necessários para sair do controle de intervalo", "INPUT_PASSWORD": "<PERSON><PERSON><PERSON> se<PERSON>a", "ABSENCE_SAVE_SUCCESS": "Controle de ausência salvo com sucesso", "SELECT_REASON_MODAL": "Selecione o motivo da ausência", "INSERT_PASSWORD_MODAL": "<PERSON><PERSON><PERSON> senha para sair", "REASON_REQUIRED_MODAL": "O motivo é obrigatório", "PASSWORD_REQUIRED_MODAL": "A senha é obrigatória", "INVALID_PASSWORD": "Senha incorreta", "INVALID_REASON": "Motivo inválido", "UNLOCK_BUTTON_MODAL": "Desb<PERSON>que<PERSON>", "REASONS_EMPTY": "Motivos não foram escolhidos", "EDIT_PERMISSION": "Usuário sem permissão de edição", "LABEL_SAC_DUPLICATE_NAMES": "<PERSON><PERSON><PERSON> nomes duplicados", "SWITCH_IP_RESTRICTION": "Restrição de IP", "TITLE_SAC": "Serviço de Atendimento ao Cliente (SAC)", "LABEL_PASSWORD_EXPIRATION_ACTIVE": "Expiração de senha", "LABEL_PASSWORD_EXPIRATION_TIME": "Prazo para a expiração", "LABEL_CREATE_USERS_WITH_PASSWORD": "Criar usuários com senha", "SEND_LINK": "Enviar link por email para usuário criar a senha", "GENERATE_PASS": "G<PERSON>r senha aleatória automaticamente", "MANUAL_CREATE": "Senha criada manualmente", "ADMIN_TYPE_CREATE": "Tipo de criação de senha", "SHOW_PASS_FIRST_ACESS": "Exigir alteração de senha do usuário no primeiro acesso", "CONFIG_PASS_USERS": "Configurações de senhas de usuários", "COUNT_TICKETS": "Contador incremental de chamados", "FORMAT_DATE": "Data no formato", "TWO_FACTOR_AUTHENTICATION": "Autenticação de dois fatores (2FA)", "ENABLE_TWO_FACTOR_AUTHENTICATION": "Ativar autenticação de dois fatores", "MAKE_MANDATORY_FOR_ALL_USERS": "Tornar obrigatório para todos os usuários", "PASSWORD_EXPIRATION_TOOLTIP": "O prazo será aplicado a todos os operadores e usuários da plataforma.", "DAYS": "dias", "AUTO_SMART_SUMMARY": "Resumo automático de IA dos atendimentos", "AUTO_GENERATE_SUMMARY_ON_TRANSFER": "Gerar resumo na transferência do chamado", "AUTO_GENERATE_SUMMARY_ON_CLOSURE": "Gerar resumo no encerramento do chamado", "TAGS_DISPLAY_IN_CHAT": "Exibição das tags no chat.", "CONFIGURE_TAGS_DISPLAY": "Configurar a exibição das tags para que apareçam na listagem dos chamados em aberto.", "ALLOW_OPERATORS_TOGGLE_TAGS_DISPLAY": "<PERSON><PERSON><PERSON> que os operadores ativem ou desativem a exibição das tags no chat."}