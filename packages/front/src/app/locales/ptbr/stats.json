{"TITLE_STATS_SERVICE": "Estatísticas de atendimento", "CREATE_STATS_LABEL_GENERAL": "Média geral", "CREATE_STATS_LABEL_PER_PERIOD": "<PERSON><PERSON>", "CREATE_STATS_LABEL_USER": "Por atendente", "CREATE_STATS_LABEL_DEPARTMENT": "Por departamento", "CREATE_STATS_LABEL_TOPICS": "Por assunto", "CREATE_STATS_LABEL_SERVICE": "<PERSON><PERSON>", "CREATE_STATS_LABEL_RECEIVED_MESSAGE": "Mensagens recebidas", "CREATE_STATS_LABEL_SENDED_MESSAGE": "Mensagens enviadas", "CREATE_STATS_LABEL_TOTAL_MESSAGE": "Total de mensagens", "CREATE_STATS_LABEL_QTD_TOTAL_MESSAGE": "Quantidade total de mensagens (enviadas + recebidas)", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_MESSAGE": "Exibe a quantidade total de mensagens (enviadas + recebidas), conforme os filtros", "CREATE_STATS_LABEL_TICKET_TOTAL": "Total de chamados (abertos + fechados)", "CREATE_STATS_LABEL_QTD_TICKET_TOTAL": "Quantidade total de Chamados (abertos + fechados)", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_TICKET": "Exibe a quantidade total de Chamados (abertos + fechados) conforme os filtros", "CREATE_STATS_LABEL_QTD_SENDED_MESSAGE": "Quantidade de mensagens enviadas", "CREATE_STATS_LABEL_QTD_RECEIVED_MESSAGE": "Quantidade de mensagens Recebidas", "CREATE_STATS_LABEL_QTD_TICKET_OPEN": "Quantidade de chamados abertos", "CREATE_STATS_LABEL_QTD_TICKET_CLOSED": "Quantidade de chamados fechados", "CREATE_STATS_LABEL_AVERAGE_CALL_TIME": "Tempo médio de chamados", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME": "Média do 1º tempo de espera", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_AVG": "Tempo médio de espera", "CREATE_STATS_LABEL_AVERAGE_CALL_TIME_IN_MINUTES": "Tempo médio de chamados em minutos", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_IN_MINUTES": "Tempo médio de espera em minutos", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_SUPPORT_IN_MINUTES": "Tempo médio de suporte em minutos", "CREATE_STATS_LABEL_PERCENT_MESSAGE_SENT": "Porcentagem de mensagens enviadas", "CREATE_STATS_LABEL_PERCENT_MESSAGE_RECEIVER": "Porcentagem de mensagens recebidas", "CREATE_STATS_LABEL_TICKET_OPEN": "<PERSON><PERSON><PERSON>", "CREATE_STATS_LABEL_TICKET_CLOSED": "<PERSON><PERSON><PERSON>", "CREATE_STATS_LABEL_MESSAGE_TOTAL": "Total de mensagem do departamento (nos chamados)", "CREATE_STATS_LABEL_MESSAGE_TOTAL_USER": "Total de mensagem do atendente (nos chamados)", "CREATE_STATS_LABEL_AVERAGE_MESSAGE_SENT": "Média de mensagens enviada (por chamado)", "CREATE_STATS_LABEL_CONTACTS_TOTAL": "Total de contatos", "CREATE_STATS_LABEL_QTD_CONTACTS_TOTAL": "Quantidade total de contatos", "CREATE_STATS_MESSAGE_AVERAGE_MESSAGE_TICKET": "Exibe a média do tempo de duração  (desde a abertura até o fechamento) de cada chamado, conforme os filtros", "CREATE_STATS_MESSAGE_POPOVER_PERIOD": "Período entre a primeira mensagem do cliente até a primeira resposta do atendente, sem contar respostas automáticas (Bot), de acordo com os filtros", "CREATE_STATS_MESSAGE_POPOVER_TME": "Média do tempo médio do chamado (oriundo das transferências do mesmo), de acordo com os filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_MESSAGE_SENDED": "Exibe a quantidade total de mensagens enviadas a partir da plataforma conforme os filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_MESSAGE_RECEIVED": "Exibe a quantidade total de mensagens recebidas conforme os filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_TICKET_OPEN": "Exibe a quantidade total de chamados abertos conforme os filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_TICKET_CLOSED": "Exibe a quantidade total de chamados fechados conforme os filtros", "CREATE_STATS_MESSAGE_POPOVER_QTD_TOTAL_CONTACTS": "Exibe a quantidade total dos contatos conforme os filtros", "FILTERS_STATS_LAST_DEPARTMENT": "Último departamento", "FILTERS_STATS_LAST_ATTENDANT": "Foi o último atendente do chamado", "FILTERS_STATS_OPEN_DATE": "Data de abertura", "FILTERS_STATS_MESSAGE_INTERVAL_DATES": "Data final deve ser maior que a inicial", "FILTERS_STATS_LABEL_DEPARTMENT": "Departamento", "FILTERS_STATS_LABEL_WITHOUT_DEPARTMENT": "Sem Departamento", "FILTERS_STATS_REQUIRED_FIELD_CALL_IN_PROGRESS": "Campo obrigatório pois (Andamento do chamado) está como (Atendido pelo departamento)", "FILTER_STATS_REQUIRED_FIELD_ATTENDANT_PARTICIPATION": "Campo obrigatório pois (Participação do atendente) está como (Atendeu no chamado)", "FILTERS_STATS_ATTENDANT_PARTICIPATION": "Participação do atendente", "FILTERS_STATS_ATTENDANT_PARTICIPATION_TYPE": "Tipo de participação", "EXPORT_FILE_NAME": "relatorio_estatistica_atendimento", "SHOW_NULL_DEPARTMENTS": "Mostrar departamentos nulos", "SHOW_NULL_SUBJECTS": "Mostrar assuntos nulos", "LABEL_SHOW_NULL_CONNECTIONS": "Mostrar conex<PERSON>es nulas", "TMA_AVERAGE_SERVICE_TIME": "Tempo médio de atendimento", "AVERAGE_SERVICE_TIME_SUBTITLE": "TMA", "AVERAGE_SERVICE_TIME": "Tempo médio de atendimento", "TME_AVERAGE_WAITING_TIME": "Média do 1º tempo de espera", "AVERAGE_WAITING_TIME_SUBTITLE": "PTE", "AVERAGE_WAITING_TIME": "Média do 1º tempo de espera", "TME_AVERAGE_WAITING_TIME_AVG": "Tempo médio de espera", "AVERAGE_WAITING_TIME_AVG_SUBTITLE": "TME", "AVERAGE_WAITING_TIME_AVG": "Tempo médio de espera", "QME_QMR_NUMBER_OF_MESSAGES_SENT_AND_RECEIVED": "Quantidade de mensagens", "NUMBER_OF_MESSAGES_SENT_AND_RECEIVED": "Quantidade de mensagens enviadas e recebidas", "QME_SUBTITLE": "Mensagens enviadas", "QMR_SUBTITLE": "Mensagens recebidas", "QMT_SUBTITLE": "Total de mensagens", "QCA_QCF_NUMBER_OF_OPEN_AND_CLOSED_CALLS": "Quantidade de chamados", "NUMBER_OF_OPEN_AND_CLOSED_TICKETS": "Quantidade de chamados abertos e fechados", "QCA_SUBTITLE": "<PERSON><PERSON><PERSON>", "QCF_SUBTITLE": "<PERSON><PERSON><PERSON>", "QCT_SUBTITLE": "Total de chamados", "QTC_NUMBER_OF_UNIQUE_CONTACTS": "Quantidade de contatos únicos", "NUMBER_OF_UNIQUE_CONTACTS": "Quantidade de contatos únicos", "NUMBER_OF_UNIQUE_CONTACTS_SUBTITLE": "QTC", "QAU_NUMBER_OF_UNIQUE_ISSUES": "Quantidade de assuntos únicos", "NUMBER_OF_UNIQUE_SUBJECTS": "Quantidade de assuntos únicos", "NUMBER_OF_UNIQUE_SUBJECTS_SUBTITLE": "QAU", "LABEL_CONNECTIONS": "Conexão", "LABEL_SUBJECT_MATTER": "<PERSON><PERSON><PERSON>", "LABEL_SHOW_NULL_ATTENDANTS": "Mostrar atendentes nulos", "LABEL_WITHOUT_ATTENDANT": "Sem atendente", "LABEL_USER": "<PERSON><PERSON><PERSON><PERSON>", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_AFTER_BOT": "Média do 1º tempo de espera após a finalização do bot", "CREATE_STATS_LABEL_AVERAGE_WAITING_TIME_AVG_AFTER_BOT": "Média do tempo médio de espera após a finalização do bot", "CREATE_STATS_MESSAGE_POPOVER_PERIOD_AFTER_BOT": "Período entre a finalização do atendimento feito pelo bot e a primeira mensagem enviada pelo atendente, de acordo com os filtros", "TMEFB_AVERAGE_SERVICE_TIME_AFTER_BOT": "TMEFB - Tempo médio de espera após finalização do bot", "AVERAGE_SERVICE_TIME_SUBTITLE_AFTER_BOT": "TMEFB", "AVERAGE_SERVICE_TIME_AFTER_BOT": "Tempo médio de espera após finalização do bot", "TMEFB_AVERAGE_WAITING_TIME_AFTER_BOT": "Média do 1º tempo de espera após finalização do bot", "TOTAL": "Total", "TOOLTIP_SUFFIX_SECOND_SINGULAR": " segundo", "TOOLTIP_SUFFIX_SECOND_PLURAL": " segundos", "TOOLTIP_SUFFIX_CALL_SINGULAR": " chamado", "TOOLTIP_SUFFIX_CALL_PLURAL": " chamados", "TOOLTIP_SUFFIX_CONTACT_SINGULAR": " contato", "TOOLTIP_SUFFIX_CONTACT_PLURAL": " contatos", "TOOLTIP_SUFFIX_MESSAGE_SINGULAR": " mensagem", "TOOLTIP_SUFFIX_MESSAGE_PLURAL": " mensagens", "LABEL_FOR_DESCRIPTION_FILTER": "Exibindo dados referentes ao período de {{dias}} dia(s) ", "DATES_SELECTED_BY_FILTER": "{{string_date}}", "AND_FILTER_BY": " e filtrando por ", "ATTENDANTS": "atendentes", "DEPARTMENTS": "departamentos", "ALL_ATTENDANTS": "Todos os atendentes", "ALL_CONNECTIONS": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "ALL_TICKET_TOPICS": "Todos os assuntos do chamado", "ALL_TAGS": "<PERSON><PERSON> as tags", "SUFIX_MESSAGE": "mensagens", "SUFIX_CONTACT": "contatos", "BUTTON_TEXT_OLD_VERSION": "Acessar versão antiga", "BUTTON_TEXT_NEW_VERSION": "Acessar nova versão"}