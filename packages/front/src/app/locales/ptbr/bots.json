{"ALERT_NODE_DIALOG_TITLE": "Não é possível utilizar esse bloco no seu robô", "ALERT_NODE_DIALOG_MSG1": "Esse bloco só é funcional em conexões WhatsApp Business e seu robô está linkado em conexões diferentes.", "ALERT_NODE_DIALOG_MSG2": "As seguintes conexões estão vinculadas a esse robô e não conseguem utilizar esse fluxo:", "ALERT_NODE_DIALOG_OK": "Ok, entendi.", "AI_NODE_UNAVAILABLE_TITLE": "Você não pode utilizar o Agente Inteligente", "AI_NODE_UNAVAILABLE_MSG1": "Você ainda não tem o bloco Agente Inteligente contratado.", "AI_NODE_UNAVAILABLE_MSG2": "Para contratar o Agente Inteligente fale com o time comercial.", "AI_NODE_UNAVAILABLE_HIRE": "Contratar agora", "START_NODE": "Início do fluxo", "DRAG_AND_DROP": "<PERSON><PERSON><PERSON>", "PERSISTENT_START_NODE": "A qualquer momento", "SIMULATOR_TRIGGER_NO_TRIGGERS": "Seu fluxo principal não tem gatilhos configurados. Vincule pelo menos um gatilho no fluxo e tente novamente.", "SIMULATOR_TRIGGER_ONE_TRIGGER_EXEC": "Seu fluxo principal tem apenas 1 gatilho. Deseja executar esse caminho novamente?", "SIMULATOR_TRIGGER_ONE_TRIGGER_AUTO": "Seu fluxo principal tem apenas 1 gatilho. O simulador vai seguir o caminho configurado no fluxo.", "SIMULATOR_TRIGGER_MANY_TRIGGERS": "Seu fluxo principal tem {{count}} gatilhos. Por qual deseja seguir?", "SIMULATOR_TRIGGER_PLACEHOLDER": "Selecione", "SIMULATOR_TRIGGER_OPTION": "Gatil<PERSON> ({{name}})", "SIMULATOR_TRIGGER_EXEC_PATH": "Executar caminho", "TITLE_BOTS": "<PERSON><PERSON><PERSON>", "TITLE_BOT": "<PERSON><PERSON>", "NO_BOTS_CREATED": "nenhum rob<PERSON>", "START_CREATE_FIRST_BOT": "a seu primeiro robô", "ADD_BOT": "<PERSON><PERSON><PERSON><PERSON> rob<PERSON>", "VIEW_BOT": "<PERSON><PERSON><PERSON> rob<PERSON>", "EDIT_BOT": "<PERSON><PERSON> r<PERSON>", "DELETE_BOT": "<PERSON><PERSON><PERSON> rob<PERSON>", "BOT_ADDED_SUCCESS": "Robô adicionado com sucesso!", "BOT_ADDED_ERROR": "Erro ao adicionar um robô!", "BOT_COPY_SUCCESS": "Robô duplicado com sucesso!", "BOT_COPY_ERROR": "Erro ao duplicar um robô!", "BOT_EDITED_SUCCESS": "Robô editado com sucesso!", "BOT_EDITED_ERROR": "Erro ao editar um robô!", "BOT_DELETED_SUCCESS": "Robô deletado com sucesso!", "BOT_DELETED_ERROR": "Erro ao deletar um robô!", "BOT_SAME_NAME_ERROR": "Já existe um robô com esse nome.", "BOT_NOT_FOUND": "<PERSON>ô não encontrado.", "BOT_NAME_REQUIRED": "Nome do robô é obrigatório.", "BOT_NAME_MAX_LENGTH": "O nome do robô não pode ter mais de 255 caracteres.", "BUTTON_NEW_BOT": "Novo robô", "COLUMN_NAME": "Nome", "ACTIONS_COLUMN_DUPLICATE": "Duplicar", "ACTIONS_VIEW_STREAM": "Visualizar fluxo", "PLACEHOLDER_SEARCH_INPUT": "Busque pelo nome", "LABEL_CONTEXT_NAME": "Nome do contexto", "LABEL_INFORMATION_BOT": "Informação", "INITIAL_VERSION": "Construtor legado", "FLOWCHART": "Fluxograma", "NEW_FLOWCHART": "Novo Fluxograma", "NEW": "NOVO", "ROBOT_BUILDER_INTRO": "O novo construtor de robôs por fluxograma traz uma proposta de construtor de robôs mais simples e intuitiva para o seu dia a dia.", "ROBOT_BUILDER_NOTE": "Ao criar um robô no modo Fluxograma, só será possível editá-lo na visão de fluxograma, não sendo possível visualizar ou editar ele na versão do construtor legado.", "ROBOT_BUILDER_INTRO_LEGACY": "A versão legado do construtor é a experiência que traz o construtor cl<PERSON><PERSON><PERSON> da Digisac, sem o uso de blocos e fluxogramas para criar o bot.", "ROBOT_BUILDER_NOTE_LEGACY": "Se você tem robôs criados na versão de Construtor legado ele só poderá ser visto nessa lista e editado nessa versão. Você pode criar um novo robô na versão de fluxograma e replicar um robô já existente na versão antiga.", "NO_TRIGGER_CREATED_FOR_THIS_CONTEXT": "Nenhum gatilho criado para este contexto.", "NO_RULES_CREATED_FOR_THIS_TRIGGER": "Nenhuma regra criada para este gatilho.", "RULE_TITLE_OPTIONAL": "<PERSON><PERSON><PERSON><PERSON> da regra (opcional)", "PLACEHOLDER_TITLE": "<PERSON><PERSON><PERSON><PERSON>", "NO_CONDITIONS_CREATED_FOR_THIS_RULE": "Nenhuma condição criada para esta regra.", "NO_ACTIONS_CREATED_FOR_THIS_RULE": "Nenhuma ação criada para esta regra.", "SELECT_TRIGGER": "Selecionar gatilho", "CONFIRM_EXIT": "Deseja realmente sair?", "UNSAVED_CHANGES_WARNING": "Você tem alterações no robô que ainda não foram salvas.", "SAVE_CHANGES_ADVICE": "Salve as mudanças para não perder nada.", "CANCEL": "<PERSON><PERSON><PERSON>", "EXIT": "<PERSON><PERSON>", "LABEL_TEMPLATES": "Templates", "LABEL_SCREENING": "Triagem", "LABEL_BOT_NAME": "Nome do robô", "RUN_RULES_IN_SEQUENCE": "Executar regras em sequência", "BUTTON_MOVE_UP": "Mover para cima", "BUTTON_MOVE_DOWN": "Mover para baixo", "NAME_OF_BOT": "Nome do bot", "STOP_RESPONDING_WHEN_A_DEPARTMENT": "Parar de responder quando um departamento for selecionado?", "SELECT_DEPARTMENTS_FOR_SCREENING": "Selecione os departamentos para triagem", "NOT_UNDERSTAND_CHOOSE_ONE_OF_THE_OPTIONS": "<PERSON><PERSON> en<PERSON>, escolha uma das opções acima, por favor.", "DEAD_CONTEXT": "Contexto Morto", "DIRECTED_TO_THE_DEPARTMENT": "Você foi direcionado(a) para o departamento {{ department }} em breve um atendente entrará em contato.", "MENU_CONTEXT": "Contexto de menu", "WELCOME_SELECT_ONE_OPTIONS": "<PERSON><PERSON> vindo(a), selecione uma das opções abaixo para ser redirecionado(a) para um departamento:", "LABEL_ACTION": "Ação", "LABEL_FILE_OPTIONAL": "Arquivo (opcional)", "LABEL_MESSAGE": "Mensagem", "LABEL_MASK": "Mascara", "LABEL_DEPARTMENT": "Departamento", "ATTENDANT_OPTIONAL": "Atendente (opcional)", "CALL_SUBJECT": "Assunto do chamado", "SERVICE_SUMMARY": "Resumo do atendimento", "COMMAND_IDENTIFIER": "Identificador do comando", "LABEL_TAG": "Tag", "LABEL_TAGS": "Tags", "LABEL_CONTEXT": "Contexto", "LABEL_QUESTION": "<PERSON><PERSON><PERSON>", "LABEL_CUSTOM_FIELD": "<PERSON>var Resposta em um Campo Personalizado", "LABEL_ACCEPTANCE_TERM": "Termo de aceite", "BUTTON_REMOVE_ACTION": "Remover ação", "LABEL_TRIGGER": "<PERSON><PERSON><PERSON><PERSON>", "LABEL_RULE": "Regra", "LABEL_ACTIONS": "Ações", "LABEL_MINIMIZE": "<PERSON><PERSON><PERSON>", "LABEL_MAXIMIZE": "Maximizar", "LABEL_ADD": "<PERSON><PERSON><PERSON><PERSON>", "LABEL_REMOVE": "Remover", "LABEL_TRUE": "<PERSON><PERSON><PERSON><PERSON>", "LABEL_FALSE": "<PERSON><PERSON><PERSON>", "LABEL_VARIABLE": "Variável", "LABEL_OPERATOR": "Operador", "LABEL_VALUES": "Valores", "LABEL_VALUE": "Valor", "LABEL_SELECT_THE_DEPARTMENT": "Selecione o(s) departamento(s)", "LABEL_EMAIL": "E-mail", "LABEL_CPF": "CPF", "LABEL_BIRTH_DATE": "Data de nascimento", "BUTTON_REMOVE_CONDITION": "Remover condição", "message_text": "Texto da mensagem", "is_first_message": "É primeira mensagem do contato", "check_numbers_tags": "Verifica a quantidade de tags", "service_type": "Tipo de <PERSON>", "is_company_open": "Dentro do horário de atendimento", "is_holiday": "É feriado", "is_message_from_me": "Mensagem foi enviada pelo atendente", "is_confirmation_term": "Contato com termo confirmado", "is_first_ticket_message": "É primeira mensagem do chamado", "ticket_department": "Chamado est<PERSON> no(em um dos) departamento(s)", "api_signal_flag": "Flag do gatilho Sinal da API", "count_session": "Contador de <PERSON>ão", "$eq": "Igual", "$in": "Está incluso na lista", "$likeSome": "Contem alguma palavra da lista", "$likeEvery": "<PERSON><PERSON><PERSON> as pala<PERSON><PERSON> da lista", "$neq": "Não é igual", "$nin": "Não está incluso na lista", "$notLikeSome": "Não contem alguma palavra da lista", "$notLikeEvery": "Não contém nenhuma das palavras da lista", "$format": "Está no formato", "$notFormat": "Não está no formato", "INITIAL_CONTEXT": "Contexto inicial", "FALLBACK_CONTEXT": "Contexto de contingência", "EVERY_CONTEXT": "Contexto persistente", "SEND_MESSAGE": "Enviar mensagem", "TRANSFER_TICKET": "<PERSON><PERSON> chamado", "CLOSE_TICKET": "<PERSON><PERSON><PERSON>", "CLOSE_TICKET_DESCRIPTION": "Defina um assunto e uma mensagem como resumo do chamado a ser fechado.", "ADD_TAG": "Adicionar tag", "REMOVE_TAG": "Remover tag", "SET_CONTEXT": "Definir contexto", "RESET_CONTEXT": "Redefinir contexto", "UNSUBSCRIBE_FROM_CAMPAIGN": "Desinscrever contato de campanhas", "SEND_WEBHOOK_COMMAND": "<PERSON>viar comando (Webhook)", "SEND_QUESTION": "<PERSON>vio <PERSON> a<PERSON>", "SEND_TERM": "Enviar termo de aceite", "CONFIRM_TERM": "Confirmar termo de aceite", "WRITE_CUSTOM_FIELD": "<PERSON><PERSON><PERSON> Personalizado", "MESSAGE_RECEIVED": "Recebimento de mensagem", "TICKET_OPENED": "Abertura de chamado automática", "MANUAL_TICKET_OPENED": "Abertura de chamado manual", "TICKET_CLOSED": "Após fechamento de chamado", "TICKET_BEFORE_CLOSE": "Antes do fechamento do chamado", "TICKET_INACTIVE": "<PERSON><PERSON><PERSON>", "BOT_INACTIVE": "Chamado inativo dentro do robô", "WIDGET_MESSAGE_RECEIVED": "Recebimento de mensagem via widget", "USER_MESSAGE_SENT": "<PERSON><PERSON> de mensagem pelo atendente", "ENTER_CONTEXT": "Entrar em um contexto", "LEAVE_CONTEXT": "<PERSON><PERSON> <PERSON> um <PERSON>o", "CONTACT_CREATED": "Novo contato criado pelo plugin (Webchat)", "CONTACT_RECONNECTED": "Contato reconectado pelo plugin (Webchat)", "API_SIGNAL": "<PERSON><PERSON>", "LABEL_@INIT": "Contexto inicial", "LABEL_@FALLBACK": "Contexto de contingência", "LABEL_@EVERY": "Contexto persistente", "ANONYMOUS_USER_CREATED_WEBCHAT": "<PERSON><PERSON><PERSON><PERSON> an<PERSON> criado pelo webchat", "AUTOMATIC_MESSAGE": "Mensagem automática", "POPOVER_HEADER": "Informações sobre o comportamento", "POPOVER_BODY": "Essa opção desmarcada permite inserir uma nova mensagem para o feriado. Caso habilitada irá pegar a mensagem pré cadastrada no feriado.", "NAME_ROBOT_IS_REQUIRED": "O nome do robô deve ser preenchido", "NAME_CONTEXT_IS_REQUIRED": "O nome do contexto deve ser preenchido", "ERROR_MESSAGE_TRIGGER_NOT_FOUND": "O Contexto deve possuir pelo menos um gatilho criado", "ERROR_MESSAGE_RULE_NOT_FOUND": "Encontramos gatilhos vazios, você deve incluir pelo menos uma regra para cada gatilho", "ERROR_MESSAGE_RULES_AND_CONDITIONS": "O contexto {{ botname }} possui regras sem ações", "GO_BACK_YOUR_CHANGES_NOT_SAVED": "Tem certeza que deseja voltar? Suas alterações não serão salvas.", "DO_YOU_WANT_DELETE_CONTEXT": "Tem certeza que deseja deletar o contexto: {{ contextName }} ?", "THERE_WAS_ERROR_CHANGING_THE_BOT": "Ocorreu um erro ao alterar o bot", "DELETED_CONTEXT_SUCCESS": "Contexto deletado com sucesso", "CREATED_CONTEXT_SUCCESS": "Contexto criado com sucesso", "TEMPLATE_CREATED_SUCCESS": "Template de triagem criado com sucesso", "ROBOT_DATA_CHANGED_SUCCESS": "Dados do robô alterado com sucesso", "THERE_WAS_ERROR_CREATING_CONTEXT": "Ocorreu um erro ao criar o contexto, entre em contato com nosso suporte", "BUTTON_DELETE": "Deletar", "BUTTON_DIALOG_DELETE": "Excluir ação do robô", "BUTTON_DISCARD_CHANGES": "Descartar alterações", "BUTTON_CLOSE": "<PERSON><PERSON><PERSON>", "BUTTON_CANCEL": "<PERSON><PERSON><PERSON>", "BUTTON_CONTINUE": "<PERSON><PERSON><PERSON><PERSON>", "BUTTON_KEEP_EDITING": "Con<PERSON><PERSON>r editando", "BUTTON_RETURN": "Voltar", "PLACEHOLDER_ENTER_ROBOT_NAME": "Digite o nome do robô", "BUTTON_ADD_CONTEXT": "<PERSON><PERSON><PERSON><PERSON> contexto", "CREATE_SCREENING_TEMPLATE": "Criar template de triagem", "CANCEL_CHANGES": "Cancelar alterações", "BUTTON_SAVE": "<PERSON><PERSON>", "LABEL_NEW_CONTEXT": "Novo contexto", "LABEL_ZOOM_IN": "Aumentar zoom", "LABEL_ZOOM_OUT": "Diminuir zoom", "LABEL_CENTRALIZE": "Centralizar", "LABEL_CONTEXTS": "Contextos", "MESSAGE_ALERT_ABOUT_TEMPLATE": "<PERSON>que atendo, pois a criação do template de triagem irá sobrescrever os contextos atuais", "CLOSE_CONNECTIONS": "<PERSON><PERSON><PERSON>", "SHOW_CONNECTIONS": "<PERSON><PERSON><PERSON>", "LABEL_FLOWS": "Fluxos", "LABEL_TRIGGERS": "Gatilhos", "LABEL_RULES": "Regras", "LABEL_CONDITIONS": "Condições", "LABEL_CONDITION": "Condição", "BUTTON_ADD_TRIGGER": "<PERSON><PERSON><PERSON><PERSON>", "BUTTON_ADD_ACTION": "Adicionar ação", "BUTTON_ADD_CONDITION": "Adicionar condi<PERSON>", "BUTTON_ADD_RULE": "<PERSON><PERSON><PERSON><PERSON> regra", "LABEL_SELECT_VALUE": "Selecione o valor", "LABEL_SELECT_THE_LOGICAL_OPERATOR": "Selecione o operador lógico", "LABEL_CONDITION_SMALL_LETTER": "condição", "LABEL_ACTIONS_SMALL_LETTER": "ação", "ERROR_MESSAGE_EMPTY_VALUES": "Todos os campos da {{ value }} são obrigatórios.", "MASK_POPOVER_INFO_HEADER": "A mascara é utilizada somente para conexões WebChat", "MASK_POPOVER_INFO_BODY": "Ao criar uma mascara numérica, utilize  nela apenas o número '9'  Ex: 999.999.999-99 ", "MESSAGE_FIELD_MESSAGE_REQUIRED": "Existem campos em suas ações que não foram preenchidos.", "MESSAGE_DEPARTMENT_USER_FIELD_REQUIRED": "O campo departamento é obrigatório na transferência de chamado", "MESSAGE_TICKET_SUBJECT_FIELD_REQUIRED": "O campo assunto de chamado é obrigatório", "MESSAGE_WEBHOOK_COMMAND_REQUIRED": "Identificador do comando Webhook é obrigatório", "MESSAGE_TAG_FIELD_REQUIRED": "É obrigatório selecionar pelo menos 1 tag", "MESSAGE_CONTEXT_FIELD_REQUIRED": "É obrigatório selecionar o contexto", "MESSAGE_QUESTION_FIELD_REQUIRED": "É obrigatório selecionar a avaliação", "MESSAGE_CUSTOM_FIELD_REQUIRED": "É obrigatório selecionar o campo personalizado", "MESSAGE_ALERT_SAVE_NAME_CONTEXT": "Após alterar o nome do contexto é necessário salvar as alterações no canto superior direito.", "MESSAGE_SEND_ACCEPTANCE_TERM_FIELD_MUST_BE_IN_TICKET_OPENED": "É obrigatório que a ação de envio de termo de aceite esteja no gatilho de abertura de chamado automática.", "MESSAGE_TO_EXISTS_CONFIRM_TERM_MUST_EXISTS_SEND_TERM": "Para existir a ação Confirmar termo de aceite, é obrigatório que exista a ação Enviar termo de aceite", "LABEL_ALERT_INACTIVE_OLD_BOT": "Essa tela do robô será descontinuada em breve, utilize o visualizar fluxo para ter uma experiência melhor na navegação do robô", "ACCEPTANCE_TERMS_MESSAGE_RESET_WARNING": "Atenção! Toda alteração realizada no termo de aceite vinculado ao rob<PERSON>, os contatos deverão aceitar o termo novamente.", "MESSAGE_SURE_CONTINUE": "Deseja continuar mesmo assim?", "MODAL_MESSAGE_ERROR": "Ocorreu um erro ao salvar o robô...", "MODAL_DUPLICATE_NAME": "Já existe um robô com esse nome.", "IS_INTERACTIVE_MESSAGE_ON_BOT": "Mensagem interativa? (Apenas para Whatsapp business)", "TITLE_SAVE_CUSTOM_FIELD": "Salvar em campo personalizado", "DESCRIPTION_SAVE_CUSTOM_FIELD": "Configure em qual campo personalizado deseja salvar a informação coletada.", "TITLE_SEND_SURVEY": "Enviar pesquisa de avaliação", "DESCRIPTION_SEND_SURVEY": "Envie pesquisas de avaliação e obtenha respostas dos seus clientes", "LABEL_SELECT_SURVEY": "Pesquisa de avaliação", "HELP_MATERIAL_TEXT": "Acessar material de ajuda", "PLACEHOLDER_TYPE_HERE": "Digite aqui", "SECTION_IF_RESPONSE_INVALID": "Se a resposta for inválida", "LABEL_TRY_AGAIN": "Tente novamente", "TRY_AGAIN_0": "0 vezes", "TRY_AGAIN_1": "1 vez", "TRY_AGAIN_2": "2 vezes", "TRY_AGAIN_3": "3 vezes", "TRY_AGAIN_4": "4 vezes", "TRY_AGAIN_5": "5 vezes", "LABEL_INVALID_RESPONSE_MESSAGE": "Mensagem alternativa de resposta inválida", "PLACEHOLDER_SELECT": "Selecione", "LABEL_SAVED_SUCCESS": "Salvo com sucesso", "LABEL_FIELD_NOT_SAVED": "Campo não foi salvo", "TEXT_PREFIX_FORMAT_ACCEPTED": "Formato aceito:", "TEXT_EXAMPLE_HOUR": "00:00", "TEXT_EXAMPLE_HOUR_RANGE": "00:00-00:00", "TEXT_EXAMPLE_DATE": "dd/mm/aaaa", "TEXT_EXAMPLE_DATE_RANGE": "dd/mm/aaaa-dd/mm/aaaa", "TEXT_EXAMPLE_MONETARY": "0,00/BRL.", "TEXT_EXAMPLE_NUMERIC": "Apenas números.", "TEXT_EXAMPLE_PHONE": "+55 00000000000", "TEXT_EXAMPLE_POSTAL_CODE": "BR/00000000", "TEXT_EXAMPLE_CNPJ": "00000000000000", "TEXT_EXAMPLE_CPF": "00000000000", "TEXT_RANGE_BETWEEN": "Valor deve estar entre {{min}} e {{max}}", "TEXT_EXAMPLE_CHECKBOX": "Separe por vírgulas para adicionar várias.", "TEXT_EXAMPLE_TYPE": "Máximo de 255 caracteres.", "BOT_SIMULATOR": "<PERSON>mu<PERSON><PERSON> <PERSON>", "TEXT_SIMULATOR_INVALID_WRITE_CUSTOM_FIELD_NODE": "O campo \"{{name}}\" não foi salvo, pois não está no formato aceito: {{format}}", "TEXT_SIMULATOR_WRITE_CUSTOM_FIELD_NODE": "\"{{name}}\" salvo com sucesso", "TEXT_SIMULATOR_INVALID_WRITE_CUSTOM_FIELD_NODE_CHECKBOX_LIST": "O campo \"{{name}}\" não foi salvo, pois não está no formato aceito.", "FLOW_WITHOUT_CONTINUITY_TITLE": "Fluxo sem continuidade", "FLOW_WITHOUT_CONTINUITY_DESCRIPTION": "Parece que um dos fluxos do robô não possui um caminho definido para todos os cenários possíveis. Isso pode fazer com que o bot pare inesperadamente durante uma conversa.", "FLOW_WITHOUT_CONTINUITY_QUESTION": "Deseja aplicar mesmo assim?", "BUTTON_CONFIRM": "Confirmar", "BOT_SIMULATOR_FOOTER": "<PERSON>mu<PERSON><PERSON> <PERSON>", "ADD_TAGS": "Adicionar tags", "ADD_TAGS_DESCRIPTION": "Adicione tags a um contato", "LABEL_TAGS_DESCRIPTION": "Selecione quais tags serão adicionadas ao contato", "LABEL_ACTION_STEP_DESCRIPTION": "Defina até 3 caminhos para o robô realizar.", "LABEL_AI_INITIAL_STEP_DESCRIPTION": "<PERSON><PERSON><PERSON><PERSON> as configurações iniciais do agente inteligente", "LABEL_AI_KNOWLEDGE_BASE_DESCRIPTION": "Criamos uma sugestão de instrução com base nas informações da sua empresa e produto", "LABEL_AI_ACTION_STEP_DESCRIPTION": "Defina os comandos que vão guiar o agente inteligente.", "LABEL_AI_AGENT": "Agente inteligente", "BUTTON_CANCEL_CHANGES": "Cancelar alterações no elemento", "HOVER_SEND_MESSAGE_DESCRIPTION": "Envia uma mensagem de texto, imagem ou arquivo.", "HOVER_SEND_TEMPLATE_MESSAGE_DESCRIPTION": "Mensagens interativas usam um template existente e incluem listas ou botões.", "HOVER_SEND_TEMPLATE_MESSAGE_DESCRIPTION_2": "É o equivalente ao 'Enviar template' do construtor legado.", "HOVER_SEND_HOLIDAY_MESSAGE_DESCRIPTION": "Manda uma mensagem referente ao feriado.", "HOVER_SEND_QUESTION_DESCRIPTION": "Manda uma pesquisa de avaliação.", "FLOW_CONTROL": "Controle de fluxo", "HOVER_FLOW_TRIGGER_DESCRIPTION": "Evento que define quando outros blocos serão acionados.", "HOVER_FLOW_CONDITION_DESCRIPTION": "Verifica cenários e direciona o fluxo conforme as regras.", "HOVER_FLOW_AI_DESCRIPTION": "Use IA para criar um chatbot que atende seus clientes em conversas abertas.", "HOVER_FLOW_PERSISTENT_START_DESCRIPTION": "Permite que uma ação seja realizada a qualquer momento do fluxo, independente de onde o usuário esteja.", "HOVER_FLOW_PERSISTENT_START_DESCRIPTION_2": "É o equivalente ao 'Contexto persistente' do construtor legado.", "SECTION_TICKETS": "<PERSON><PERSON><PERSON>", "HOVER_TRANSFER_TICKET_DESCRIPTION": "Transfere o chamado em andamento.", "HOVER_CLOSE_TICKET_DESCRIPTION": "Encerra o chamado em andamento.", "SECTION_CONTACTS_AND_DATA": "Contatos e dados do cliente", "HOVER_ADD_TAG_DESCRIPTION": "Insere tags ao contato.", "HOVER_REMOVE_TAG_DESCRIPTION": "Retira tags do contato.", "HOVER_WRITE_CUSTOM_FIELD_DESCRIPTION": "Permite salvar a última mensagem enviada pelo cliente em um campo personalizado da plataforma.", "HOVER_WRITE_CUSTOM_FIELD_DESCRIPTION_2": "É o equivalente ao 'Gravar campos' do construtor legado.", "HOVER_UNSUBSCRIBE_FROM_CAMPAIGNS_DESCRIPTION": "Remove um contato de uma campanha.", "SECTION_AUTOMATIONS": "Automações", "HOVER_SEND_WEBHOOK_DESCRIPTION": "Envia dados para sistema externo a partir de URL configurada.", "HOVER_RESET_NODE_DESCRIPTION": "Retorna ao primeiro bloco do fluxo e reinicia o caminho.", "HOVER_DEAD_END_NODE_DESCRIPTION": "Para instantaneamente todo o fluxo principal.", "DEAD_END_NODE_TITLE": "Parar fluxo principal", "DRAG_BLOCKS_TO_BUILD_BOT": "<PERSON><PERSON><PERSON> os blocos para construir seu robô", "BOT_VERSIONS_HISTORY_TITLE": "Histórico de versões do robô", "BOT_VERSIONS_HISTORY_EMPTY": "Você não tem nenhuma versão do robô para ser exibida no histórico.", "BOT_VERSIONS_HISTORY_EMPTY_SUBTITLE": "Sempre que uma nova versão for salva ela será exibida aqui.", "BOT_VERSIONS_HISTORY_LOAD_MORE": "<PERSON><PERSON><PERSON> mais", "BOT_VERSIONS_HISTORY_STATUS_DRAFT": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BOT_VERSIONS_HISTORY_STATUS_PUBLISHED_CURRENT": "Publicada e atual", "BOT_VERSIONS_HISTORY_STATUS_PUBLISHED": "Publicada", "CONFIRM_UNSAVED_CHANGES_TITLE": "As mudanças não foram aplicadas", "CONFIRM_UNSAVED_CHANGES_DESCRIPTION": "Você tem alterações no bloco de ação que ainda não foram aplicadas.", "CONFIRM_UNSAVED_CHANGES_QUESTION": "<PERSON><PERSON><PERSON> aplicar as <PERSON><PERSON><PERSON><PERSON>?", "CONFIRM_UNSAVED_CHANGES_DISCARD": "Descar<PERSON>", "CONFIRM_UNSAVED_CHANGES_APPLY": "Aplicar", "CONFIRM_UNSAVED_CHANGES_APPLY_TOOLTIP": "Aplicar alterações no elemento", "FOOTER_CANCEL_CHANGES_TOOLTIP": "Cancelar alterações no elemento", "FOOTER_APPLY_CHANGES_TOOLTIP": "Aplicar alterações no elemento", "CONDITION_EDIT_TITLE": "Configurar condição", "CONDITION_EDIT_DESCRIPTION": "Configure condicionais de e/ou no seu robô.", "CONDITION_EDIT_BLOCK_CONDITION": "Condição do bloco", "CONDITION_EDIT_TYPE_LABEL": "Tipo de condição", "CONDITION_EDIT_TYPE_PLACEHOLDER": "Selecione o tipo de condição", "CONDITION_EDIT_TYPE_AND": "Condição “e”", "CONDITION_EDIT_TYPE_OR": "Condição “ou”", "CONDITION_EDIT_TYPE_AND_DESCRIPTION": "<PERSON><PERSON> as condições abaixo precisam ser atendidas.", "CONDITION_EDIT_TYPE_OR_DESCRIPTION": "Uma das condições abaixo precisa ser atendida.", "CONDITION_EDIT_CONDITION_LABEL": "Condição {{index}}", "CONDITION_EDIT_ADD_ANOTHER_CONDITION": "Adicionar outra condição", "FORM_REQUIRED_FIELD": "Campo obrigatório", "CONDITION_NODE_RESULTS": "Resul<PERSON><PERSON>", "CONDITION_NODE_TRUE": "<PERSON><PERSON><PERSON><PERSON>", "CONDITION_NODE_FALSE": "<PERSON><PERSON><PERSON>", "CONDITION_NODE_ALL_CONDITIONS": "<PERSON><PERSON> as condições devem ser atendidas.", "CONDITION_NODE_ONE_CONDITION": "Uma das condições deve ser atendida.", "CONDITION_NODE_BLOCK_CONDITIONS": "Condições do bloco", "CONDITION_NODE_SEE_ALL_CONDITIONS": "<PERSON><PERSON> <PERSON><PERSON> as condiçõ<PERSON>", "TARGET_INPUT_SELECT_PLACEHOLDER": "Selecione", "TARGET_INPUT_NUMBER_PLACEHOLDER": "Digite o valor", "TARGET_INPUT_TEXT_PLACEHOLDER": "Digite o valor", "START_NODE_TITLE": "Início do fluxo", "PERSISTENT_START_NODE_TITLE": "A qualquer momento", "REMOVE_TAGS_TITLE": "Remover tags", "REMOVE_TAGS_DESCRIPTION": "Remova tags de um contato", "REMOVE_TAGS_INSTRUCTION": "Selecione quais tags serão removidas do contato", "RESET_NODE_TITLE": "Voltar ao início do fluxo", "SEND_ACCEPTANCE_TERMS_NODE_LABEL_RESPONSES": "Respostas", "SEND_ACCEPTANCE_TERMS_NODE_ANSWER_ACCEPT": "<PERSON><PERSON>", "SEND_ACCEPTANCE_TERMS_NODE_ANSWER_REJECT": "Não aceito", "SEND_HOLIDAY_MESSAGE_NODE_TITLE": "Enviar mensagem de feriado", "MESSAGE_EDIT_PLACEHOLDER_TEXT": "Digite aqui", "MESSAGE_EDIT_VARIABLES_HINT": "Digite '{{' para ver a lista de variáveis disponíveis.", "MESSAGE_EDIT_VARIABLES_HELP": "Em caso de dúvidas sobre as variáveis", "MESSAGE_EDIT_VARIABLES_HELP_BUTTON": "clique aqui.", "MESSAGE_EDIT_LABEL_SET_ANSWERS": "Definir recebimento de respostas", "MESSAGE_EDIT_LABEL_ADD_ANSWER": "Adicionar outra resposta", "MESSAGE_EDIT_LABEL_REMOVE_ANSWER": "Remover resposta", "MESSAGE_EDIT_PLACEHOLDER_ANSWER": "Digite aqui a resposta", "MESSAGE_EDIT_LABEL_ANSWERS_HINT": "Defina possíveis respostas do seu cliente e direcione ele para diferentes etapas do robô de acordo com cada resposta específica", "MESSAGE_EDIT_LABEL_DEFINE_MESSAGE": "Defina uma mensagem de texto, imagem e arquivo para ser enviada", "MESSAGE_NODE_HEADER_TITLE": "Enviar mensagem", "MESSAGE_NODE_LABEL_ANSWERS": "Respostas", "MESSAGE_NODE_LABEL_OTHER_ANSWERS": "Outras respostas", "SEND_QUESTION_NODE_HEADER_TITLE": "Enviar pesquisa de avaliação", "TEMPLATE_MESSAGE_EDIT_NODE_NAME": "Enviar mensagem interativa", "TEMPLATE_MESSAGE_EDIT_HEADER_TITLE": "Enviar mensagem interativa", "TEMPLATE_MESSAGE_EDIT_DESCRIPTION": "Selecione uma mensagem interativa já criada ou então configure uma nova.", "TEMPLATE_MESSAGE_EDIT_ATTENTION": "Atenção: Mensagens interativas só podem ser enviadas em conexões WhatsApp Business API.", "SEND_WEBHOOK_NODE_HEADER_TITLE": "Enviar webhook", "SEND_WEBHOOK_NODE_DESCRIPTION": "Use webhooks para enviar dados para outro sistema ao ocorrer um evento.", "SEND_WEBHOOK_NODE_PLACEHOLDER_COMMAND": "Digite aqui", "START_NODE_HEADER_TITLE": "Início do fluxo", "TRANSFER_TICKET_NODE_DESCRIPTION": "Selecione um departamento e/ou atendente para transferir um chamado.", "TRANSFER_TICKET_NODE_DEPARTMENT_HINT": "Selecione o departamento que irá receber o chamado", "TRANSFER_TICKET_NODE_USER_LABEL": "Atendente (Opcional)", "TRANSFER_TICKET_NODE_USER_HINT": "Selecione o atendente que irá receber o chamado", "TRIGGER_EDIT_HEADER_TITLE": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON>", "TRIGGER_EDIT_DESCRIPTION": "Defina um gatilho para acionar uma atividade do robô", "TRIGGER_EDIT_LABEL_TRIGGER": "<PERSON><PERSON><PERSON><PERSON>", "TRIGGER_EDIT_PLACEHOLDER_SELECT": "Selecione", "TRIGGER_EDIT_BOT_INACTIVE_HINT": "Esse gatilho só pode ser utilizado no 'A qualquer momento'", "TRIGGER_EDIT_LABEL_INACTIVITY_TIME": "Tempo de inatividade", "TRIGGER_EDIT_PLACEHOLDER_TIME": "Digite o tempo", "TRIGGER_EDIT_LABEL_TIME_UNIT": "Unidade de tempo", "TRIGGER_EDIT_OPTION_MINUTES": "<PERSON><PERSON><PERSON>", "TRIGGER_EDIT_OPTION_HOURS": "<PERSON><PERSON>", "UNSUBSCRIBE_FROM_CAMPAIGNS_NODE_HEADER_TITLE": "Retirar contato de camp<PERSON>ha", "WRITE_CUSTOM_FIELD_NODE_HEADER_TITLE": "Gravar campo personalizado", "DELETE_DIALOG_WARNING": "Ao deletar uma caixa de ação do robô, o conteúdo configurado será perdido e não será possível recuperá-lo.", "DUPLICATE_NODE_LABEL": "Duplicar bloco", "DELETE_NODE_LABEL": "Excluir bloco", "BOT_SIMULATOR_ALERT": "Informações, textos e mensagens de escolha de fluxos serão visíveis somente para você durante o uso do simulador de robôs.", "BOT_SIMULATOR_LOADING": "O simulador está sendo reiniciado, aguarde...", "BOT_SIMULATOR_HEADER": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Digisac", "BOT_SIMULATOR_STATUS_ONLINE": "Online", "BOT_SIMULATOR_RESTARTED": "O simulador foi reiniciado.", "BOT_SIMULATOR_PLACEHOLDER_MESSAGE": "Digite sua mensagem", "BOT_SIMULATOR_ERROR_RESTART": "Ocorreu uma falha ao reiniciar o robô!", "CHAT_EVENT_TRANSFER_TICKET_NODE": "O chamado foi transferido.", "CHAT_EVENT_CLOSE_TICKET_NODE": "O chamado foi fechado.", "CHAT_EVENT_RESET_NODE": "Você chegou ao bloco de voltar ao início.\nO fluxo foi reiniciado.", "CHAT_EVENT_DEAD_END_NODE": "O fluxo do robô foi parado.", "CHAT_EVENT_SEND_WEBHOOK_NODE": "O webhook foi enviado.", "CHAT_EVENT_ADD_TAG_NODE": "As tags foram adicionadas.", "CHAT_EVENT_REMOVE_TAG_NODE": "As tags foram removidas.", "CHAT_EVENT_WRITE_CUSTOM_FIELD_NODE": "As informações foram salvas em um campo personalizado.", "CHAT_EVENT_SEND_QUESTION_NODE": "A pesquisa de avaliação foi enviada.", "CHAT_EVENT_UNSUBSCRIBE_FROM_CAMPAIGNS_NODE": "O contato foi retirado da campanha.", "CONFIRM_SIMULATOR_DIALOG_TITLE_GO": "Deseja realmente sair e ir para o simulador?", "CONFIRM_SIMULATOR_DIALOG_TITLE_SAVE_GO": "<PERSON>eja salvar e ir para o simulador?", "CONFIRM_SIMULATOR_DIALOG_DESC_GO": "Para entrar no modo simulador todas as alterações que foram feitas serão salvas como rascunho, mas não serão publicadas.", "CONFIRM_SIMULATOR_DIALOG_DESC_SAVE_GO": "Para entrar no modo simulador, seu rob<PERSON> e todos os blocos configurados deverão estar salvos como rascunho.", "CONFIRM_SIMULATOR_DIALOG_BTN_GO": "Ir para simulador", "CONFIRM_SIMULATOR_DIALOG_BTN_SAVE_GO": "Salvar e abrir simulador", "BOT_SIMULATOR_YOU": "Você", "SIMULATOR_CONDITION_LABEL": "Seu bloco de condição tem dois cenários possíveis. Qual deles você deseja simular?", "SIMULATOR_CONDITION_PLACEHOLDER": "Selecione", "SIMULATOR_CONDITION_TRUE": "Condição verdadeira", "SIMULATOR_CONDITION_FALSE": "Condição falsa", "BOT_NAME_CHANGED_SUCCESS": "Nome do robô alterado com sucesso", "BOT_NAME_ALREADY_EXISTS": "Já existe um robô com esse nome", "BOT_NAME_CHANGE_ERROR": "Ocorreu uma falha ao alterar o nome do robô!", "BOT_SAVE_SUCCESS": "Robô salvo com sucesso", "BOT_PUBLISH_SUCCESS": "Nova versão do robô publicada com sucesso.", "LINK_CONNECTION": "Vincular conexão", "BOT_SAVE_ERROR": "Ocorreu uma falha ao salvar o robô!", "NODE_CHANGES_APPLIED": "Alterações aplicadas no elemento", "TRIGGER_LINK_ONLY_ALLOWED": "O vínculo do bloco que utiliza o gatilho \"{{triggerName}}\" só é permitido com o bloco \"{{startNodeName}}\".", "TRIGGER_DUPLICATED_LINK": "Você já tem um gatilho igual a esse linkado no bloco \"{{startNodeName}}\". Não é possível utilizar dois gatilhos iguais linkados ao mesmo bloco.", "CONFIRM_BOT_NAME_TITLE": "Nome do robô", "CONFIRM_BOT_NAME_LABEL": "Nome do robô", "CONFIRM_BOT_NAME_TOOLTIP": "O nome do robô não será exibido para o cliente, apenas na listagem de robôs da Digisac.", "CONFIRM_BOT_NAME_PLACEHOLDER": "Digite aqui", "CONFIRM_BOT_NAME_CONFIRM": "Confirmar", "CONFIRM_SAVE_NAVIGATION_TITLE": "Deseja realmente sair?", "CONFIRM_SAVE_NAVIGATION_DESCRIPTION_1": "Você fez alterações que não estão salvas.", "CONFIRM_SAVE_NAVIGATION_DESCRIPTION_2": "Deseja salvar como rascunho antes de sair?", "CONFIRM_SAVE_NAVIGATION_DISCARD": "Descartar alterações", "CONFIRM_SAVE_NAVIGATION_SAVE_AND_EXIT": "<PERSON>var rascunho e sair", "NODE_NOT_CONFIGURED": "Seu bloco não foi configurado", "NODE_CONFIG_NOT_FINISHED": "A configuração do seu bloco não foi finalizada e não podemos salvar ele de forma incompleta.", "NODE_CONFIG_FINISH_OR_DISCARD": "Deseja finalizar a configuração ou descartar e sair?", "NODE_FINISH_CONFIG_TITLE": "Finalizar configuração do bloco", "NODE_FINISH_CONFIG": "Finalizar configuração", "NODE_DISCARD_AND_EXIT_TITLE": "Descartar alterações e sair", "NODE_DISCARD_AND_EXIT": "Descartar e sair", "INVALID_TRIGGER_CONNECTION": "O vínculo do bloco que utiliza o gatilho {{ triggerName }} só é permitido com o bloco {{ startNodeName }}", "TRIGGER_EDIT_ERROR_INVALID_TRIGGER": "Não é possível utilizar esse gatilho no seu robô.", "TRIGGER_EDIT_ERROR_WEBCHAT_ONLY": "Esse gatilho só funciona em conexões de Webchat mas o robô que você está editando está vinculado a outros tipos de conexões.", "TRIGGER_EDIT_ERROR_INVALID_TRIGGER_LINK": "O vínculo do bloco que utiliza o gatilho \"{{triggerName}}\" só é permitido com o bloco \"{{startNodeName}}\".", "TRIGGER_EDIT_ERROR_DUPLICATED_TRIGGER": "Você já tem um gatilho de \"{{duplicatedTriggerName}}\" vinculado. Só é possível utilizar um tipo de gatilho linkado no bloco \"{{startNodeName}}\" por vez.", "TRIGGER_EDIT_ERROR_WRITE_CUSTOM_FIELD_TRIGGER_LINK": "Você já possui um gatilho de \"{{triggerType}}\" neste fluxo. Não é possível estabelecer uma conexão entre um bloco de \"Salvar em campo personalizado\" e um gatilho de \"{{triggerType}}\".", "EDIT_NAME": "Editar nome", "PUBLISHED_VERSION": "Versão publicada", "DRAFT_MODE": "<PERSON><PERSON> ras<PERSON>o", "LAST_UPDATE": "Última atualização", "BOT_VERSION_HISTORY": "Histórico de versões do robô", "PUBLISH_VERSION_TITLE": "Publicar nova versão", "PUBLISH_VERSION_WARNING": "Ao publicar uma nova versão, o fluxo atual será perdido. <PERSON><PERSON> as seguintes conexões que utilizam esse fluxo:", "PUBLISH_VERSION_MORE_CONNECTIONS": "e mais {{count}} cone<PERSON><PERSON><PERSON>.", "PUBLISH_VERSION_NEED_CONNECTION": "Ao publicar o robô você precisa vincular a uma conexão para que ele realize os atendimentos.", "PUBLISH_VERSION_CURRENT_VERSION": "Versão atual do robô", "PUBLISH_VERSION_PUBLISHED_AT": "Publicada em {{date}}, às {{time}}.", "PUBLISH_VERSION_NEW_VERSION": "Nova versão", "PUBLISH_VERSION_LAST_MODIFIED": "Última modificação em {{date}}, às {{time}}.", "PUBLISH": "Publicar", "VARIABLES_HELPER_TITLE": "Variáveis disponíveis", "VARIABLES_HELPER_SUBTITLE": "<PERSON><PERSON><PERSON> as variáveis disponíveis para serem utilizadas no bloco \"{{nodeName}}\".", "VARIABLES_HELPER_CONTACT_NAME_DESC": "Resgata o nome do cliente para a conversa, na seguinte ordem:", "VARIABLES_HELPER_CONTACT_NAME_1": "1º - Nome salvo na plataforma;", "VARIABLES_HELPER_CONTACT_NAME_2": "2º - Nome do perfil do WhatsApp;", "VARIABLES_HELPER_CONTACT_NAME_3": "3º - Nome salvo na agendas;", "VARIABLES_HELPER_CONTACT_NAME_4": "4º - Caso não tenha nenhuma das anteriores, puxa o número do contato.", "VARIABLES_HELPER_CONTACT_NAME_DIGISAC_DESC": "Resgata o nome do cliente salvo na plataforma Digisac ou então o número do contato.", "VARIABLES_HELPER_CONTACT_NAME_CALENDAR_DESC": "Resgata o nome do cliente salvo na sua agenda ou então o número do contato.", "VARIABLES_HELPER_CONTACT_NAME_WHATSAPP_DESC": "Resgata o nome do cliente salvo no WhatsApp ou então o número do contato.", "VARIABLES_HELPER_PROTOCOL_NUMBER_DESC": "Resgata o protocolo de atendimento.", "VARIABLES_HELPER_CONTACT_NUMBER_DESC": "Resgata o número do contato. As conexões Instagram, Telegram e E-mail não trazem essa informação, então essa variável ficará vazia.", "VARIABLES_HELPER_GREETING_DESC": "Insere uma saudação na mensagem, podendo ser bom dia, boa tarde ou boa noite. Essa variável puxa a saudação de acordo com o fuso horário da plataforma.", "VARIABLES_HELPER_CAPITALIZED_GREETING_DESC": "Insere uma saudação na mensagem começando com a letra maiúscula, podendo ser <PERSON>, <PERSON><PERSON> tarde ou Boa noite. Essa variável puxa a saudação de acordo com o fuso horário da plataforma."}