interface Permission {
  createdAt: string
  deletedAt: string | null
  description: string | null
  id: string
  name: string
  type: string
  updatedAt: string
  role_permissions?: {
    createdAt: string
    permissionId: string
    roleId: string
    updatedAt: string
  }
}

export interface Role {
  accountId: string
  createdAt: string
  displayName: string
  id: string
  isAdmin: boolean
  updatedAt: string
  permissions: Permission[]
  usersCount?: number
}
