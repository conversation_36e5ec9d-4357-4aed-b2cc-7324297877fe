import { Service } from './Service'
import { BotVersion } from './BotVersion'

type BotCreatedVersion = 'v1' | 'v2' | 'v3'

export interface BotLimitations {
  hasInteractiveMessage?: boolean
  hasWebchatTrigger?: boolean
}

export interface Bot {
  id: string
  name: string
  settings: {
    sequentialRuleExecution?: boolean
    botCreatedVersion?: BotCreatedVersion
    limitations?: BotLimitations
  }
  contexts: {
    name: string
    id: string
    triggers: {
      rules: {
        title: string
        actions: {
          data: {
            content: string
          }
          type: {
            name: string
            value: string
          }
        }[]
        conditions: any[]
        fallbackActions: any[]
      }[]
      trigger: {
        name: string
        type: string
      }[]
    }[]
  }[]
  flowJson: {
    nodes?: any[]
    edges?: any[]
  }
  createdAt: string
  updatedAt: string
  currentBotVersionId?: string
  currentBotVersion?: BotVersion
  services?: Service[]
  botVersions?: BotVersion[]
  data?: Record<string, any>
}
