export type MessageType =
  | 'text_bot'
  | 'text_contact'
  | 'text_interactive_bot'
  | 'chat_event'
  | 'select_trigger_bot'
  | 'select_condition_bot'

export type Event =
  | 'MESSAGE_RECEIVED'
  | 'WIDGET_MESSAGE_RECEIVED'
  | 'USER_MESSAGE_SENT'
  | 'MANUAL_TICKET_OPENED'
  | 'TICKET_OPENED'
  | 'TICKET_CLOSED'
  | 'TICKET_INACTIVE'
  | 'ANONYMOUS_USER_CREATED_WEBCHAT'
  | 'CONTACT_CREATED'
  | 'CONTACT_RECONNECTED'
  | 'ENTER_CONTEXT'
  | 'LEAVE_CONTEXT'
  | 'TICKET_BEFORE_CLOSE'
  | 'API_SIGNAL'
  | 'BOT_INACTIVE'

export interface BotSimulatorContact {
  id: string
  name: string
  createdAt: string
  session: {
    store: {
      context?: string
      contextPassCount?: number
    }
  } | null
}

export interface MessageFile {
  id: string
  url: string
  publicFilename: string
}

export interface MessageInteractive {
  type: 'list' | 'button'
  header?: {
    text?: string
    type: 'text' | 'document' | 'image' | 'video'
  }
  body: { text: string }
  footer?: { text: string }
  action: {
    button?: string
    sections?: {
      title: string
      rows: { id: string; title: string }[]
    }
    buttons?: { type: 'reply'; reply: { id: string; title: string } }[]
  }
}

export interface MessageData {
  interactive?: MessageInteractive
  conditionNodeId?: string
  trigger?: {
    events: Event[]
    allowAutoSend?: boolean
  }
  selectedValue?: string | boolean
  customFieldData?: {
    type: string
    name: string
    id: string
    settings: {}
  }
}

export interface BotSimulatorMessage {
  id: string
  type: MessageType
  text: string
  data: MessageData
  file?: MessageFile
  createdAt: string
}
