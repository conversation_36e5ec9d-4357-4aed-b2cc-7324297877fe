import React, { create<PERSON>ontext, Props<PERSON>ith<PERSON><PERSON><PERSON>n, useEffect } from 'react'
import Axios from 'axios'
import { useSelector } from 'react-redux'
import * as authSelectors from '../../modules/auth/selectors'
import * as impersonateSelectors from '../../modules/admin/modules/users/modules/impersonate/selectors'

const AuthContext = createContext(null)

export function AuthProvider(props: PropsWithChildren) {
  const accessToken = useSelector(authSelectors.getAccessToken)
  const impersonate = useSelector(impersonateSelectors.getIsImpersonating)

  useEffect(() => {
    Axios.defaults.headers.Authorization = `Bearer ${accessToken}`
    Axios.defaults.headers.impersonate = impersonate
  }, [accessToken, impersonate])

  return <AuthContext.Provider {...props} value={null} />
}
