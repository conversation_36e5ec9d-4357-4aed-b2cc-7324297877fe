import { PermissionsByTabEnum, type PermissionsModel, PermissionsTypeEnum } from '../api/permissions'

const tabsPermissionsMap: Record<PermissionsByTabEnum, PermissionsTypeEnum[]> = {
  [PermissionsByTabEnum.SERVICE]: [
    PermissionsTypeEnum.SCHEDULE,
    PermissionsTypeEnum.TICKETTOPICS,
    PermissionsTypeEnum.CONTACTS,
    PermissionsTypeEnum.TICKETS,
    PermissionsTypeEnum.CHAT,
    PermissionsTypeEnum.DISTRIBUTION,
    PermissionsTypeEnum.STICKERS,
    PermissionsTypeEnum.GROUPS,
    PermissionsTypeEnum.MESSAGES,
    PermissionsTypeEnum.INTERACTIVE_MESSAGES,
    PermissionsTypeEnum.DOWNLOAD,
    PermissionsTypeEnum.QUICKREPLIES,
    PermissionsTypeEnum.NOTIFICATION,
  ],
  [PermissionsByTabEnum.MARKETING]: [
    PermissionsTypeEnum.CAMPAIGNS,
    PermissionsTypeEnum.PIPELINES,
    PermissionsTypeEnum.BOTS,
    PermissionsTypeEnum.AUTOMATION,
  ],
  [PermissionsByTabEnum.AI]: [PermissionsTypeEnum.KNOWLEDGEBASE, PermissionsTypeEnum.AICONSUMPTION],
  [PermissionsByTabEnum.MANAGEMENT]: [
    PermissionsTypeEnum.AUTHHISTORY,
    PermissionsTypeEnum.EVALUATION,
    PermissionsTypeEnum.SETTINGSAPI,
    PermissionsTypeEnum.SERVICES,
    PermissionsTypeEnum.ROLES,
    PermissionsTypeEnum.CUSTOMFIELDS,
    PermissionsTypeEnum.DEPARTMENTS,
    PermissionsTypeEnum.MISCELLANEOUS,
    PermissionsTypeEnum.MYACCOUNT,
    PermissionsTypeEnum.OVERVIEW,
    PermissionsTypeEnum.HOLIDAYS,
    PermissionsTypeEnum.INTEGRATIONS,
    PermissionsTypeEnum.ORGANIZATIONS,
    PermissionsTypeEnum.PEOPLE,
    PermissionsTypeEnum.TIMETABLES,
    PermissionsTypeEnum.TAGS,
    PermissionsTypeEnum.HSM,
    PermissionsTypeEnum.ACCEPTANCETERMS,
    PermissionsTypeEnum.USERS,
  ],
}

type GroupedPermissionByType = Map<PermissionsByTabEnum, Map<PermissionsTypeEnum, PermissionsModel[]>>
const groupPermissionsByTab = (
  groupedPermissionByType: Map<PermissionsTypeEnum, PermissionsModel[]>,
): GroupedPermissionByType => {
  return Object.entries(tabsPermissionsMap).reduce<
    Map<PermissionsByTabEnum, Map<PermissionsTypeEnum, PermissionsModel[]>>
  >(
    (curr, value) => {
      const tab = value[0] as PermissionsByTabEnum
      const permissions = value[1]
      const currentMap = curr.get(tab)

      permissions.forEach((permission) => {
        currentMap.set(permission, groupedPermissionByType.get(permission))
      })

      return curr
    },
    new Map([
      [PermissionsByTabEnum.SERVICE, new Map()],
      [PermissionsByTabEnum.MARKETING, new Map()],
      [PermissionsByTabEnum.AI, new Map()],
      [PermissionsByTabEnum.MANAGEMENT, new Map()],
    ]),
  )
}

const groupPermissionsByType = (permissions: PermissionsModel[]) => {
  return permissions.reduce<Map<PermissionsTypeEnum, PermissionsModel[]>>((acc, permission) => {
    const { type } = permission

    const permissionsList = acc.get(type) || []

    permissionsList.push(permission)

    acc.set(type, permissionsList)

    return acc
  }, new Map<PermissionsTypeEnum, PermissionsModel[]>())
}

export { groupPermissionsByType, groupPermissionsByTab, type GroupedPermissionByType }
