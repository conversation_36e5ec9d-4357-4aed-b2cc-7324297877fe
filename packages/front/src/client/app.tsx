import './globals'
import React from 'react'
import { createRoot } from 'react-dom/client'
import { StoreContext } from 'redux-react-hook'
import { Provider as ReduxProvider } from 'react-redux'
import { Router } from 'react-router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import configureStore from '../app/store/configureStore'
import App from '../app/components/App'
import history from './history'
import { ConfirmationProvider } from '../app/hooks/useConfirmation2'
import { AuthProvider } from '../app/providers/AuthProvider/auth-provider'
import { NebulaI18nProvider } from '@ikatec/nebula-react'
import { apm } from '.'

const container = document.querySelector('#app')
const root = createRoot(container)
const appRehydrateState = window.__APP_REHYDRATE_STATE__
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: true,
      staleTime: 60 * 1000, // one minute,
    },
  },
})

async function renderApp() {
  const store = await configureStore(appRehydrateState, { history })

  history.listen((location) => {
    apm.startTransaction(location.pathname, 'page-load')
  })

  const app = (
    <React.StrictMode>
      <NebulaI18nProvider customI18nStorageKey="i18nextLng">
        <ReduxProvider store={store}>
          <StoreContext.Provider value={store}>
            <Router history={history}>
              <ConfirmationProvider>
                <AuthProvider>
                  <QueryClientProvider client={queryClient}>
                    <App />
                  </QueryClientProvider>
                </AuthProvider>
              </ConfirmationProvider>
            </Router>
          </StoreContext.Provider>
        </ReduxProvider>
      </NebulaI18nProvider>
    </React.StrictMode>
  )

  root.render(app)
}

renderApp()
