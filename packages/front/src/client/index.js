import React from 'react'
import * as Sentry from '@sentry/browser'
import { init as initApm } from '@elastic/apm-rum'
import config from '../../config'
import './i18n'

const sentryDsn = config('sentryDsn')

if (process.env.BUILD_FLAG_IS_DEV === 'true') {
  import('@welldone-software/why-did-you-render').then((whyDidYouRender) => {
    whyDidYouRender.default(React, {
      logOnDifferentValues: true,
    })
  })
}

Sentry.init({
  dsn: sentryDsn,
  environment: process.env.NODE_ENV,
  release: config('version'),
  enabled: !!sentryDsn,
  attachStacktrace: true,
})

Sentry.withScope(() => {
  import('./app')
})

export const apm = initApm({
  serviceVersion: config('version'),
  serverUrl: config('elasticApmServerUrl'),
  serviceName: config('elasticApmServiceName'),
  environment: config('elasticApmEnvironment'),
  active: config('elasticApmActive'),
})
