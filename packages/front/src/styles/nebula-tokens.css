:root {
  --primary-50: #f2f7fc;
  --primary-100: #e1edf8;
  --primary-200: #cadff3;
  --primary-300: #a5cbeb;
  --primary-400: #7ab0e0;
  --primary-500: #5a92d7;
  --primary-600: #4679ca;
  --primary-700: #3c66b9;
  --primary-800: #365497;
  --primary-900: #324b7d;
  --primary-950: #212e4a;
  --success-50: #e9ffe5;
  --success-100: #cdffc7;
  --success-200: #9fff95;
  --success-300: #63fe58;
  --success-400: #32f526;
  --success-500: #0edc06;
  --success-600: #04b100;
  --success-700: #068506;
  --success-800: #0b690b;
  --success-900: #0f5811;
  --success-950: #013204;
  --warning-50: #fdfbe9;
  --warning-100: #fcf9c5;
  --warning-200: #fbef8d;
  --warning-300: #f8df4c;
  --warning-400: #f3cb1c;
  --warning-500: #e3b30f;
  --warning-600: #c48b0a;
  --warning-700: #9c630c;
  --warning-800: #814f12;
  --warning-900: #6f4115;
  --warning-950: #402108;
  --danger-50: #fef2f2;
  --danger-100: #fee2e2;
  --danger-200: #fecaca;
  --danger-300: #fba6a6;
  --danger-400: #f77272;
  --danger-500: #ee4545;
  --danger-600: #db2727;
  --danger-700: #b81d1d;
  --danger-800: #981c1c;
  --danger-900: #801e1e;
  --danger-950: #450a0a;
  --neutral-white: #ffffff;
  --neutral-50: #f7f8f8;
  --neutral-100: #edeef1;
  --neutral-200: #d7dbe0;
  --neutral-300: #b4bbc5;
  --neutral-400: #8c97a4;
  --neutral-500: #6e7a89;
  --neutral-600: #586171;
  --neutral-700: #4c5461;
  --neutral-800: #3e444e;
  --neutral-900: #373c43;
  --neutral-950: #24272d;
  --neutral-1000: #1d1f24;

  /* Action Bar styles */
  --actionBar-background-divider: rgba(238, 239, 242, 0.16);

  /*Accordion */
  --accordion-background: var(--neutral-white);
  --accordion-icon: var(--neutral-950);
  --accordion-text-title: var(--neutral-950);
  --accordion-text-description: var(--neutral-600);
  --accordion-border: var(--neutral-200);

  /* Alert styles */
  --alert-success-background: var(--success-50);
  --alert-success-border: var(--success-600);
  --alert-success-text: var(--neutral-950);
  --alert-success-icon: var(--success-700);
  --alert-success-button-color: var(--primary-900);

  --alert-info-background: var(--primary-50);
  --alert-info-border: var(--primary-600);
  --alert-info-text: var(--neutral-950);
  --alert-info-icon: var(--primary-700);
  --alert-info-button-color: var(--primary-900);

  --alert-default-background: var(--neutral-50);
  --alert-default-border: var(--primary-600);
  --alert-default-text: var(--neutral-950);
  --alert-default-icon: var(--primary-700);
  --alert-default-button-color: var(--primary-900);

  --alert-warning-background: var(--warning-50);
  --alert-warning-border: var(--warning-600);
  --alert-warning-text: var(--neutral-950);
  --alert-warning-icon: var(--warning-700);
  --alert-warning-button-color: var(--primary-900);

  --alert-danger-background: var(--danger-50);
  --alert-danger-border: var(--danger-600);
  --alert-danger-text: var(--neutral-950);
  --alert-danger-icon: var(--danger-700);
  --alert-danger-button-color: var(--primary-900);

  /* Badge styles */
  --badge-primary-background: var(--primary-900);
  --badge-primary-text: var(--neutral-white);

  --badge-secondary-background: var(--neutral-200);
  --badge-secondary-text: var(--neutral-1000);

  --badge-alert-background-default: var(--danger-600);
  --badge-alert-text: var(--neutral-white);

  /* Box styles */
  --box-background: white;
  --box-secondary-background: var(--neutral-100);
  --box-border: var(--neutral-200);

  /* Breadcrumb styles */
  --breadcrumb-text-default: var(--neutral-600);
  --breadcrumb-text-hover: var(--neutral-950);
  --breadcrumb-text-active: var(--primary-700);
  --breadcrumb-ellipsis-bg-hover: var(--neutral-100);

  /* Button styles */
  --button-primary-background-default: var(--primary-900);
  --button-primary-background-hover: var(--primary-800);
  --button-primary-background-active: var(--primary-950);
  --button-primary-background-focus: var(--primary-800);
  --button-primary-border-focus: var(--primary-300);
  --button-primary-text: var(--neutral-50);

  --button-secondary-background-default: white;
  --button-secondary-background-hover: var(--neutral-50);
  --button-secondary-background-active: var(--neutral-100);
  --button-secondary-background-focus: white;
  --button-secondary-border-default: var(--neutral-300);
  --button-secondary-border-focus: var(--primary-300);
  --button-secondary-text: var(--neutral-1000);

  --button-ghost-background-hover: var(--neutral-100);
  --button-ghost-background-active: var(--neutral-200);
  --button-ghost-background-focus: var(--neutral-100);
  --button-ghost-border-focus: var(--primary-300);
  --button-ghost-text: var(--neutral-1000);

  --button-danger-background-default: var(--danger-700);
  --button-danger-background-hover: var(--danger-600);
  --button-danger-background-active: var(--danger-800);
  --button-danger-background-focus: var(--danger-600);
  --button-danger-border-focus: var(--danger-300);
  --button-danger-text: var(--neutral-50);

  --button-disabled-background: var(--neutral-200);
  --button-disabled-text: var(--neutral-500);

  /* Checkbox styles */
  --checkbox-unselected-background-default: white;
  --checkbox-unselected-background-hover: var(--neutral-50);
  --checkbox-unselected-background-focus: var(--neutral-50);
  --checkbox-unselected-background-disabled: var(--neutral-200);
  --checkbox-unselected-border-default: var(--neutral-500);
  --checkbox-unselected-border-hover: var(--neutral-500);
  --checkbox-unselected-border-focus: var(--neutral-500);
  --checkbox-unselected-border-disabled: var(--neutral-300);
  --checkbox-unselected-border-external-focus: var(--primary-300);
  --checkbox-selected-background-default: var(--primary-800);
  --checkbox-selected-background-hover: var(--primary-900);
  --checkbox-selected-background-focus: var(--primary-900);
  --checkbox-selected-background-disabled: var(--neutral-200);
  --checkbox-selected-border-focus: var(--primary-800);
  --checkbox-selected-icon-default: white;
  --checkbox-selected-icon-disabled: var(--neutral-500);

  /* Dialog styles */
  --dialog-background-default: white;
  --dialog-background-overlay: rgba(0, 0, 0, 0.5);
  --dialog-title: var(--primary-950);
  --dialog-text: var(--neutral-600);
  --dialog-icon: var(--primary-950);

  /* Drawer styles */
  --drawer-header-background: white;
  --drawer-header-border: var(--neutral-100);
  --drawer-header-title: var(--neutral-950);
  --drawer-header-description: var(--neutral-600);
  --drawer-footer-background: white;
  --drawer-footer-border: var(--neutral-100);
  --drawer-footer-label: var(--neutral-950);
  --drawer-body-background: white;
  --drawer-body-text: var(--neutral-950);
  --drawer-background-overlay: rgba(0, 0, 0, 0.5);

  /* Dropdown styles */
  --dropdown-background: white;
  --dropdown-border: var(--neutral-200);
  --dropdown-shadow: rgba(166, 184, 230, 0.32);

  /* Input styles */
  --input-background: white;
  --input-border-default: var(--neutral-300);
  --input-border-focus: var(--primary-950);
  --input-text: var(--neutral-950);
  --input-placeholder: var(--neutral-500);
  --input-helper-text-default: var(--neutral-500);
  --input-helper-text-error: var(--danger-500);

  /* Input select styles */
  /* default */
  --inputSelect-background-default: var(--neutral-white);
  --inputSelect-border-default: var(--neutral-300);
  --inputSelect-text-default: var(--neutral-500);
  --inputSelect-icon-default: var(--neutral-500);

  /* Focus */
  --inputSelect-border-focus: var(--primary-300);
  --inputSelect-text-focus: var(--neutral-950);

  /* Active */
  --inputSelect-border-active: var(--neutral-950);
  --inputSelect-text-active: var(--neutral-950);
  --inputSelect-icon-active: var(--neutral-950);

  /* Filled */
  --inputSelect-border-filled: var(--primary-300);
  --inputSelect-text-filled: var(--neutral-950);
  --inputSelect-icon-filled: var(--neutral-950);

  /* Disabled */
  --inputSelect-background-disabled: var(--neutral-100);

  /* Danger */
  --inputSelect-border-danger: var(--danger-600);
  --inputSelect-icon-danger: var(--danger-600);

  /* Input-text styles */
  --inputText-background-default: white;
  --inputText-background-disabled: var(--neutral-100);
  --inputText-border-default: var(--neutral-300);
  --inputText-border-focus: var(--primary-300);
  --inputText-border-active: var(--neutral-950);
  --inputText-border-danger: var(--danger-600);
  --inputText-border-filled: var(--neutral-950);
  --inputText-text-default: var(--neutral-500);
  --inputText-text-focus: var(--neutral-950);
  /* --inputText-text-active: var(--primary-950); */
  --inputText-text-filled: var(--neutral-950);
  --inputText-text-disabled: var(--neutral-500);
  --inputText-icon-default: var(--neutral-500);
  --inputText-icon-focus: var(--neutral-950);
  /* --inputText-icon-active: var(--primary-950); */
  --inputText-icon-filled: var(--neutral-950);
  --inputText-icon-danger: var(--danger-600);
  --inputText-icon-disabled: var(--neutral-500);

  /* Label styles */
  --label-text-default: var(--neutral-500);
  --label-text-active: var(--neutral-1000);

  /* ListItem styles */
  --listItem-background: white;
  --listItem-background-hover: var(--neutral-50);
  --listItem-border: var(--neutral-200);
  --listItem-text-header: var(--neutral-400);
  --listItem-text: var(--neutral-600);
  --listItem-text-selected: var(--neutral-950);
  --listItem-icon: var(--neutral-600);
  --listItem-icon-selected: var(--neutral-950);

  /* Pagination styles */
  --pagination-text: var(--neutral-600);
  --pagination-background-default: var(--neutral-white);
  --pagination-background-hover: var(--neutral-50);
  --pagination-background-disabled: var(--neutral-white);
  --pagination-border-default: var(--neutral-200);
  --pagination-border-disabled: var(--neutral-100);
  --pagination-icon-default: var(--neutral-600);
  --pagination-icon-disabled: var(--neutral-300);

  /* Popover styles */
  --popover-background: var(--neutral-white);
  --popover-border-color: var(--neutral-200);
  --popover-shadow-color: rgba(166, 184, 230, 0.32);

  /* Separator styles */
  --separator-border-default: var(--neutral-300);

  /* Skeleton styles */
  --skeleton-background: var(--neutral-100);

  /* Switch styles */
  --switch-background-off-default: var(--neutral-200);
  --switch-background-off-hover: var(--neutral-300);
  --switch-background-off-focus: var(--neutral-300);
  --switch-background-off-disabled: var(--neutral-200);
  --switch-background-on-default: var(--primary-800);
  --switch-background-on-hover: var(--primary-900);
  --switch-background-on-focus: var(--primary-900);
  --switch-background-on-disabled: var(--primary-800);
  --switch-ring: var(--primary-300);
  --switch-circle: white;

  /* Table styles */
  --table-background: white;
  --table-border: var(--neutral-200);
  /* --table-header-background: white; */
  /* --table-header-border: var(--neutral-200); */
  --table-header-text: var(--neutral-800);
  /* --table-header-helper: var(--neutral-300); */
  --table-cell-background-first-layer: white;
  --table-cell-background-second-layer: var(--neutral-50);
  --table-cell-border: var(--neutral-200);
  --table-cell-text: var(--neutral-950);
  --table-cell-link: var(--primary-700);
  --table-cell-icon: var(--primary-950);

  /* Tabs styles */
  --tabs-border-default: var(--neutral-200);
  --tabs-border-active: var(--primary-700);
  --tabs-text-default: var(--neutral-600);
  --tabs-text-active: var(--neutral-950);

  /* Tag styles */
  --tag-border: var(--neutral-200);
  --tag-icon: var(--neutral-400);
  --tag-text: var(--neutral-600);
  --tag-background-hover: var(--neutral-100);
  --tag-colorIndicator-yellow: #f3cb1c;
  --tag-colorIndicator-blue: #40c3ff;
  --tag-colorIndicator-red: #f77272;
  --tag-colorIndicator-green: #6ee566;
  --tag-colorIndicator-gray: #8c97a4;
  --tag-colorIndicator-orange: #ff7f29;

  /* typography */
  --typography-heading-1-size: 1.5rem;
  --typography-heading-1-weight: 600;
  --typography-heading-1-letter-spacing: 0px;
  --typography-heading-1-line-height: 1.5rem;
  --typography-heading-1-color: var(--neutral-950);

  --typography-heading-2-size: 1.25rem;
  --typography-heading-2-weight: 600;
  --typography-heading-2-letter-spacing: 0px;
  --typography-heading-2-line-height: 1.25rem;
  --typography-heading-2-color: var(--neutral-950);

  --typography-heading-3-size: 1.25rem;
  --typography-heading-3-weight: 500;
  --typography-heading-3-letter-spacing: 0px;
  --typography-heading-3-line-height: 1rem;
  --typography-heading-3-color: var(--neutral-950);

  --typography-heading-4-size: 1rem;
  --typography-heading-4-weight: 600;
  --typography-heading-4-letter-spacing: 0px;
  --typography-heading-4-line-height: 0.75rem;
  --typography-heading-4-color: var(--neutral-950);

  --typography-heading-5-size: 0.875rem;
  --typography-heading-5-weight: 600;
  --typography-heading-5-letter-spacing: 0px;
  --typography-heading-5-line-height: 0.75rem;
  --typography-heading-5-color: var(--neutral-950);

  --typography-heading-6-size: 0.75rem;
  --typography-heading-6-weight: 600;
  --typography-heading-6-letter-spacing: 0px;
  --typography-heading-6-line-height: 0.75rem;
  --typography-heading-6-color: var(--neutral-950);

  --typography-paragraph-md-size: 1rem !important;
  --typography-paragraph-md-weight: 400;
  --typography-paragraph-md-letter-spacing: 0px;
  --typography-paragraph-md-line-height: 1.5rem;
  --typography-paragraph-md-color: var(--neutral-600);

  --typography-paragraph-sm-size: 0.875rem !important;
  --typography-paragraph-sm-weight: 400;
  --typography-paragraph-sm-letter-spacing: 0px;
  --typography-paragraph-sm-line-height: 150%;
  --typography-paragraph-sm-color: var(--neutral-600);

  --typography-caption-size: 10px;
  --typography-caption-weight: 400;
  --typography-caption-letter-spacing: 0px;
  --typography-caption-line-height: 150%;
  --typography-caption-color: var(--neutral-600);

  --typography-link-md-size: var(--typography-paragraph-md-size);
  --typography-link-md-weight: var(--typography-paragraph-md-weight);
  --typography-link-md-letter-spacing: var(--typography-paragraph-md-letter-spacing);
  --typography-link-md-line-height: var(--typography-paragraph-md-line-height);
  --typography-link-md-color: var(--neutral-1000);

  --typography-link-sm-size: var(--typography-paragraph-sm-size);
  --typography-link-sm-weight: var(--typography-paragraph-sm-weight);
  --typography-link-sm-letter-spacing: var(--typography-paragraph-sm-letter-spacing);
  --typography-link-sm-line-height: var(--typography-paragraph-sm-line-height);
  --typography-link-sm-color: var(--neutral-1000);

  --typography-link-hover-color: var(--neutral-800);

  /* Toast styles */
  --toast-title: var(--neutral-1000);
  --toast-border: var(--neutral-200);
  --toast-description: var(--neutral-600);
  --toast-icon-default: var(--neutral-1000);
  --toast-icon-error: var(--danger-600);
  --toast-background: var(--neutral-white);
  --toast-shadow: rgba(3, 30, 70, 0.1);

  /* Tooltip styles */
  --tooltip-background: var(--neutral-950);
  --tooltip-text: var(--neutral-50);

  --button-radius: 9999px;
  --input-radius: 20px;
}

.dark {
  /* Action Bar styles */
  --actionBar-background-divider: rgba(238, 239, 242, 0.16);

  /*Accordion */
  --accordion-background: var(--neutral-900);
  --accordion-icon: var(--neutral-white);
  --accordion-text-title: var(--neutral-white);
  --accordion-text-description: var(--neutral-400);
  --accordion-border: var(--neutral-600);

  /* Alert styles */
  --alert-success-background: var(--success-950);
  --alert-success-border: var(--success-300);
  --alert-success-text: var(--neutral-200);
  --alert-success-icon: var(--success-300);
  --alert-success-button-color: var(--primary-200);

  --alert-info-background: var(--primary-950);
  --alert-info-border: var(--primary-300);
  --alert-info-text: var(--neutral-200);
  --alert-info-icon: var(--primary-300);
  --alert-info-button-color: var(--primary-200);

  --alert-default-background: var(--neutral-950);
  --alert-default-border: var(--primary-300);
  --alert-default-text: var(--neutral-200);
  --alert-default-icon: var(--primary-300);
  --alert-default-button-color: var(--primary-200);

  --alert-warning-background: var(--warning-950);
  --alert-warning-border: var(--warning-300);
  --alert-warning-text: var(--neutral-200);
  --alert-warning-icon: var(--warning-300);
  --alert-warning-button-color: var(--primary-200);

  --alert-danger-background: var(--danger-950);
  --alert-danger-border: var(--danger-300);
  --alert-danger-text: var(--neutral-200);
  --alert-danger-icon: var(--danger-300);
  --alert-danger-button-color: var(--primary-200);

  /* Badge styles */
  --badge-primary-background: var(--primary-200);
  --badge-primary-text: var(--neutral-950);

  --badge-secondary-background: var(--neutral-200);
  --badge-secondary-text: var(--neutral-950);

  --badge-alert-background-default: var(--danger-600);
  --badge-alert-text: var(--neutral-white);

  /* Box styles */
  --box-background: var(--neutral-950);
  --box-secondary-background: var(--neutral-950);
  --box-border: var(--neutral-800);

  /* Breadcrumb styles */
  --breadcrumb-text-default: var(--neutral-400);
  --breadcrumb-text-hover: var(--neutral-50);
  --breadcrumb-text-active: var(--primary-500);
  --breadcrumb-ellipsis-bg-hover: var(--neutral-800);

  /* Button styles */
  --button-primary-background-default: var(--primary-200);
  --button-primary-background-hover: var(--primary-300);
  --button-primary-background-active: var(--primary-400);
  --button-primary-background-focus: var(--primary-300);
  --button-primary-border-focus: var(--primary-500);
  --button-primary-text: var(--neutral-950);

  --button-secondary-background-default: var(--neutral-950);
  --button-secondary-background-hover: var(--neutral-900);
  --button-secondary-background-active: var(--neutral-800);
  --button-secondary-background-focus: var(--neutral-950);
  --button-secondary-border-default: var(--neutral-500);
  --button-secondary-border-focus: var(--primary-500);
  --button-secondary-text: var(--neutral-200);

  --button-ghost-background-hover: var(--neutral-800);
  --button-ghost-background-active: var(--neutral-900);
  --button-ghost-background-focus: var(--neutral-800);
  --button-ghost-border-focus: var(--primary-500);
  --button-ghost-text: var(--neutral-200);

  --button-danger-background-default: var(--danger-300);
  --button-danger-background-hover: var(--danger-400);
  --button-danger-background-active: var(--danger-500);
  --button-danger-background-focus: var(--danger-400);
  --button-danger-border-focus: var(--danger-300);
  --button-danger-text: var(--neutral-950);

  --button-disabled-background: var(--neutral-800);
  --button-disabled-text: var(--neutral-400);

  /* Checkbox styles */
  --checkbox-unselected-background-default: var(--neutral-900);
  --checkbox-unselected-background-hover: var(--neutral-800);
  --checkbox-unselected-background-focus: var(--neutral-800);
  --checkbox-unselected-background-disabled: var(--neutral-500);
  --checkbox-unselected-border-default: var(--neutral-800);
  --checkbox-unselected-border-hover: var(--neutral-500);
  --checkbox-unselected-border-focus: var(--neutral-500);
  --checkbox-unselected-border-external-focus: var(--primary-300);
  --checkbox-unselected-border-disabled: var(--neutral-700);
  --checkbox-selected-background-default: var(--primary-100);
  --checkbox-selected-background-hover: var(--primary-200);
  --checkbox-selected-background-focus: var(--primary-200);
  --checkbox-selected-background-disabled: var(--neutral-500);
  --checkbox-selected-border-focus: var(--primary-100);
  --checkbox-selected-icon-default: var(--neutral-950);
  --checkbox-selected-icon-disabled: var(--neutral-800);

  /* Dialog styles */
  --dialog-background-default: var(--neutral-950);
  --dialog-background-overlay: rgba(0, 0, 0, 0.5);
  --dialog-title: var(--neutral-200);
  --dialog-text: var(--neutral-400);
  --dialog-icon: var(--neutral-200);

  /* Drawer styles */
  --drawer-header-background: var(--neutral-900);
  --drawer-header-border: var(--neutral-800);
  --drawer-header-title: var(--neutral-200);
  --drawer-header-description: var(--neutral-400);
  --drawer-footer-background: var(--neutral-900);
  --drawer-footer-border: var(--neutral-800);
  --drawer-footer-label: var(--neutral-200);
  --drawer-body-background: var(--neutral-900);
  --drawer-body-text: var(--neutral-200);
  --drawer-background-overlay: rgba(0, 0, 0, 0.5);

  /* Dropdown styles */
  --dropdown-background: var(--neutral-900);
  --dropdown-border: var(--neutral-800);
  --dropdown-shadow: rgba(0, 0, 0, 0.5);

  /* Input styles */
  --input-background: var(--neutral-900);
  --input-border-default: var(--neutral-500);
  --input-border-focus: var(--primary-300);
  --input-text: var(--neutral-200);
  --input-placeholder: var(--neutral-400);
  --input-helper-text-default: var(--neutral-400);
  --input-helper-text-error: var(--danger-300);

  /* Input select styles */
  /* Default */
  --inputSelect-background-default: var(--neutral-900);
  --inputSelect-border-default: var(--neutral-500);
  --inputSelect-text-default: var(--neutral-500);
  --inputSelect-icon-default: var(--neutral-500);

  /* Focus */
  --inputSelect-border-focus: var(--primary-400);
  --inputSelect-text-focus: var(--neutral-200);

  /* Active */
  --inputSelect-border-active: var(--primary-200);
  --inputSelect-text-active: var(--neutral-200);
  --inputSelect-icon-active: var(--neutral-200);

  /* Filled */
  --inputSelect-border-filled: var(--primary-500);
  --inputSelect-text-filled: var(--neutral-200);
  --inputSelect-icon-filled: var(--neutral-200);

  /* Disabled */
  --inputSelect-background-disabled: var(--primary-500);

  /* Danger */
  --inputSelect-border-danger: var(--danger-300);
  --inputSelect-icon-danger: var(--danger-300);

  /* Input-text styles */
  --inputText-background-default: var(--neutral-900);
  --inputText-background-disabled: var(--neutral-700);
  --inputText-border-default: var(--neutral-500);
  --inputText-border-focus: var(--primary-300);
  --inputText-border-active: var(--neutral-200);
  --inputText-border-danger: var(--danger-300);
  --inputText-border-filled: var(--neutral-200);
  --inputText-text-default: var(--neutral-500);
  --inputText-text-focus: var(--neutral-200);
  /* --inputText-text-active: var(--primary-950); */
  --inputText-text-filled: var(--neutral-200);
  --inputText-text-disabled: var(--neutral-400);
  --inputText-icon-default: var(--neutral-500);
  --inputText-icon-focus: var(--primary-950);
  /* --inputText-icon-active: var(--primary-950); */
  --inputText-icon-filled: var(--neutral-200);
  --inputText-icon-danger: var(--danger-300);
  --inputText-icon-disabled: var(--neutral-400);

  /* Label styles */
  --label-text-default: var(--neutral-400);
  --label-text-active: var(--neutral-200);

  /* ListItem styles */
  --listItem-background: var(--neutral-900);
  --listItem-background-hover: var(--neutral-800);
  --listItem-border: var(--neutral-800);
  --listItem-text-header: var(--neutral-500);
  --listItem-text: var(--neutral-300);
  --listItem-text-selected: var(--neutral-200);
  --listItem-icon: var(--neutral-300);
  --listItem-icon-selected: var(--neutral-200);

  /* Pagination styles */
  --pagination-text: var(--neutral-600);
  --pagination-background-hover: var(--neutral-800);
  --pagination-background-default: var(--neutral-950);
  --pagination-border-default: var(--neutral-800);
  --pagination-icon-default: var(--neutral-500);
  --pagination-icon-disabled: var(--neutral-800);
  --pagination-background-disabled: var(--neutral-950);
  --pagination-border-disabled: var(--neutral-900);

  /* Popover styles */
  --popover-background: var(--neutral-900);
  --popover-border-color: var(--neutral-800);
  --popover-shadow-color: rgba(0, 0, 0, 0.5);

  /* Separator styles */
  --separator-border-default: var(--neutral-800);

  /* Skeleton styles */
  --skeleton-background: var(--neutral-900);

  /* Switch styles */
  --switch-background-off-default: var(--neutral-800);
  --switch-background-off-hover: var(--neutral-700);
  --switch-background-off-focus: var(--neutral-700);
  --switch-background-off-disabled: var(--neutral-800);
  --switch-background-on-default: var(--primary-500);
  --switch-background-on-hover: var(--primary-400);
  --switch-background-on-focus: var(--primary-400);
  --switch-background-on-disabled: var(--primary-500);
  --switch-ring: var(--primary-100);
  --switch-circle: white;

  /* Table styles */
  --table-background: var(--neutral-900);
  --table-border: var(--neutral-800);
  /* --table-header-background: white; */
  /* --table-header-border: var(--neutral-200); */
  --table-header-text: var(--neutral-400);
  /* --table-header-helper: var(--neutral-300); */
  --table-cell-background-first-layer: var(--neutral-950);
  --table-cell-background-second-layer: var(--neutral-1000);
  --table-cell-border: var(--neutral-800);
  --table-cell-text: var(--neutral-200);
  --table-cell-link: var(--primary-300);
  --table-cell-icon: var(--primary-500);

  /* Tabs styles */
  --tabs-border-default: var(--neutral-800);
  --tabs-border-active: var(--primary-200);
  --tabs-text-default: var(--neutral-500);
  --tabs-text-active: var(--neutral-200);

  /* Tag styles */
  --tag-border: var(--neutral-800);
  --tag-icon: var(--neutral-500);
  --tag-text: var(--neutral-200);
  --tag-background-hover: var(--neutral-800);
  --tag-colorIndicator-yellow: #f3cb1c;
  --tag-colorIndicator-blue: #40c3ff;
  --tag-colorIndicator-red: #f77272;
  --tag-colorIndicator-green: #6ee566;
  --tag-colorIndicator-gray: #8c97a4;
  --tag-colorIndicator-orange: #ff7f29;

  /* Toast styles */
  --toast-title: var(--neutral-100);
  --toast-border: var(--neutral-800);
  --toast-description: var(--neutral-100);
  --toast-icon-default: var(--neutral-100);
  --toast-icon-error: var(--danger-600);
  --toast-background: var(--neutral-900);
  --toast-shadow: rgba(3, 30, 70, 0.1);

  /* typography */
  --typography-caption-color: var(--neutral-400);
  --typography-heading-1-color: var(--neutral-300);
  --typography-heading-2-color: var(--neutral-300);
  --typography-heading-3-color: var(--neutral-300);
  --typography-heading-4-color: var(--neutral-300);
  --typography-heading-5-color: var(--neutral-300);
  --typography-heading-6-color: var(--neutral-300);
  --typography-paragraph-md-color: var(--neutral-400);
  --typography-paragraph-sm-color: var(--neutral-400);
  --typography-link-md-color: var(--neutral-200);
  --typography-link-sm-color: var(--neutral-200);
  --typography-link-hover-color: var(--neutral-400);

  /* Tooltip styles */
  --tooltip-background: var(--neutral-700);
  --tooltip-text: var(--neutral-50);
}
