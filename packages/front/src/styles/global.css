.tailwind,
.nebula-ds {
  @import url('https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300..900;1,300..900&family=Lexend:wght@100..900&display=swap');

  @tailwind base;
  @tailwind components;
  @tailwind utilities;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    /* font-family: 'Lexend', sans-serif !important; */
  }

  * {
    /* font-family: 'Figtree', sans-serif !important; */
  }

  label {
    margin: 0 !important;
  }
}

html {
  font-size: 16px !important;
}

body {
  font-family: 'Inter', sans-serif !important;
  font-optical-sizing: auto !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-size: 0.875rem;
}

body.dark #dashboard {
  background-color: #1d1f24 !important;
}

/* Chrome, Edge and Safari */
*::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

*::-webkit-scrollbar-track {
  border-radius: 0px;
  background-color: #eeeeee;
}

*::-webkit-scrollbar-track:hover {
  background-color: #eeeeee;
}

*::-webkit-scrollbar-track:active {
  background-color: #eeeeee;
}

*::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #858585;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #939393;
}

*::-webkit-scrollbar-thumb:active {
  background-color: #939393;
}
