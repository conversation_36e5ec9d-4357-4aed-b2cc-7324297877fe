import React from 'react'
import { ResourceSelect, ResourceSelectProps } from './resource-select'
import timetableApi, { TimetableData, timetableQueryKey } from '../../api/timetable'

type TimetableSelectProps = ResourceSelectProps<TimetableData>

function TimetableSelect({
  isPaged = true,
  ...rest
}: Omit<TimetableSelectProps, 'queryKey' | 'fetchFn' | 'getOptionLabel' | 'getOptionValue'>) {
  return (
    <ResourceSelect<TimetableData>
      {...rest}
      queryKey={[timetableQueryKey, 'list']}
      fetchFn={(params) => timetableApi.getAll({ ...params })}
      getOptionLabel={(item) => item.name}
      getOptionValue={(item) => item.id}
      isPaged={isPaged}
      value={rest.value as any}
      isMulti={rest.isMulti as any}
    />
  )
}

export { TimetableSelect }
