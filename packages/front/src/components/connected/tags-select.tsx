import React from 'react'
import tagsApi, { tagsQueryKey } from '../../api/tags'
import { ResourceSelect, ResourceSelectProps } from './resource-select'
import { Tag } from '../../app/types/Tag'

type TagsSelectProps = ResourceSelectProps<Tag>

function TagsSelect({
  isPaged = true,
  ...rest
}: Omit<TagsSelectProps, 'queryKey' | 'fetchFn' | 'getOptionLabel' | 'getOptionValue'>) {
  return (
    <ResourceSelect<Tag>
      queryKey={[tagsQueryKey, 'list']}
      fetchFn={tagsApi.getAll}
      getOptionLabel={(item) => item.label}
      getOptionValue={(item) => item.id}
      isPaged={isPaged}
      {...rest}
      value={rest.value as any}
      isMulti={rest.isMulti as any}
    />
  )
}

export { TagsSelect }
