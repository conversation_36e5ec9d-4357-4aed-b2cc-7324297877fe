import { SelectProps as NebulaSelectProps, Select } from '@ikatec/nebula-react'
import { useQuery } from '@tanstack/react-query'
import React, { useCallback, useEffect, useMemo, useState } from 'react'

import { PaginatedApiResponse } from '../../api/types'

type ResourceResponse<T> = PaginatedApiResponse<T> | T[]

interface Option {
  value: string | number
  label: string
}

interface SelectProps extends Omit<NebulaSelectProps, 'options' | 'isLoading' | 'onMenuScrollToBottom'> {
  extraOptions?: Option[]
  isPaged?: boolean
}

type ResourceSelectValue = { value: string | number; label?: string }

type BaseResourceSelectProps<T> = {
  queryKey: string[]
  fetchFn: (params: { page?: number; paginate?: boolean; name?: string }) => Promise<ResourceResponse<T>>
  getOptionLabel: (item: T) => string
  getOptionValue: (item: T) => string
} & Omit<SelectProps, 'value'>

type ResourceSingleSelectProps = {
  isMulti?: false
  value: ResourceSelectValue | null
}

type ResourceMultiSelectProps = {
  isMulti: true
  value: ResourceSelectValue[] | null
}

type ResourceSelectProps<T> = BaseResourceSelectProps<T> & (ResourceSingleSelectProps | ResourceMultiSelectProps)

function ResourceSelect<T>({
  isPaged = true,
  queryKey,
  fetchFn,
  getOptionLabel,
  getOptionValue,
  extraOptions = [],
  value,
  ...rest
}: ResourceSelectProps<T>) {
  const [page, setPage] = useState(1)
  const [search, setSearch] = useState('')
  const [options, setOptions] = useState<Option[]>([])

  const { data, isFetching, isSuccess } = useQuery({
    queryKey: [...queryKey, isPaged, page, search],
    queryFn: () => fetchFn({ page: isPaged ? page : undefined, paginate: isPaged, name: search }),
  })

  const onMenuScrollToBottom = useCallback(() => {
    if (isPaged && !isFetching && data && 'lastPage' in data && page < data.lastPage) {
      setPage((prev) => prev + 1)
    }
  }, [isPaged, isFetching, page, data])

  const normalizedData = useMemo(() => {
    if (!isSuccess) return []

    return Array.isArray(data) ? data : data.data
  }, [data])

  useEffect(() => {
    if (!isSuccess || !normalizedData) return

    const newOptions = normalizedData.map((item) => ({
      value: getOptionValue(item),
      label: getOptionLabel(item),
    }))

    if (isPaged) {
      setOptions((prev) => {
        const uniqueOptions = [...prev]
        newOptions.forEach((newOption) => {
          if (!uniqueOptions.some((existing) => existing.value === newOption.value)) {
            uniqueOptions.push(newOption)
          }
        })
        return uniqueOptions
      })
      return
    }

    setOptions(newOptions)
  }, [isSuccess, getOptionLabel, getOptionValue, isPaged, normalizedData])

  const normalizedValueWithLabel = useMemo<Option | Option[] | null>(() => {
    const getValueInOptions = (item: any) => normalizedData.find((option) => getOptionValue(option as T) === item.value)

    if (Array.isArray(value)) {
      return value.map((item) => {
        const selectedOption = getValueInOptions(item) ?? item
        return {
          label: getOptionLabel(selectedOption) || item.label,
          value: getOptionValue(selectedOption) || item.value,
        }
      })
    }

    if (!isSuccess || !value?.value) return null

    const selectedOption = getValueInOptions(value)

    if (value.value && value.label) return value as Option

    if (selectedOption) {
      return {
        label: getOptionLabel(selectedOption),
        value: getOptionValue(selectedOption),
      }
    }

    return null
  }, [value, normalizedData, isSuccess, getOptionLabel, getOptionValue])

  const onInputChange = useCallback((newValue: string) => {
    setSearch(newValue)
    setPage(1)
  }, [])

  return (
    <Select
      onMenuScrollToBottom={onMenuScrollToBottom}
      options={[...extraOptions, ...options]}
      isLoading={isFetching}
      value={normalizedValueWithLabel}
      onInputChange={onInputChange}
      {...rest}
    />
  )
}

export { ResourceSelect, type SelectProps, type ResourceSelectProps, type ResourceSelectValue }
