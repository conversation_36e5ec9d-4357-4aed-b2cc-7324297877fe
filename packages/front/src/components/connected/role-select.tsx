import React from 'react'

import rolesA<PERSON>, { rolesQ<PERSON>y<PERSON>ey, type RolesModel } from '../../api/roles'
import { ResourceSelect, ResourceSelectProps } from './resource-select'

type RolesSelectProps = ResourceSelectProps<RolesModel>

function RolesSelect({
  isPaged = true,
  ...rest
}: Omit<RolesSelectProps, 'queryKey' | 'fetchFn' | 'getOptionLabel' | 'getOptionValue'>) {
  return (
    <ResourceSelect<RolesModel>
      queryKey={[rolesQueryKey, 'list']}
      fetchFn={(params) => rolesApi.getAll({ ...params })}
      getOptionLabel={(item) => item.displayName}
      getOptionValue={(item) => item.id}
      isPaged={isPaged}
      {...rest}
      value={rest.value as any}
      isMulti={rest.isMulti as any}
    />
  )
}

export { RolesSelect }
