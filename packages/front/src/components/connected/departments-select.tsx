import React from 'react'
import departmentsApi, { departmentsQueryKey } from '../../api/departments'
import { ResourceSelect, type ResourceSelectProps } from './resource-select'
import { Department } from '../../app/types/Department'

type DepartmentsSelectProps = ResourceSelectProps<Department> & {
  hideArchived?: boolean
}

function DepartmentsSelect({
  isPaged = true,
  hideArchived = false,
  ...rest
}: Omit<DepartmentsSelectProps, 'queryKey' | 'fetchFn' | 'getOptionLabel' | 'getOptionValue'>) {
  return (
    <ResourceSelect<Department>
      {...rest}
      queryKey={[departmentsQueryKey, 'list']}
      fetchFn={(params) =>
        departmentsApi.getAll({
          ...params,
          archivedAt: hideArchived ? 'unarchived' : undefined,
          name: params.name,
        })
      }
      getOptionLabel={(item) => item.name}
      getOptionValue={(item) => item.id}
      isPaged={isPaged}
      value={rest.value as any}
      isMulti={rest.isMulti as any}
    />
  )
}

export { DepartmentsSelect }
