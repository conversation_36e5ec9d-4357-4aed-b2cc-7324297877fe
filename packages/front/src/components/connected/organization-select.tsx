import React from 'react'

import organizationsApi, { OrganizationsModel, organizationsQueryKey } from '../../api/organizations'
import { ResourceSelect, ResourceSelectProps } from './resource-select'

type OrganizationSelectProps = ResourceSelectProps<OrganizationsModel>

function OrganizationSelect({
  isPaged = true,
  ...rest
}: Omit<OrganizationSelectProps, 'queryKey' | 'fetchFn' | 'getOptionLabel' | 'getOptionValue'>) {
  return (
    <ResourceSelect<OrganizationsModel>
      queryKey={[organizationsQueryKey, 'list']}
      fetchFn={(params) => organizationsApi.getAll({ ...params })}
      getOptionLabel={(item) => item.name}
      getOptionValue={(item) => item.id}
      isPaged={isPaged}
      {...rest}
      value={rest.value as any}
      isMulti={rest.isMulti as any}
    />
  )
}

export { OrganizationSelect }
