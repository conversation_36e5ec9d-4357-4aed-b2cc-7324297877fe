import React from 'react'
import { InputPhone, InputText } from '@ikatec/nebula-react'

export const RenderInputPhone = (props) => {
  const { value } = props

  const cleaned = String(value ?? '')
    .replace(/\s+/g, '')
    .replace(/[-()]/g, '')
  const isValidPhone = /^\+?\d+$/.test(cleaned)

  if (value && !isValidPhone) {
    return <InputText type="text" {...props} />
  }

  return <InputPhone {...props} />
}
