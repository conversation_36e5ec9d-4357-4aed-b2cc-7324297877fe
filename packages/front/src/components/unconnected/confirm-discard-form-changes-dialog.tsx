import {
  AlertDialog,
  AlertDialog<PERSON>ontent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogAction,
  AlertDialogCancel,
} from '@ikatec/nebula-react'
import React from 'react'
import { useTranslation } from 'react-i18next'

interface ConfirmDiscardFormChangesDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onContinue: VoidFunction
}

const ConfirmDiscardFormChangesDialog = (props: ConfirmDiscardFormChangesDialogProps) => {
  const { t } = useTranslation(['common'])
  return (
    <AlertDialog {...props}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('common:BUTTON_DISCARD_CHANGES')}</AlertDialogTitle>
          <AlertDialogDescription>{t('common:HAS_PENDENT_CHANGES')}</AlertDialogDescription>
          <AlertDialogDescription>{t('common:ARE_YOU_SURE_TO_CONTINUE')}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="w-full" variant="secondary">
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>
          <AlertDialogAction className="w-full" onClick={props.onContinue}>
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { ConfirmDiscardFormChangesDialog }
