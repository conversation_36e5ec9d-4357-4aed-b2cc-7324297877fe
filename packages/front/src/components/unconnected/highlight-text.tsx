import React, { useMemo } from 'react'
import { cn } from '../../app/components/common/unconnected/ui/utils'

interface HighlightTextProps extends Omit<React.HTMLAttributes<HTMLSpanElement>, 'children'> {
  highlightText: string
  text: string
  caseSensitive?: boolean
}

function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

const HighlightText = React.memo(function HighlightText({
  highlightText,
  text,
  caseSensitive = false,
  className,
  ...rest
}: HighlightTextProps) {
  const trimHighlighText = useMemo(() => highlightText.trim(), [highlightText])
  const parts = useMemo(() => {
    if (!trimHighlighText || !trimHighlighText?.length) return [text]

    const cleanedText = text.trim()
    const escaped = escapeRegExp(trimHighlighText.trimStart())
    const flags = caseSensitive ? 'g' : 'gi'
    const regex = new RegExp(escaped, flags)

    const result: (string | JSX.Element)[] = []
    let lastIndex = 0

    for (const match of cleanedText.matchAll(regex)) {
      const index = match.index
      const matchedText = match[0]

      if (lastIndex < index) {
        result.push(cleanedText.slice(lastIndex, index))
      }

      const leadingSpace = matchedText.startsWith(' ') ? ' ' : ''
      const trailingSpace = matchedText.endsWith(' ') ? ' ' : ''
      const cleanText = matchedText.trim()

      result.push(
        <React.Fragment key={index}>
          {leadingSpace}
          <mark className="inline-flex p-0 m-0 text-inherit bg-primary-200 dark:bg-primary-800">{cleanText}</mark>
          {trailingSpace}
        </React.Fragment>,
      )

      lastIndex = index + matchedText.length
    }

    if (lastIndex < text.length) {
      result.push(cleanedText.slice(lastIndex))
    }

    return result
  }, [text, trimHighlighText, caseSensitive])

  return (
    <span {...rest} className={cn('text-inherit', className)}>
      {parts}
    </span>
  )
})

export { HighlightText }
