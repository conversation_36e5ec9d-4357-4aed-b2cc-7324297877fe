import React from 'react'
import { Label } from '@ikatec/nebula-react'
import { Skeleton } from '../../../app/components/common/unconnected/ui/skeleton'

interface InputSkeletonProps {
  label: string
}

const InputSkeleton = ({ label }: InputSkeletonProps) => {
  return (
    <div className="w-full">
      <Label htmlFor="name">{label}</Label>
      <Skeleton width="100%" height="40px" className="rounded-full" />
    </div>
  )
}

export { InputSkeleton }
