import React, { PropsWithChildren } from 'react'
import { Space } from '@ikatec/nebula-react'
import { Skeleton } from '../../../app/components/common/unconnected/ui/skeleton'
import { FormSkeleton } from './form-skeleton'
import { Container, ContainerContent, ContainerHeader } from '../../../layouts/container'
import { ButtonSkeleton } from './button-skeleton'

const FormPageSkeleton = ({ children }: PropsWithChildren) => {
  return (
    <Container>
      <ContainerHeader mtSize="sm">
        <Space className="w-full items-center" size="sm">
          <Skeleton className="h-5 w-20" />
          <Skeleton className="h-5 w-20" />
          <Skeleton className="h-5 w-20" />
        </Space>

        <Space className="w-full items-center" size="sm">
          <ButtonSkeleton icon size="md" />
          <Skeleton className="h-9 w-96" />
        </Space>
      </ContainerHeader>
      <ContainerContent>
        <Space className="gap-6">
          <Space direction="column" className="gap-6 flex-1">
            {children}
            <FormSkeleton />
          </Space>

          <Skeleton className="w-[340px] h-52" />
        </Space>
      </ContainerContent>
    </Container>
  )
}

export { FormPageSkeleton }
