import React from 'react'
import { type ButtonProps } from '@ikatec/nebula-react'
import { Skeleton } from '../../../app/components/common/unconnected/ui/skeleton'
import { cn } from '../../../app/components/common/unconnected/ui/utils'

interface ButtonSkeletonProps extends Pick<ButtonProps, 'size' | 'icon' | 'className'> {}

const ButtonSkeleton = ({ size = 'md', icon = false, className }: ButtonSkeletonProps) => {
  return (
    <Skeleton
      className={cn(
        'rounded-full w-40',
        {
          'h-6': size === 'xs',
          'h-8': size === 'sm',
          'h-10': size === 'md',
          'h-12': size === 'lg',
          'w-6': icon && size === 'xs',
          'w-8': icon && size === 'sm',
          'w-10': icon && size === 'md',
          'w-12': icon && size === 'lg',
        },
        className,
      )}
    />
  )
}

export { ButtonSkeleton }
