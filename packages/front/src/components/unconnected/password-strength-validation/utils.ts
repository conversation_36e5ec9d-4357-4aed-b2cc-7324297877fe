import { containKnownPatternsInZxcvbnResult } from '../../../app/utils/validator/validators'
import zxcvbnHelper from '../../../app/utils/zxcvbnHelper'

enum PasswordValidationRules {
  AT_LEAST_LENGTH = 'AT_LEAST_LENGTH',
  AT_LEAST_UPPERCASE = 'AT_LEAST_UPPERCASE',
  AT_LEAST_LOWERCASE = 'AT_LEAST_LOWERCASE',
  AT_LEAST_DIGITS = 'AT_LEAST_DIGITS',
  AT_LEAST_SPECIAL = 'AT_LEAST_SPECIAL',
  NOT_CONTAIN_KNOWN_PATTERNS = 'NOT_CONTAIN_KNOWN_PATTERNS',
}

interface PasswordValidation {
  message: [messageKey: string, params: Record<string, string | number>]
  validate: (value: string, passwordStrength?: { score: number }) => boolean
}

const MIN_PASSWORD_LENGTH = 8
const passwordValidators: Record<PasswordValidationRules, PasswordValidation> = {
  AT_LEAST_LENGTH: {
    message: ['AT_LEAST_LENGTH', { minLength: MIN_PASSWORD_LENGTH }],
    validate: (value: string) => value?.length >= MIN_PASSWORD_LENGTH,
  },
  AT_LEAST_UPPERCASE: {
    message: ['AT_LEAST_UPPERCASE', { minLength: 1 }],
    validate: (value: string) => new RegExp(/[A-Z]/).test(value),
  },
  AT_LEAST_LOWERCASE: {
    message: ['AT_LEAST_LOWERCASE', { minLength: 1 }],
    validate: (value: string) => new RegExp(/[a-z]/).test(value),
  },
  AT_LEAST_DIGITS: {
    message: ['AT_LEAST_DIGITS', { minLength: 1 }],
    validate: (value: string) => new RegExp(/\d/).test(value),
  },
  AT_LEAST_SPECIAL: {
    message: ['AT_LEAST_SPECIAL', { minLength: 1 }],
    validate: (value: string) => new RegExp(/[-#!$@£%^&*()_+|~=`{}[\]:";'<>?,./ ]/).test(value),
  },
  NOT_CONTAIN_KNOWN_PATTERNS: {
    message: ['NOT_CONTAIN_KNOWN_PATTERNS', {}],
    validate: (value: string, passwordStrength?: { score: number }) =>
      !containKnownPatternsInZxcvbnResult(zxcvbnHelper.getPasswordPatterns(), passwordStrength)({ value }) && !!value,
  },
}

const passwordIsStrong = (password: string) =>
  Object.values(passwordValidators).reduce((acc, { validate }) => {
    if (!acc) return false

    return validate(password)
  }, true)

export { passwordValidators, passwordIsStrong }
