import React from 'react'
import { Skeleton } from '../../../app/components/common/unconnected/ui/skeleton'

interface CardSkeletonProps {
  rows?: number
}

const CardSkeleton = ({ rows = 6 }: CardSkeletonProps) => {
  return (
    <>
      {Array.from({ length: rows }).map(() => (
        <div className="flex-col gap-3">
          <Skeleton height={'136px'} width={'100%'} />
        </div>
      ))}
    </>
  )
}

export { CardSkeleton }
