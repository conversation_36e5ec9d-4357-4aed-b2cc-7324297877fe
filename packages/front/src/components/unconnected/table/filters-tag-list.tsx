import { Button, Separator, Space, Tag } from '@ikatec/nebula-react'
import React from 'react'
import { useTranslation } from 'react-i18next'

type FiltersTableTags = {
  name: string
  label: string
  filter: string
}[]

interface FiltersTagListProps {
  onClear: VoidFunction
  filters?: FiltersTableTags
  onDeleteFilter: (filterName: string) => void
}

const FiltersTagList = ({ onClear, filters, onDeleteFilter }: FiltersTagListProps) => {
  const { t } = useTranslation(['common'])

  if (!filters?.length) return null

  return (
    <Space className="items-center mb-4">
      <span className="text-neutral-1000 dark:text-neutral-600">{t('FILTERED_BY')}:</span>
      {filters.map((filter) => (
        <Tag
          key={filter.name}
          onDelete={() => {
            onDeleteFilter(filter.filter)
            onDeleteFilter(filter.filter + '-label')
          }}
        >
          {`${filter.name}: ${filter.label}`}
        </Tag>
      ))}
      <Separator orientation="vertical" className="h-4" />
      <Button onClick={onClear} size="xs" variant="ghost">
        {t('REMOVE_FILTERS_LABEL')}
      </Button>
    </Space>
  )
}

export { FiltersTagList, type FiltersTableTags }
