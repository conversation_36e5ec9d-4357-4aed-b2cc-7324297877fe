import React, { useEffect, useState } from 'react'
import { InputText, InputTextProps } from '@ikatec/nebula-react'
import { SearchIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface InputFilterProps extends Omit<InputTextProps, 'name' | 'value' | 'placeholder'> {
  name: string
}

const InputFilter = ({ onClean, onChange, defaultValue = '', ...props }: InputFilterProps) => {
  const [value, setValue] = useState(defaultValue)
  const { t } = useTranslation(['common'])

  const handleClean = () => {
    setValue('')
    onClean?.()
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value)
    onChange?.(e)
  }

  useEffect(() => {
    setValue(defaultValue)
  }, [defaultValue])

  return (
    <InputText
      className="w-[372px]"
      icon={<SearchIcon />}
      onClean={handleClean}
      onChange={handleChange}
      value={value}
      defaultValue={defaultValue}
      {...props}
      placeholder={t('common:SEARCH_BY_NAME')}
    />
  )
}

export { InputFilter }
