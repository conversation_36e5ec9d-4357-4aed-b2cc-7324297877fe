import React from 'react'
import { <PERSON>, But<PERSON> } from '@ikatec/nebula-react'
import { WandSparkles } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { ResultFeedback } from '../result-feedback'

interface NoResultsFoundByServiceTypeProps {
  navigateToList: VoidFunction
}

const NoResultsFoundByServiceType = ({ navigateToList }: NoResultsFoundByServiceTypeProps) => {
  const { t } = useTranslation(['common'])
  return (
    <Box variant="primary" border paddingSize="xl">
      <ResultFeedback
        icon={<WandSparkles />}
        title={t('LIST_NO_RESULTS_FOUND_WITH_FILTER')}
        description={t('NO_CREDIT_MOVEMENT_DESCRIPTION')}
        actions={
          <Button type="button" variant="secondary" size="sm" onClick={navigateToList}>
            {t('FORM_ACTION_BACK')}
          </Button>
        }
      />
    </Box>
  )
}

export { NoResultsFoundByServiceType }
