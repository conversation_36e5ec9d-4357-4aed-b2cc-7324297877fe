import React from 'react'
import { Skeleton } from '../../../app/components/common/unconnected/ui/skeleton'

interface TableSkeletonProps {
  rows?: number
}

const TableSkeleton = ({ rows = 10 }: TableSkeletonProps) => {
  return (
    <div className="grid gap-2">
      <Skeleton height={'40px'} width={'100%'} />
      {Array.from({ length: rows }).map(() => (
        <Skeleton height={'48px'} width={'100%'} />
      ))}
    </div>
  )
}

export { TableSkeleton }
