import { useCallback, useMemo } from 'react'
import { useHistory, useLocation } from 'react-router'

/**
 * Hook to manage search params.
 * @example
 * const { params, setValues, set, remove, clear } = useSearchParams<{ page: number, pageSize: number }>()
 */
function useSearchParams<T = any>(defaultValues?: Partial<T>, countbleFilters?: (keyof T)[]) {
  type TKeys = keyof T

  const history = useHistory()
  const { search } = useLocation()

  const filtersParams = useMemo(() => new URLSearchParams(search), [search])
  const memorizedDefaultValues = useMemo(() => defaultValues, [])

  /**
   * Serialize search params.
   */
  const serializedFilters = useMemo<T>(() => {
    return { ...memorizedDefaultValues, ...Object.fromEntries(filtersParams) } as T
  }, [filtersParams])

  /**
   * Total of applyed filters
   */
  const totalFilters = useMemo(() => {
    return Object.keys(serializedFilters).filter((k: any) => (countbleFilters ? countbleFilters.includes(k) : true))
      .length
  }, [serializedFilters])

  const handleSetValues = useCallback((params: Partial<T>) => {
    const newParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (!value) {
        newParams.delete(key)
      } else {
        newParams.set(key, String(value))
      }
    })

    history.replace({ search: newParams.toString() })
  }, [])

  const handleSet = (key: TKeys, value: string) => {
    const newParams = filtersParams

    const innerKey = key as string

    if (newParams.has(innerKey)) newParams.delete(key as string)

    newParams.append(innerKey, value)
    history.replace({ search: newParams.toString() })
  }

  const handleRemove = (keys: TKeys[]) => {
    const newParams = filtersParams

    keys.forEach((k) => {
      newParams.delete(k as string)
      newParams.delete((k as string) + '-label')
    })

    history.replace({ search: newParams.toString() })
  }

  const handleClear = useCallback(() => {
    history.replace({ search: '' })
  }, [])

  const hasFilter = useMemo(() => {
    return filtersParams.size > 0
  }, [filtersParams])

  return {
    totalFilters,
    /**
     * URLSearchParams instance.
     */
    searchParams: filtersParams.toString(),
    /**
     * Indicates whether there are any filters applied.
     */
    hasFilter,
    /**
     * Serialized search params.
     * @returns An object with the serialized search params with values parsed to `string` typed as `T`.
     */
    params: serializedFilters,
    /**
     * Use this method to set multiple search params.
     * @example
     * setValues({ page: 2, pageSize: 10 })
     */
    setValues: handleSetValues,
    /**
     * Use this method to set a single search param.
     * @example
     * set('page', '2')
     */
    set: handleSet,
    /**
     * Use this method to remove the search params.
     * @example
     * remove('page')
     */
    remove: handleRemove,
    /**
     * Use this method to clear the search params.
     * @example
     * clear()
     */
    clear: handleClear,
  }
}

export { useSearchParams }
