import React from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { CurrentListBots } from './components/current-list-bots'
import { LegacyListBots } from './components/legacy-list-bots'
import { useSelector } from 'react-redux'
import { selectors as userSelectors } from '../../app/modules/auth'
import { useBotRoutes } from '../../app/components/App/Dashboard/bots/BotsIndex/UseBotRoutes'

function ListBots() {
  const { t } = useTranslation(['botsPage', 'common'])
  const user = useSelector(userSelectors.getUser)
  const { isBotV3Enabled } = useBotRoutes(user?.account)

  return (
    <>
      <Helmet title={t('botsPage:TITLE_BOTS')} />
      {isBotV3Enabled ? <CurrentListBots /> : <LegacyListBots />}
    </>
  )
}

export { ListBots }
