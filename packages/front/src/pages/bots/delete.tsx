import React from 'react'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import botsApi, { botsQueryKey } from '../../api/bots'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'

const DeleteBots = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['botsPage', 'common'])
  const history = useHistory()

  const { isFetching, isError, isSuccess } = useQuery({
    queryKey: [botsQueryKey, id],
    queryFn: () => botsApi.getById(id),
    enabled: !!id,
  })

  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      await botsApi.delete(id)
    },
    onSuccess: async () => {
      toast.success(t('botsPage:BOT_DELETED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [botsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [botsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])
    },
    onError: () => {
      toast.error(t('botsPage:BOT_DELETED_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('botsPage:BOT_NOT_FOUND'))
      return history.replace('/bots')
    }
  }, [isError])

  return (
    <AlertDialog
      open
      onOpenChange={(open) => {
        if (!open) history.goBack()
      }}
    >
      <AlertDialogContent
        onCloseAutoFocus={(event) => {
          event.preventDefault()
          document.body.style.pointerEvents = ''
        }}
      >
        <AlertDialogHeader>
          <AlertDialogTitle>
            <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />
            {t('botsPage:DELETE_BOT')}
          </AlertDialogTitle>

          {isFetching && (
            <>
              <Skeleton width="80%" height="24px" />
              <Skeleton width="50%" height="24px" />
            </>
          )}

          {isSuccess && (
            <>
              <AlertDialogDescription>{t('common:IRREVERSIBLE_ACTION_LABEL')}</AlertDialogDescription>
              <AlertDialogDescription>{t('common:ARE_YOU_SURE_TO_CONTINUE')}</AlertDialogDescription>
            </>
          )}
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel className="w-full" data-testid="bots-delete-button-cancel">
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>

          <AlertDialogAction
            variant="danger"
            className="w-full"
            onClick={() => mutate()}
            disabled={isPending || isFetching}
            data-testid="bots-delete-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { DeleteBots }
