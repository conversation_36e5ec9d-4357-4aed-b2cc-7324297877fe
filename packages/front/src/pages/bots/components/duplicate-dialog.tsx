import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogBody,
  Label,
  DialogFooter,
  DialogClose,
  InputText,
  Button,
  toast,
} from '@ikatec/nebula-react'
import { Bo<PERSON> } from '../../../app/types/Bot'
import { useTranslation } from 'react-i18next'
import z from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError, HttpStatusCode } from 'axios'
import botsApi, { botsQueryKey } from '../../../api/bots'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Form, FormControl, FormField, FormItem, FormMessage } from '../../../app/components/common/unconnected/ui/form'

const formSchema = z.object({
  name: z
    .string()
    .trim()
    .nonempty({ message: 'botsPage:BOT_NAME_REQUIRED' })
    .max(255, { message: 'botsPage:BOT_NAME_MAX_LENGTH' })
    .default(''),
})

interface FormValues extends z.infer<typeof formSchema> {}

interface DuplicateDialogProps {
  selectedBot: Bot
  setSelectedBot: (bot: Bot) => void
}

export function DuplicateDialog({ selectedBot, setSelectedBot }: DuplicateDialogProps) {
  const { t } = useTranslation(['botsPage', 'common'])
  const queryClient = useQueryClient()

  const formContext = useForm<FormValues>({
    defaultValues: {
      name: '',
    },
    resetOptions: {
      keepDefaultValues: true,
    },
    shouldFocusError: true,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(formSchema),
  })

  React.useEffect(() => {
    if (selectedBot.name) {
      formContext.reset({
        name: `${selectedBot.name} - ${t('common:COPY')}`,
      })
      setTimeout(() => {
        formContext.setFocus('name')
      }, 0)
    }
  }, [selectedBot.name, t])

  const { mutateAsync: createBot } = useMutation({
    mutationFn: botsApi.create,
    onSuccess: () => {
      toast.success(t('botsPage:BOT_COPY_SUCCESS'))
      queryClient.invalidateQueries({
        queryKey: [botsQueryKey, 'list'],
      })
      setSelectedBot({} as Bot)
    },
    onError(error: AxiosError) {
      const errorHandlingMap = {
        [HttpStatusCode.Conflict]: 'botsPage:BOT_SAME_NAME_ERROR',
      }

      toast.error(t(errorHandlingMap[error.response?.status] || 'botsPage:BOT_COPY_ERROR'))
    },
  })

  function duplicatesBot(data: FormValues) {
    createBot({
      ...selectedBot,
      contexts: selectedBot.contexts,
      data: selectedBot.data,
      flowJson: selectedBot.flowJson,
      settings: selectedBot.settings,
      name: data.name,
    })
  }

  return (
    <Dialog open={Boolean(selectedBot.id)} onOpenChange={() => setSelectedBot({} as Bot)} key="duplicate-dialog">
      <Form {...formContext}>
        <form onSubmit={formContext.handleSubmit(duplicatesBot)}>
          <DialogContent
            onCloseAutoFocus={(event) => {
              event.preventDefault()
              document.body.style.pointerEvents = ''
            }}
          >
            <DialogHeader>
              <DialogTitle>{t('botsPage:ACTIONS_COLUMN_DUPLICATE')}</DialogTitle>
            </DialogHeader>
            <DialogBody>
              <FormField
                name="name"
                control={formContext.control}
                render={({ field, fieldState }) => {
                  return (
                    <FormItem>
                      <FormControl>
                        <>
                          <Label htmlFor="name">{t('common:LABEL_NAME')}</Label>
                          <InputText
                            id="name"
                            name="name"
                            type="text"
                            isError={Boolean(fieldState?.error?.message)}
                            data-testid="bots-duplicate-form-input-name"
                            {...field}
                          />
                        </>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )
                }}
              />
            </DialogBody>
            <DialogFooter>
              <DialogClose asChild>
                <Button variant="secondary" className="w-full" data-testid="bots-duplicate-form-button-cancel">
                  {t('common:FORM_ACTION_CLOSE')}
                </Button>
              </DialogClose>
              <Button
                type="submit"
                className="w-full"
                data-testid="bots-duplicate-form-button-confirm"
                disabled={!formContext.formState.isValid}
              >
                {t('common:BUTTON_TEXT_CONFIRM')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  )
}
