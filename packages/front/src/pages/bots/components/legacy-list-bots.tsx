import { Button, Heading, Space } from '@ikatec/nebula-react'
import { PlusIcon } from 'lucide-react'
import React from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import { type BotsFilters } from '../../../api/bots'
import IfUserCan from '../../../app/components/common/connected/IfUserCan'
import { InputFilter } from '../../../components/unconnected/table/input-filter'
import { useSearchParams } from '../../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../../layouts/container'
import { debounceFn } from '../../../utils/debouce-fn'
import { BotsTable } from './table'

function LegacyListBots() {
  const history = useHistory()
  const { t } = useTranslation(['botsPage', 'common'])
  const countableFilters: (keyof BotsFilters)[] = []
  const { params, setValues, set, hasFilter, clear, remove } = useSearchParams<BotsFilters>(
    {
      order: 'ASC',
      sort: 'name',
      page: 1,
    },
    countableFilters,
  )

  const navigateToCreate = (version: 'flow' | 'legacy') => {
    if (version === 'flow') setTimeout(() => history.push({ pathname: '/bots-v3/create' }))
    else setTimeout(() => history.push({ pathname: '/bots-v2/create' }))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'name', 'order', 'sort'])
  }

  const handleSetFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      set('name', value)
      remove(['page', 'order', 'sort'])
    })
  }

  return (
    <>
      <Helmet title={t('botsPage:TITLE_BOTS')} />
      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="bots-list-heading">
              {t('botsPage:TITLE_BOTS')}
            </Heading>
            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="bots-list-input-filter"
                name="name"
                defaultValue={params.name}
                onChange={(e) => handleSetFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />
              <IfUserCan permission="departments.create">
                <Button size="md" data-testid="bots-list-button-add" onClick={() => navigateToCreate('legacy')}>
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>
        <ContainerContent>
          <BotsTable
            params={params}
            setValues={setValues}
            hasFilter={hasFilter}
            clear={clear}
            navigateToCreate={navigateToCreate}
            set={set}
            currentTab="legacy"
          />
        </ContainerContent>
      </Container>
    </>
  )
}

export { LegacyListBots }
