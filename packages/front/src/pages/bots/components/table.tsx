import React, { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { CopyIcon, EditIcon, EllipsisVerticalIcon, EyeIcon, PlayIcon, Trash2Icon, WorkflowIcon } from 'lucide-react'
import { useHistory } from 'react-router-dom'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableHead,
  TableRow,
  Space,
  Button,
  Pagination,
} from '@ikatec/nebula-react'
import { Bot } from '../../../app/types/Bot'
import { useBotRoutes } from '../../../app/components/App/Dashboard/bots/BotsIndex/UseBotRoutes'
import { selectors as userSelectors } from '../../../app/modules/auth'
import { useSelector } from 'react-redux'
import { SortTableButton } from '../../../components/unconnected/table/sort-table-button'
import botsA<PERSON>, { <PERSON><PERSON><PERSON><PERSON><PERSON>, bots<PERSON>uery<PERSON><PERSON> } from '../../../api/bots'
import IfUserCan from '../../../app/components/common/connected/IfUserCan'
import { TableSkeleton } from '../../../components/unconnected/table/table-skeleton'
import { NoPossibleLoadData } from '../../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../../components/unconnected/table/no-results-found-without-filter'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { getPlaceholderData } from '../../../utils/get-placeholder-data'

import { DuplicateDialog } from './duplicate-dialog'

interface TableBotsIndexProps {
  params: BotsFilters
  setValues: (params: Partial<BotsFilters>) => void
  hasFilter: boolean
  clear: () => void
  navigateToCreate: (version: 'flow' | 'legacy') => void
  set: (key: string, value: string) => void
}

export function BotsTable({ params, setValues, hasFilter, clear, navigateToCreate, set }: TableBotsIndexProps) {
  const { t } = useTranslation(['botsPage', 'common'])
  const [selectedBot, setSelectedBot] = useState<Bot>({} as Bot)
  const user = useSelector(userSelectors.getUser)
  const { routeViewBot, routeViewFlowBot, routeEditBot } = useBotRoutes(user?.account)
  const history = useHistory()
  const queryClient = useQueryClient()

  const {
    data: bots,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [botsQueryKey, 'list', params],
    queryFn: () => botsApi.getAll({ ...params }),
    placeholderData() {
      return getPlaceholderData(queryClient.getQueriesData<typeof bots>({ queryKey: [botsQueryKey, 'list'] }))
    },
    staleTime: 0,
  })

  const { currentPage, data = [], limit, total } = bots ?? {}

  return (
    <>
      {/* if is fetching another page or is initial fetch */}
      {isFetching && <TableSkeleton />}

      {/* If not isFetching and isError, render error feedback */}
      {isError && <NoPossibleLoadData onRefresh={refetch} />}

      {/* If no data and is successful with filters, render empty state */}
      {!isFetching && data?.length === 0 && isSuccess && hasFilter && <NoResultsFoundByFilter onClearFilter={clear} />}

      {/* If no data and is successful without filters */}
      {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
        <NoResultsFoundWithoutFilter
          onAdd={() => navigateToCreate(params.tab)}
          moduleTitle={t('botsPage:NO_BOTS_CREATED')}
          moduleDescription={t('botsPage:START_CREATE_FIRST_BOT')}
        />
      )}

      {/* If has data and is successful, render table */}
      {isSuccess && data?.length > 0 && !isFetching && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Space size="md" className="justify-start items-center">
                  {t('botsPage:COLUMN_NAME')}
                  <SortTableButton
                    sort="name"
                    order={params.order}
                    currentSort={params.sort}
                    data-testid="bots-list-sort-name"
                    onChange={(head, order) => {
                      setValues({
                        ...params,
                        order,
                        sort: head as BotsFilters['sort'],
                      })
                    }}
                  />
                </Space>
              </TableHead>
              <TableHead className="w-[72px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((bot, index) => {
              return (
                <TableRow key={bot.id}>
                  <TableCell>
                    <div className="max-w-[600px] overflow-ellipsis overflow-hidden">{bot.name}</div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu key="dropdown">
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" icon data-testid={`bots-list-button-actions-${index}`}>
                          <EllipsisVerticalIcon />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {bot?.settings?.botCreatedVersion !== 'v3' && (
                          <DropdownMenuItem
                            onClick={() => history.push(`${routeViewBot(bot)}/${bot.id}`)}
                            data-testid={`bots-list-button-actions-${index}-view`}
                          >
                            <EyeIcon />
                            {t('common:ACTIONS_SUBMENU_VIEW')}
                          </DropdownMenuItem>
                        )}
                        {routeViewFlowBot(bot) && (
                          <DropdownMenuItem
                            onClick={() => history.push(`${routeViewFlowBot(bot)}/${bot.id}`)}
                            data-testid={`bots-list-button-actions-${index}-view-flow`}
                          >
                            {bot?.settings?.botCreatedVersion === 'v3' ? (
                              <>
                                <EyeIcon />
                                {t('common:ACTIONS_SUBMENU_VIEW')}
                              </>
                            ) : (
                              <>
                                <WorkflowIcon />
                                {t('botsPage:ACTIONS_VIEW_STREAM')}
                              </>
                            )}
                          </DropdownMenuItem>
                        )}
                        <IfUserCan permission="bots.update">
                          {routeEditBot(bot) && bot?.settings?.botCreatedVersion === 'v3' && (
                            <DropdownMenuItem
                              onClick={() => history.push(`${routeEditBot(bot)}/${bot.id}/simulator`)}
                              data-testid={`bots-list-button-actions-${index}-simulator`}
                            >
                              <PlayIcon />
                              {t('botsPage:BOT_SIMULATOR')}
                            </DropdownMenuItem>
                          )}
                        </IfUserCan>
                        <IfUserCan permission="bots.create">
                          <DropdownMenuItem
                            onClick={() => setSelectedBot(bot)}
                            data-testid={`bots-list-button-actions-${index}-duplicate`}
                          >
                            <CopyIcon />
                            {t('botsPage:ACTIONS_COLUMN_DUPLICATE')}
                          </DropdownMenuItem>
                        </IfUserCan>
                        <IfUserCan permission="bots.update">
                          {routeEditBot(bot) && (
                            <DropdownMenuItem
                              data-testid={`bots-list-button-actions-${index}-edit`}
                              onClick={() => history.push(`${routeEditBot(bot)}/${bot.id}/edit`)}
                            >
                              <EditIcon />
                              {t('common:ACTIONS_SUBMENU_EDIT')}
                            </DropdownMenuItem>
                          )}
                        </IfUserCan>
                        <IfUserCan permission="bots.destroy">
                          <DropdownMenuItem
                            onClick={() =>
                              history.push({ pathname: `/bots/${bot.id}/delete`, search: `?tab=${params.tab}` })
                            }
                            data-testid={`bots-list-button-actions-${index}-delete`}
                          >
                            <Trash2Icon />
                            {t('common:LABEL_DELETE')}
                          </DropdownMenuItem>
                        </IfUserCan>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      )}

      {/* if has data and isSuccessful, render pagination */}
      {Boolean(total && isSuccess) && (
        <Pagination
          page={currentPage}
          pageSize={limit}
          total={total}
          onChangePage={(page) => set('page', page.toString())}
        />
      )}

      <DuplicateDialog selectedBot={selectedBot} setSelectedBot={setSelectedBot} />
    </>
  )
}
