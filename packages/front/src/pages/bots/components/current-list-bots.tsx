import React from 'react'
import {
  Badge,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Heading,
  Paragraph,
  Space,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@ikatec/nebula-react'
import { BotIcon, PlusIcon, WorkflowIcon } from 'lucide-react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import { type BotsFilters } from '../../../api/bots'
import IfUserCan from '../../../app/components/common/connected/IfUserCan'
import { InputFilter } from '../../../components/unconnected/table/input-filter'
import { useSearchParams } from '../../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../../layouts/container'
import { debounceFn } from '../../../utils/debouce-fn'
import { BotsTable } from './table'
import { useBotRoutes } from '../../../app/components/App/Dashboard/bots/BotsIndex/UseBotRoutes'
import { useSelector } from 'react-redux'
import { selectors as userSelectors } from '../../../app/modules/auth'

function CurrentListBots() {
  const history = useHistory()
  const user = useSelector(userSelectors.getUser)
  const { isBotV2Enabled } = useBotRoutes(user?.account)
  const { t } = useTranslation(['botsPage', 'common'])
  const [hasFilterWithoutTab, setHasFilterWithoutTab] = React.useState(false)
  const countableFilters: (keyof BotsFilters)[] = []
  const { params, setValues, set, remove, searchParams } = useSearchParams<BotsFilters>(
    {
      order: 'ASC',
      sort: 'name',
      page: 1,
      tab: 'flow', // Default to 'flow' tab
    },
    countableFilters,
  )

  React.useEffect(() => {
    const urlParams = new URLSearchParams(searchParams)
    const urlKeys = Array.from(urlParams.keys())
    setHasFilterWithoutTab(
      urlKeys.some((key) => key !== 'tab' && urlParams.get(key) !== null && urlParams.get(key) !== ''),
    )
  }, [searchParams])

  const navigateToCreate = (version: 'flow' | 'legacy') => {
    if (version === 'flow') setTimeout(() => history.push({ pathname: '/bots-v3/create' }))
    else setTimeout(() => history.push(`/${isBotV2Enabled ? 'bots-v2' : 'bots-v1'}/create`))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'name', 'order', 'sort'])
  }

  const handleSetFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      set('name', value)
      remove(['page', 'order', 'sort'])
    })
  }

  return (
    <>
      <Helmet title={t('botsPage:TITLE_BOTS')} />
      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="bots-list-heading">
              {t('botsPage:TITLE_BOTS')}
            </Heading>
            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="bots-list-input-filter"
                name="name"
                defaultValue={params.name}
                onChange={(e) => handleSetFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />
              <IfUserCan permission="departments.create">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="md" data-testid="bots-list-button-add">
                      <PlusIcon />
                      {t('common:LIST_ADD_NEW')}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem
                      onClick={() => navigateToCreate('flow')}
                      data-testid="bots-list-dropdown-create-flow"
                    >
                      <WorkflowIcon />
                      {t('botsPage:FLOWCHART')}
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => navigateToCreate('legacy')}
                      data-testid="bots-list-dropdown-create-legacy"
                    >
                      <BotIcon />
                      {t('botsPage:INITIAL_VERSION')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>
        <Tabs
          value={params.tab}
          onValueChange={(value: 'flow' | 'legacy') => {
            remove(['page', 'name', 'order', 'sort'])
            set('tab', value)
          }}
        >
          <TabsList>
            <TabsTrigger value="flow" data-testid="bots-list-tab-flow">
              {t('botsPage:FLOWCHART')}
              <Badge variant="secondary" size="sm" className="px-2 uppercase">
                {t('common:MESSAGE_NEW')}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="legacy" data-testid="bots-list-tab-legacy">
              {t('botsPage:INITIAL_VERSION')}
            </TabsTrigger>
          </TabsList>
          <ContainerContent>
            <TabsContent value="flow" className="mt-6">
              <div className="mb-6">
                <Paragraph size="sm">{t('botsPage:ROBOT_BUILDER_INTRO')}</Paragraph>
                <Paragraph size="sm">{t('botsPage:ROBOT_BUILDER_NOTE')}</Paragraph>
              </div>
            </TabsContent>
            <TabsContent value="legacy" className="mt-6">
              <div className="mb-6">
                <Paragraph size="sm">{t('botsPage:ROBOT_BUILDER_INTRO_LEGACY')}</Paragraph>
                <Paragraph size="sm">{t('botsPage:ROBOT_BUILDER_NOTE_LEGACY')}</Paragraph>
              </div>
            </TabsContent>
            <BotsTable
              params={params}
              setValues={setValues}
              hasFilter={hasFilterWithoutTab}
              clear={handleClearInputFilter}
              navigateToCreate={navigateToCreate}
              set={set}
            />
          </ContainerContent>
        </Tabs>
      </Container>
    </>
  )
}

export { CurrentListBots }
