import React from 'react'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import tagsApi, { tagsQueryKey } from '../../api/tags'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'

const DeleteTags = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['tagsPage', 'common'])
  const history = useHistory()

  const { data, isFetching, isError, isSuccess } = useQuery({
    queryKey: [tagsQueryKey, id],
    queryFn: () => tagsApi.getById(id),
    enabled: !!id,
  })

  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      await tagsApi.delete(id)
    },
    onSuccess: async () => {
      toast.success(t('TAG_DELETED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [tagsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [tagsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])
    },
    onError: () => {
      toast.error(t('TAG_DELETION_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('TAG_NOT_FOUND'))
      return history.replace('/tags')
    }
  }, [isError])

  return (
    <AlertDialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/tags')
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />
            {t('common:LABEL_DELETE')} {t('tagsPage:TITLE_TAG')}
          </AlertDialogTitle>

          {isFetching && (
            <>
              <Skeleton width="80%" height="24px" />
              <Skeleton width="50%" height="24px" />
            </>
          )}

          {isSuccess && (
            <>
              <AlertDialogDescription>
                {Number(data?.linkedContacts) === 0
                  ? t('MODAL_DELETE_TAG_WARNING_WITHOUT_CONTACTS')
                  : t('MODAL_DELETE_TAG_WARNING_WITH_CONTACTS')}
              </AlertDialogDescription>
              <AlertDialogDescription>{t('MODAL_DELETE_TAG_CONFIRM_QUESTION')}</AlertDialogDescription>
            </>
          )}
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel className="w-full" data-testid="tags-delete-button-cancel">
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>

          <AlertDialogAction
            variant="danger"
            className="w-full"
            onClick={() => mutate()}
            disabled={isPending || isFetching}
            data-testid="tags-delete-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { DeleteTags }
