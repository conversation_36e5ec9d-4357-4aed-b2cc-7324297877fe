import React from 'react'
import { <PERSON><PERSON>, <PERSON>alogBody, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import tagsApi, { TagData, tagsQueryKey } from '../../api/tags'
import { TagsForm } from './components/form'
import { AxiosError } from 'axios'
import { useEffect } from 'react'

const EditTags = () => {
  const { t } = useTranslation(['tagsPage', 'common'])
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching } = useQuery({
    queryKey: [tagsQueryKey, id],
    queryFn: () => tagsApi.getById(id),
    enabled: Boolean(id),
  })
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: TagData) => tagsApi.update(id, values),
    onSuccess: async () => {
      toast.success(t('TAG_UPDATED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [tagsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [tagsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])

      history.replace('/tags')
    },
    onError: (error: AxiosError) => {
      const { status } = error.response || {}
      if (status === 409) {
        toast.error(t('MODAL_DUPLICATE_NAME'))
        return
      }

      toast.error(t('MODAL_MESSAGE_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('TAG_NOT_FOUND'))
      return history.replace('/tags')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/tags')
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {t('common:LABEL_EDITING_STAGE')} {t('tagsPage:TITLE_TAG')}
          </DialogTitle>
        </DialogHeader>
        <DialogBody>
          <TagsForm
            defaultValues={data}
            onSubmit={(values) => mutateAsync(values)}
            onCancel={() => history.replace('/tags')}
            isPending={isPending}
            isFetching={isFetching}
          />
        </DialogBody>
      </DialogContent>
    </Dialog>
  )
}

export { EditTags }
