import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { useHistory, useParams } from 'react-router'
import tagsApi, { tagsQuery<PERSON>ey } from '../../api/tags'
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
  Button,
  Space,
  Label,
  Tag,
  toast,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect, useMemo } from 'react'
import { oldColorsToNewColors } from './constants'

const ViewTags = () => {
  const { t } = useTranslation(['tagsPage', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [tagsQueryKey, id],
    queryFn: () => tagsApi.getById(id),
    enabled: Boolean(id),
  })

  const departments = useMemo(() => {
    return data?.departments?.length ? data.departments : [{ id: 'all', name: t('tagsPage:ALL') }]
  }, [data?.departments])

  useEffect(() => {
    if (isError) {
      toast.error(t('TAG_NOT_FOUND'))
      return history.replace('/tags')
    }
  }, [isError])

  const tagColor = useMemo(() => {
    return oldColorsToNewColors.get(data?.backgroundColor) ?? data?.backgroundColor
  }, [data?.backgroundColor])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/tags')
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('VIEW_TAG')}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          {isFetching && (
            <>
              <Space size="md">
                <div className="flex-1">
                  <Label>{t('TAG_NAME_LABEL')}</Label>
                  <p>{<Skeleton width="150px" height="24px" />}</p>
                </div>

                <div className="flex-1">
                  <Label>{t('COLOR_TAG')}</Label>
                  <div className="w-6 h-6 rounded-md overflow-hidden">{<Skeleton width="100%" height="100%" />}</div>
                </div>
              </Space>

              <div>
                <Label>{t('DEPARTMENTS_LABEL')}</Label>
                <div>
                  <Skeleton width="100px" height="24px" />
                </div>
              </div>
            </>
          )}

          {isSuccess && !isFetching && (
            <div>
              <Space size="md">
                <div className="flex-1">
                  <Label>{t('TAG_NAME_LABEL')}</Label>
                  <p className="max-w-64 break-words dark:text-neutral-200">{data.label}</p>
                </div>

                <div className="flex-1">
                  <Label>{t('COLOR_TAG')}</Label>
                  <div
                    className="w-6 h-6 rounded-md border-neutral-400 dark:border-neutral-600 border-dashed"
                    style={{
                      backgroundColor: tagColor ?? 'transparent',
                      borderWidth: tagColor ? 0 : 1,
                    }}
                  />
                </div>
              </Space>

              <div className="mt-6">
                <Label>{t('DEPARTMENTS_LABEL')}</Label>
                <Space className="flex flex-wrap justify-start" size="sm">
                  {departments.map((department: (typeof departments)[number]) => (
                    <Tag key={department.id}>{department.name}</Tag>
                  ))}
                </Space>
              </div>
            </div>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="tags-view-button-close">
              {t('common:FORM_ACTION_CLOSE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { ViewTags }
