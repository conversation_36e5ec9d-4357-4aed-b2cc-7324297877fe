import React from 'react'
import { <PERSON><PERSON>, DialogBody, Dialog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import tagsApi, { TagData, tagsQueryKey } from '../../api/tags'
import { TagsForm } from './components/form'

const CreateTags = () => {
  const { t } = useTranslation(['tagsPage', 'common'])
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: TagData) => tagsApi.create(values),
    onSuccess: () => {
      toast.success(t('TAG_CREATED_SUCCESS'))

      queryClient.invalidateQueries({
        queryKey: [tagsQueryKey, 'list'],
      })

      history.replace('/tags')
    },
    onError: (error: AxiosError) => {
      const { status } = error.response || {}
      if (status === 409) {
        toast.error(t('MODAL_DUPLICATE_NAME'))
        return
      }

      toast.error(t('TAG_CREATION_ERROR'))
    },
  })

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/tags')
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {t('common:LIST_ADD_NEW')} {t('tagsPage:TITLE_TAG')}
          </DialogTitle>
        </DialogHeader>
        <DialogBody>
          <TagsForm
            onSubmit={(values) => mutateAsync(values)}
            onCancel={() => history.replace('/tags')}
            isPending={isPending}
          />
        </DialogBody>
      </DialogContent>
    </Dialog>
  )
}

export { CreateTags }
