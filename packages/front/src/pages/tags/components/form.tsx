import React from 'react'
import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { Alert, AlertDescription, Button, InputText, Label, Space } from '@ikatec/nebula-react'
import { AlertTriangleIcon, CheckIcon, LoaderIcon } from 'lucide-react'
import { useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import z from 'zod'
import { type TagData } from '../../../api/tags'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from '../../../app/components/common/unconnected/ui/form'
import { getUser } from '../../../app/modules/auth/selectors'
import { Tag } from '../../../app/types/Tag'
import { formTagColorMap, oldColorsToNewColors } from '../constants'
import { Skeleton } from '../../../app/components/common/unconnected/ui/skeleton'
import { InputSkeleton } from '../../../components/unconnected/skeletons/input-skeleton'
import { DepartmentsSelect } from '../../../components/connected/departments-select'

interface TagsFormProps {
  onCancel: VoidFunction
  onSubmit: (values: TagData) => void
  defaultValues?: Partial<Tag>
  isPending?: boolean
  isFetching?: boolean
}

const formSchema = z.object({
  label: z
    .string()
    .trim()
    .min(1, { message: 'tagsPage:TAG_NAME_REQUIRED' })
    .max(80, { message: 'tagsPage:TAG_NAME_MAX_LIMIT' }),
  backgroundColor: z.string({ message: 'tagsPage:SELECT_COLOR' }).min(1, { message: 'tagsPage:SELECT_COLOR' }),
  departments: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
      }),
    )
    .min(1, { message: 'tagsPage:SELECT_AT_LEAST_ONE_DEPARTMENT' }),
})

interface FormValues extends z.infer<typeof formSchema> {}

const TagsForm = ({ onCancel, onSubmit, defaultValues, isPending, isFetching }: TagsFormProps) => {
  const user = useSelector(getUser)
  const [showTagWarning, setShowTagWarning] = useState(false)
  const { t } = useTranslation(['tagsPage', 'common'])

  const userDepartments = user.departments

  const userIsAdmin = useMemo(() => user?.isSuperAdmin || user?.roles?.some((role) => role.isAdmin), [user])

  const tagColor = useMemo(() => {
    return oldColorsToNewColors.get(defaultValues?.backgroundColor) ?? defaultValues?.backgroundColor
  }, [defaultValues?.backgroundColor])

  const formContext = useForm<FormValues>({
    defaultValues: {
      label: '',
      departments: [],
      ...defaultValues,
      backgroundColor: tagColor,
    },
    resetOptions: {
      keepDefaultValues: true,
    },
    shouldFocusError: true,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(formSchema),
  })

  const handleSubmit = (values: Tag) => {
    const departments = values.departments.map((department) => department.id)
    const isAll = departments.some((id) => id === 'all')

    onSubmit({
      ...values,
      label: values.label,
      departments: isAll ? [] : departments,
    })
  }

  useEffect(() => {
    if (!defaultValues) return

    formContext.reset({
      ...defaultValues,
      backgroundColor: tagColor,
      departments: defaultValues?.departments?.length
        ? defaultValues.departments
        : [{ id: 'all', name: t('tagsPage:ALL') }],
    })
  }, [defaultValues])

  useEffect(() => {
    if (userIsAdmin || userDepartments.length === 0) return

    // Verifica se os departamentos selecionados pelo usuário contém pelo menos um departamento que o usuário faz parte

    // Caso não exista, exibe um alerta informando que o usuário não poderá visualizar/utilizar a tag criada

    const hasAtLeastOneDepartmentFromUserDepartments = formContext
      .watch('departments')
      ?.some((department) =>
        userDepartments.some((userDepartment) => userDepartment.id === department.id || department.id === 'all'),
      )

    if (!hasAtLeastOneDepartmentFromUserDepartments) {
      setShowTagWarning(true)
    }

    return () => {
      setShowTagWarning(false)
    }
  }, [formContext.watch('departments')])

  if (isFetching) {
    return (
      <div>
        <Space direction="column" size="xl" className="w-full">
          <div className="w-full">
            <InputSkeleton label={t('tagsPage:COLUMN_TABLE_NAME')} />
          </div>

          <div className="w-full">
            <Label>{t('tagsPage:COLOR_TAG_LABEL')}</Label>
            <Space size="sm" className="justify-start">
              {[...formTagColorMap].map(() => (
                <Skeleton width="100%" height="40px" className="w-6 h-6 rounded-md" />
              ))}
            </Space>
          </div>

          <div className="w-full">
            <InputSkeleton label={t('tagsPage:LINK_TAGS_TO_DEPARTMENTS')} />
          </div>
        </Space>

        <Space className="w-full mt-10">
          <Button className="w-full" variant="secondary" type="button" onClick={onCancel}>
            {t('common:FORM_ACTION_CANCEL')}
          </Button>
          <Button className="w-full" variant="primary" type="submit" disabled>
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:FORM_ACTION_SAVE')}
          </Button>
        </Space>
      </div>
    )
  }

  return (
    <Form {...formContext}>
      <form onSubmit={formContext.handleSubmit(handleSubmit)}>
        <Space direction="column" size="xl" className="w-full">
          <div className="w-full">
            <FormField
              name="label"
              control={formContext.control}
              render={({ field, fieldState }) => {
                return (
                  <FormItem>
                    <FormControl>
                      <>
                        <Label htmlFor="name">{t('tagsPage:COLUMN_TABLE_NAME')}</Label>
                        <InputText
                          data-testid="tags-form-input-name"
                          type="text"
                          id="name"
                          name="name"
                          isError={Boolean(fieldState?.error?.message)}
                          {...field}
                        />
                      </>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>

          <div className="w-full">
            <FormField
              name="backgroundColor"
              control={formContext.control}
              render={({ field }) => {
                return (
                  <FormItem>
                    <Label>{t('tagsPage:COLOR_TAG_LABEL')}</Label>
                    <FormControl>
                      <Space size="sm" className="justify-start">
                        {[...formTagColorMap].map(([key], index) => {
                          const isSelected = field.value === key
                          return (
                            <button
                              data-testid={`tags-form-color-${index}`}
                              className="w-6 h-6 rounded-md cursor-pointer flex items-center justify-center"
                              key={key}
                              style={{ background: key }}
                              role="option"
                              aria-selected={isSelected}
                              type="button"
                              onClick={() => {
                                field.onChange(key)
                              }}
                            >
                              {isSelected ? <CheckIcon color="#fff" className="w-4 h-4" /> : null}
                            </button>
                          )
                        })}
                      </Space>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )
              }}
            />
          </div>

          <div className="w-full">
            <FormField
              name="departments"
              control={formContext.control}
              render={({ field }) => (
                <FormItem>
                  <Label>{t('tagsPage:LINK_TAGS_TO_DEPARTMENTS')}</Label>
                  <FormControl>
                    <DepartmentsSelect
                      className="w-full"
                      id="tags-form-departments-select"
                      hideArchived
                      placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                      isMulti
                      onChange={(selected: { value: string; label: string }[]) => {
                        if (selected.some((item) => item.value === 'all')) {
                          field.onChange([
                            {
                              id: 'all',
                              name: t('tagsPage:ALL'),
                            },
                          ])
                        } else {
                          field.onChange(
                            selected.map((item) => ({
                              id: item.value,
                              name: item.label,
                            })),
                          )
                        }
                      }}
                      value={
                        Array.isArray(field.value)
                          ? field.value.map((item: any) => ({
                              value: item.id,
                              label: item.name,
                            }))
                          : []
                      }
                      extraOptions={[
                        {
                          value: 'all',
                          label: t('tagsPage:ALL'),
                        },
                      ]}
                    />
                  </FormControl>
                  <FormMessage />
                  {field.value?.some?.((item) => item.id === 'all') && (
                    <FormDescription>{t('tagsPage:TAG_AVAILABLE_TO_ALL_DEPARTMENTS')}</FormDescription>
                  )}
                </FormItem>
              )}
            />
          </div>

          {showTagWarning && (
            <Alert variant="warning">
              <AlertTriangleIcon className="w-6 h-6" />
              <AlertDescription>{t('tagsPage:TAG_DEPARTMENT_WARNING')}</AlertDescription>
            </Alert>
          )}
        </Space>

        <Space className="w-full mt-10">
          <Button
            className="w-full"
            variant="secondary"
            type="button"
            onClick={onCancel}
            data-testid="tags-form-button-cancel"
          >
            {t('common:FORM_ACTION_CANCEL')}
          </Button>
          <Button
            className="w-full"
            variant="primary"
            type="submit"
            disabled={isPending || !formContext.formState.isValid}
            data-testid="tags-form-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </Space>
      </form>
    </Form>
  )
}

export { TagsForm }
