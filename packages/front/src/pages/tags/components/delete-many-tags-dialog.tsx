import React from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  Button,
  toast,
} from '@ikatec/nebula-react'

import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'

import tagsApi, { tagsQueryKey } from '../../../api/tags'
import { DialogProps } from '@radix-ui/react-dialog'

interface DeleteManyTagsDialogProps extends DialogProps {
  tagsId: string[]
  onSuccess: VoidFunction
}

const DeleteManyTagsDialog = ({ tagsId, onSuccess, ...rest }: DeleteManyTagsDialogProps) => {
  const { t } = useTranslation(['tagsPage', 'common'])
  const queryClient = useQueryClient()
  const { mutateAsync: deleteTags, isPending } = useMutation({
    mutationFn: async () => {
      await tagsApi.deleteManyByIds(tagsId)
    },
    onSuccess: async () => {
      onSuccess()
      toast.success(t('TAG_DELETED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [tagsQueryKey, 'list'],
        }),

        ...tagsId.map((tagId) =>
          queryClient.invalidateQueries(
            {
              queryKey: [tagsQueryKey, tagId],
            },
            { cancelRefetch: true },
          ),
        ),
      ])
    },
    onError: () => {
      toast.error(t('TAG_DELETION_ERROR'))
    },
  })
  return (
    <AlertDialog modal {...rest}>
      <AlertDialogContent className="text-center gap-4">
        <AlertDialogHeader>
          <AlertTriangleIcon size={40} className="text-red-700" />
          <AlertDialogTitle>{t('MODAL_DELETE_TAG_TITLE')}</AlertDialogTitle>

          <AlertDialogDescription>{t('MODAL_DELETE_TAG_WARNING_MANY')}</AlertDialogDescription>
          <AlertDialogDescription>{t('MODAL_DELETE_TAG_CONFIRM_QUESTION')}</AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel
            className="w-full"
            type="button"
            variant="secondary"
            data-testid="tags-delete-many-button-cancel"
          >
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>
          <Button
            className="w-full"
            variant="danger"
            onClick={() => deleteTags()}
            disabled={isPending}
            data-testid="tags-delete-many-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { DeleteManyTagsDialog }
