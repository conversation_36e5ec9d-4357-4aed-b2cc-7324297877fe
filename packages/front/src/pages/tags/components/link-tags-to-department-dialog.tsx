import React from 'react'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  DialogBody,
  <PERSON>alog<PERSON>lose,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  Di<PERSON>Title,
  toast,
} from '@ikatec/nebula-react'
import { DialogProps } from '@radix-ui/react-dialog'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Building2, LoaderIcon } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'
import tagsApi, { tagsQueryKey } from '../../../api/tags'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../app/components/common/unconnected/ui/form'
import { DepartmentsSelect } from '../../../components/connected/departments-select'

const formSchema = z.object({
  departments: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
      }),
    )
    .min(1, { message: 'tagsPage:SELECT_AT_LEAST_ONE_DEPARTMENT' }),
})

interface FormValues extends z.infer<typeof formSchema> {}

interface LinkTagsToDepartmentDialogProps extends DialogProps {
  tagsId: string[]
  onSuccess: VoidFunction
}

const LinkTagsToDepartmentDialog = ({ tagsId, onSuccess, ...rest }: LinkTagsToDepartmentDialogProps) => {
  const { t } = useTranslation(['tagsPage', 'common'])

  const queryClient = useQueryClient()

  const form = useForm<FormValues>({
    defaultValues: {
      departments: [],
    },
    resolver: zodResolver(formSchema),
  })

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: FormValues) => {
      const departmentIds = values.departments.map((department) => department.id)
      return tagsApi.attachManyDepartments(tagsId, departmentIds.includes('all') ? [] : departmentIds)
    },
    onSuccess: async () => {
      onSuccess()
      toast.success(t('TAG_LINK_DEPARTMENTS_SUCCESS'))
      form.reset()

      await Promise.all(
        tagsId.map((tagId) =>
          queryClient.invalidateQueries(
            {
              queryKey: [tagsQueryKey, tagId],
            },
            { cancelRefetch: true },
          ),
        ),
      )
    },
    onError: (error: AxiosError) => {
      const { status } = error.response || {}
      if (status === 409) {
        toast.error(t('MODAL_DUPLICATE_NAME'))
        return
      }

      toast.error(t('MODAL_MESSAGE_ERROR'))
    },
  })

  return (
    <Dialog {...rest}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('LINK_TAGS_TO_DEPARTMENTS')}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Form {...form}>
            <form onSubmit={form.handleSubmit((values) => mutateAsync(values))}>
              <FormField
                control={form.control}
                name="departments"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('tagsPage:DEPARTMENTS_LABEL')}</FormLabel>
                    <FormControl>
                      <DepartmentsSelect
                        className="w-full"
                        icon={<Building2 className="w-5 h-5 mr-2 text-inputText-icon-default" />}
                        id="chatDepartmentsSelect"
                        hideArchived
                        placeholder={t('common:SELECT_LOADING_PLACEHOLDER')}
                        isMulti
                        data-testid="tags-link-departments-select"
                        onChange={(selected: { value: string; label: string }[]) => {
                          if (selected.some((item) => item.value === 'all')) {
                            field.onChange([
                              {
                                id: 'all',
                                name: t('tagsPage:ALL'),
                              },
                            ])
                          } else {
                            field.onChange(
                              selected.map((item) => ({
                                id: item.value,
                                name: item.label,
                              })),
                            )
                          }
                        }}
                        value={
                          Array.isArray(field.value)
                            ? field.value.map((item: any) => ({
                                value: item.id,
                                label: item.name,
                              }))
                            : []
                        }
                        extraOptions={[
                          {
                            value: 'all',
                            label: t('tagsPage:ALL'),
                          },
                        ]}
                      />
                    </FormControl>
                    <FormMessage />
                    {field.value?.some((item) => item.id === 'all') && (
                      <FormDescription>{t('tagsPage:TAG_AVAILABLE_TO_ALL_DEPARTMENTS')}</FormDescription>
                    )}
                  </FormItem>
                )}
              />

              <DialogFooter className="mt-8">
                <DialogClose asChild>
                  <Button
                    className="w-full"
                    type="button"
                    variant="secondary"
                    data-testid="tags-link-to-department-button-cancel"
                  >
                    {t('common:FORM_ACTION_CANCEL')}
                  </Button>
                </DialogClose>
                <Button
                  className="w-full"
                  type="submit"
                  disabled={isPending || !form.formState.isDirty}
                  data-testid="tags-link-to-department-button-confirm"
                >
                  {isPending ? <LoaderIcon className="animate-spin" /> : null}
                  {t('common:BUTTON_TEXT_CONFIRM')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogBody>
      </DialogContent>
    </Dialog>
  )
}

export { LinkTagsToDepartmentDialog }
