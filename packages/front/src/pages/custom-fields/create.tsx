import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>readcrumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
  Button,
  Heading,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError, HttpStatusCode } from 'axios'
import { ChevronLeftIcon } from 'lucide-react'
import React, { useCallback, useRef } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import { Link } from 'react-router-dom'
import customFieldsApi, { CustomFieldsData, CustomFieldsQueryKey } from '../../api/custom-fields'
import useToggle from '../../app/hooks/useToggle'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { CustomFieldForm, CustomFieldValues } from './components/form'
import { LightningLearningBox } from './components/lightning-learning-box'
import { CustomFieldType } from './constants'

const CreateCustomFields = () => {
  const { t } = useTranslation(['customFields', 'common'])
  const history = useHistory()

  const formRef = useRef(null)
  const queryClient = useQueryClient()
  const parseCustomFieldData = (values: CustomFieldValues): CustomFieldsData => {
    return {
      allowed: values.allowed,
      name: values.name,
      required: values.required,
      settings: values.settings,
      showOnRegister: values.showOnRegister,
      type: values.type as CustomFieldType,
    }
  }
  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (values: CustomFieldValues) => customFieldsApi.create(parseCustomFieldData(values)),
    onSuccess: () => {
      toast.success(t('CUSTOM_FIELD_ADD_SUCCESS'))

      queryClient.invalidateQueries({
        queryKey: [CustomFieldsQueryKey],
      })
      history.replace('/custom-fields')
    },
    onError: (error: AxiosError<{ message: string }>) => {
      const errorMap = {
        [HttpStatusCode.Conflict]: t('MODAL_DUPLICATE_NAME'),
      }
      toast.error(errorMap[error.response.status] ?? t('MODAL_MESSAGE_ERROR'))
    },
  })

  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const handleGoBack = useCallback(() => {
    if (formRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/custom-fields')
  }, [formRef])

  return (
    <Container>
      <Helmet title={`${t('common:LABEL_CREATING')} ${t('TITLE_CUSTOM_FIELDS_SINGULAR')}`} />
      <ContainerHeader mtSize="sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <Link to="/custom-fields">{t('TITLE_CUSTOM_FIELDS')}</Link>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                {t('common:LIST_ADD_NEW')} {t('TITLE_CUSTOM_FIELDS_SINGULAR').toLowerCase()}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Space className="w-full items-center" size="sm">
          <Button
            icon
            variant="ghost"
            onClick={handleGoBack}
            aria-label="Go back to users' list"
            data-testid="custom-fields-add-button-go-back"
            role="link"
          >
            <ChevronLeftIcon />
          </Button>

          <Heading level="1" data-testid="custom-fields-create-heading">
            {t('common:LIST_ADD_NEW')} {t('TITLE_CUSTOM_FIELDS_SINGULAR').toLowerCase()}
          </Heading>
        </Space>
      </ContainerHeader>
      <ContainerContent>
        <Space className="gap-6">
          <div className="flex-1">
            <CustomFieldForm
              ref={formRef}
              onSubmit={(values) => mutateAsync(values)}
              onCancel={handleGoBack}
              isPending={isPending}
            />
          </div>
          <aside className="hidden md-block">
            <LightningLearningBox />
          </aside>
        </Space>
      </ContainerContent>

      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={() => history.replace('/custom-fields')}
      />
    </Container>
  )
}

export { CreateCustomFields }
