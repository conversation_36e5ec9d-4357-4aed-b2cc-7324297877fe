import i18n from '../../client/i18n'

export enum CustomFieldType {
  'type' = 'type',
  'long-text' = 'long-text',
  'numeric' = 'numeric',
  'monetary' = 'monetary',
  'phone' = 'phone',
  'email' = 'email',
  'postal-code' = 'postal-code',
  'hour' = 'hour',
  'hour-range' = 'hour-range',
  'date' = 'date',
  'date-range' = 'date-range',
  'cpf' = 'cpf',
  'cnpj' = 'cnpj',
  'text' = 'text',
  'list' = 'list',
  'checkbox' = 'checkbox',
}

export const CustomFieldTypeToTranslateKey = {
  [CustomFieldType.type]: { label: 'OPTION_FIELD_SHORT_TEXT_TEXT', description: 'OPTION_FIELD_SHORT_TEXT_DESCRIPTION' },
  [CustomFieldType.text]: { label: 'OPTION_FIELD_SHORT_TEXT_TEXT', description: 'OPTION_FIELD_SHORT_TEXT_DESCRIPTION' },
  [CustomFieldType['long-text']]: {
    label: 'OPTION_FIELD_LONG_TEXT_TEXT',
    description: 'OPTION_FIELD_LONG_TEXT_DESCRIPTION',
  },
  [CustomFieldType.numeric]: { label: 'OPTION_FIELD_NUMBER_TEXT', description: 'OPTION_FIELD_NUMBER_DESCRIPTION' },
  [CustomFieldType.monetary]: { label: 'OPTION_FIELD_CURRENCY_TEXT', description: 'OPTION_FIELD_CURRENCY_DESCRIPTION' },
  [CustomFieldType.phone]: { label: 'OPTION_FIELD_PHONE_TEXT', description: 'OPTION_FIELD_PHONE_DESCRIPTION' },
  [CustomFieldType.email]: { label: 'OPTION_FIELD_EMAIL_TEXT', description: 'OPTION_FIELD_EMAIL_DESCRIPTION' },
  [CustomFieldType['postal-code']]: {
    label: 'OPTION_FIELD_POSTAL_CODE_TEXT',
    description: 'OPTION_FIELD_POSTAL_CODE_DESCRIPTION',
  },
  [CustomFieldType.hour]: { label: 'OPTION_FIELD_HOUR_TEXT', description: 'OPTION_FIELD_HOUR_DESCRIPTION' },
  [CustomFieldType['hour-range']]: {
    label: 'OPTION_FIELD_HOUR_RANGE_TEXT',
    description: 'OPTION_FIELD_HOUR_RANGE_DESCRIPTION',
  },
  [CustomFieldType.date]: { label: 'OPTION_FIELD_DATE_TEXT', description: 'OPTION_FIELD_DATE_DESCRIPTION' },
  [CustomFieldType['date-range']]: {
    label: 'OPTION_FIELD_DATE_RANGE_TEXT',
    description: 'OPTION_FIELD_DATE_RANGE_DESCRIPTION',
  },
  [CustomFieldType.cpf]: { label: 'OPTION_FIELD_CPF_TEXT', description: 'OPTION_FIELD_CPF_DESCRIPTION' },
  [CustomFieldType.cnpj]: { label: 'OPTION_FIELD_CNPJ_TEXT', description: 'OPTION_FIELD_CNPJ_DESCRIPTION' },
  [CustomFieldType.list]: { label: 'OPTION_FIELD_LIST_TEXT', description: 'OPTION_FIELD_LIST_DESCRIPTION' },
  [CustomFieldType.checkbox]: { label: 'OPTION_FIELD_CHECKBOX_TEXT', description: 'OPTION_FIELD_CHECKBOX_DESCRIPTION' }, // novo tipo
}

// Defina o allowedFieldsType como um array que realmente contenha valores:
export const allowedFieldsType = [
  CustomFieldType.type,
  CustomFieldType['long-text'],
  CustomFieldType.numeric,
  CustomFieldType.monetary,
  CustomFieldType.list,
  CustomFieldType.checkbox,
  CustomFieldType.phone,
  CustomFieldType.email,
  CustomFieldType['postal-code'],
  CustomFieldType.hour,
  CustomFieldType['hour-range'],
  CustomFieldType.date,
  CustomFieldType['date-range'],
  CustomFieldType.cpf,
  CustomFieldType.cnpj,
]

export const legacyAllowedFieldsType = [CustomFieldType.text]

export const SETTINGS_INPUT_NAME_SEPARATOR = '.'
export const SETTINGS_INPUT_PREFIX_ON_FORM = 'settings' + SETTINGS_INPUT_NAME_SEPARATOR

export const DEFAULT_ALLOWED_OPTIONS = {
  contacts: {
    label: i18n.t('customFields:OPTION_SELECT_CONTACTS'),
    value: 'contacts',
  },
}

export const DEFAULT_TYPES_OPTIONS = allowedFieldsType.reduce((acc, type) => {
  acc[type] = {
    value: type,
    divider: type === CustomFieldType.cpf,
  }

  return acc
}, {})

export const DEFAULT_TYPES_OPTIONS_FOR_EDITING = [...legacyAllowedFieldsType, ...allowedFieldsType].reduce(
  (acc, type) => {
    acc[type] = {
      value: type,
      divider: type === CustomFieldType.cpf,
    }

    return acc
  },
  {},
)

export const CURRENCY_OPTIONS = [
  {
    code: 'AFN',
    symbol: '؋',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'ALL',
    symbol: 'L',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'ARS',
    symbol: '$',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'AUD',
    symbol: 'A$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'BRL',
    symbol: 'R$',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'CAD',
    symbol: 'C$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'CHF',
    symbol: 'CHF',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'CLP',
    symbol: '$',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'CNY',
    symbol: '¥',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'COP',
    symbol: '$',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'CZK',
    symbol: 'Kč',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'DKK',
    symbol: 'kr',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'EGP',
    symbol: '£',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'EUR',
    symbol: '€',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'GBP',
    symbol: '£',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'HKD',
    symbol: 'HK$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'HUF',
    symbol: 'Ft',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'IDR',
    symbol: 'Rp',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'ILS',
    symbol: '₪',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'INR',
    symbol: '₹',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'JPY',
    symbol: '¥',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'KRW',
    symbol: '₩',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'MXN',
    symbol: '$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'MYR',
    symbol: 'RM',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'NOK',
    symbol: 'kr',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'NZD',
    symbol: 'NZ$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'PEN',
    symbol: 'S/',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'PHP',
    symbol: '₱',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'PLN',
    symbol: 'zł',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'RUB',
    symbol: '₽',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'SAR',
    symbol: 'ر.س',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'SEK',
    symbol: 'kr',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'SGD',
    symbol: 'S$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'THB',
    symbol: '฿',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'TRY',
    symbol: '₺',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'UAH',
    symbol: '₴',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'USD',
    symbol: '$',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'VES',
    symbol: 'Bs',
    decimalSeparator: ',',
    thousandSeparator: '.',
  },
  {
    code: 'VND',
    symbol: '₫',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
  {
    code: 'ZAR',
    symbol: 'R',
    decimalSeparator: '.',
    thousandSeparator: ',',
  },
]
