import { InputText, Label, Paragraph } from '@ikatec/nebula-react'
import React from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { FormField, FormMessage } from '../../../../app/components/common/unconnected/ui/form'
import { SETTINGS_INPUT_PREFIX_ON_FORM } from '../../constants'

export function LongTextSettingsPresets({ disabled }) {
  const { t } = useTranslation(['customFields', 'common'])

  const form = useFormContext()
  const name = SETTINGS_INPUT_PREFIX_ON_FORM + 'placeholder'

  return (
    <div className="w-full">
      <FormField
        name={name}
        control={form.control}
        render={({ field, fieldState }) => {
          return (
            <>
              <div className="grid gap-y-1">
                <fieldset>
                  <Label htmlFor="custom-field-form-long-text-placeholder">
                    {t('SHORT_TEXT_SETTINGS_PLACEHOLDER_LABEL')}
                  </Label>
                  <InputText
                    {...field}
                    value={field.value ?? ''}
                    isError={!!fieldState.error?.message}
                    disabled={disabled}
                    id="custom-field-form-long-text-placeholder"
                    data-testid="custom-field-form-long-text-placeholder"
                  />
                </fieldset>
                <Paragraph size="sm">{t('SHORT_TEXT_SETTINGS_PLACEHOLDER_DESCRIPTION')}</Paragraph>
              </div>
              <FormMessage />
            </>
          )
        }}
      />
    </div>
  )
}
