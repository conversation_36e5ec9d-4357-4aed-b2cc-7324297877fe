import { InputText, Label, Paragraph, Select, Separator, Space, Switch } from '@ikatec/nebula-react'
import React, { useEffect, useState } from 'react'
import { useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { FormField, FormMessage } from '../../../../app/components/common/unconnected/ui/form'
import { CURRENCY_OPTIONS, SETTINGS_INPUT_PREFIX_ON_FORM } from '../../constants'

const options = CURRENCY_OPTIONS.map((item) => {
  return { value: item.code, label: item.code }
})

export function MonetarySettingsPresets({ disabled }) {
  const { t } = useTranslation(['customFields', 'common'])
  const form = useFormContext()
  const name = SETTINGS_INPUT_PREFIX_ON_FORM + 'currency'
  const nameMin = SETTINGS_INPUT_PREFIX_ON_FORM + 'min'
  const nameMax = SETTINGS_INPUT_PREFIX_ON_FORM + 'max'
  const minValue = form.watch(nameMin)
  const maxValue = form.watch(nameMax)
  const hasValue = minValue || maxValue
  const [minAndMax, setMinAndMax] = useState<Boolean>(!!hasValue)

  useEffect(() => {
    if (maxValue && minValue) {
      if (+minValue < +maxValue) {
        form.clearErrors(nameMin)
        form.clearErrors(nameMax)
      }
    }
  }, [maxValue, minValue])

  useEffect(() => {
    if (!minAndMax) {
      form.setValue(nameMin, '')
      form.setValue(nameMax, '')
    }
  }, [minAndMax])

  return (
    <>
      <FormField
        name={name}
        control={form.control}
        render={({ field }) => (
          <>
            <fieldset className="w-full">
              <Label>
                {t('MONETARY_SETTINGS_PLACEHOLDER_LABEL')} ({t('common:OPTIONAL_LABEL')})
              </Label>
              <Select
                isClearable
                options={options}
                onChange={(value: (typeof options)[number]) => {
                  form.setValue(name, value?.value)
                }}
                value={options.find((opt) => opt.value === field.value)}
                disabled={disabled}
                data-testid="custom-fields-form-monetary-currency-select"
                placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
              />
            </fieldset>
            <FormMessage />
          </>
        )}
      />
      <div className="w-full pt-3">
        <Separator orientation="horizontal" />
      </div>
      <fieldset className="flex justify-between items-center w-full pt-3">
        <Label>
          <Space direction="column" size="xs">
            {t('customFields:NUMERIC_MINMAX_SETTINGS_PLACEHOLDER_TITLE')}
            <Paragraph size="sm">{t('NUMERIC_MINMAX_SETTINGS_PLACEHOLDER_LABEL')}</Paragraph>
          </Space>
        </Label>

        <Switch
          id="custom-field-form-monetary-switch-min-max"
          data-testid="custom-field-form-monetary-switch-min-max"
          checked={!!minAndMax}
          onCheckedChange={(checked) => {
            setMinAndMax(checked)
          }}
          disabled={disabled}
        />
      </fieldset>
      {minAndMax && (
        <>
          <div className="w-full">
            <Space className="w-full">
              <div className="w-full">
                <FormField
                  name={nameMin}
                  control={form.control}
                  render={({ field, fieldState }) => {
                    return (
                      <div className="w-full">
                        <InputText
                          {...field}
                          type="number"
                          isError={!!fieldState.error?.message}
                          disabled={disabled}
                          data-testid="custom-fields-form-min-field"
                          placeholder={t('common:FORM_PLACEHOLDERS#MIN')}
                        />
                        <FormMessage messageParams={{ max: maxValue }} />
                      </div>
                    )
                  }}
                />
              </div>
              <div className="w-full">
                <FormField
                  name={nameMax}
                  control={form.control}
                  render={({ field, fieldState }) => {
                    return (
                      <div className="w-full">
                        <InputText
                          {...field}
                          type="number"
                          isError={!!fieldState.error?.message}
                          disabled={disabled}
                          data-testid="custom-fields-form-max-field"
                          placeholder={t('common:FORM_PLACEHOLDERS#MAX')}
                        />
                        <FormMessage messageParams={{ min: minValue }} />
                      </div>
                    )
                  }}
                />
              </div>
            </Space>
          </div>
        </>
      )}
    </>
  )
}
