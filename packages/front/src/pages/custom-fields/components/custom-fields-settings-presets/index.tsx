import React, { ReactNode } from 'react'
import { ShortTextSettingsPresets } from './short-text'
import { LongTextSettingsPresets } from './long-text'
import { NumericSettingsPresets } from './numeric'
import { MonetarySettingsPresets } from './monetary'
import { CustomFieldType } from '../../constants'
import { DraggableOptionsList } from './draggable-options-list'

type CustomFieldTypes = keyof typeof CustomFieldType

interface SettingsByTypeProps {
  type: CustomFieldTypes
  disabled?: boolean
}

export function SettingsByType({ type, disabled }: SettingsByTypeProps) {
  const presets = new Map<CustomFieldTypes, ReactNode>([
    [CustomFieldType.type, <ShortTextSettingsPresets disabled={disabled} />],
    [CustomFieldType['long-text'], <LongTextSettingsPresets disabled={disabled} />],
    [CustomFieldType.numeric, <NumericSettingsPresets disabled={disabled} />],
    [CustomFieldType.monetary, <MonetarySettingsPresets disabled={disabled} />],
    [CustomFieldType.list, <DraggableOptionsList disabled={disabled} />],
    [CustomFieldType.checkbox, <DraggableOptionsList disabled={disabled} />],
  ])

  return presets.get(type) || null
}
