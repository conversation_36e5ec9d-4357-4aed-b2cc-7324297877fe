import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd'
import { Button, Heading, InputText, Space } from '@ikatec/nebula-react'
import { GripHorizontalIcon, PlusIcon, TrashIcon } from 'lucide-react'
import React, { useEffect, useRef } from 'react'
import { useFieldArray, useFormContext } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { FormMessage } from '../../../../app/components/common/unconnected/ui/form'
import { SETTINGS_INPUT_PREFIX_ON_FORM } from '../../constants'

const formFieldName = SETTINGS_INPUT_PREFIX_ON_FORM + 'options'
export const EMPTY_OPTION = () => ({ id: `opt_${Date.now()}`, label: '' })

const DraggableOptionsList = ({ disabled }) => {
  const itShouldFocusNextInput = useRef(false)
  const { t } = useTranslation(['customFields', 'common'])
  const form = useFormContext()
  const { fields, append, remove, move } = useFieldArray({
    control: form.control,
    name: formFieldName,
    rules: { minLength: 1, required: true },
  })

  useEffect(() => {
    return () => {
      itShouldFocusNextInput.current = false
    }
  }, [])

  return (
    <>
      <div className="w-full mt-2">
        <Space size="lg" direction="column">
          <Heading level="5">{t('LIST_SETTINGS_OPTIONS_SECTION_TITLE')}</Heading>

          <DragDropContext
            onDragEnd={(result) => {
              move(result.source.index, result.destination.index)
            }}
          >
            <Droppable droppableId="000" isDropDisabled={disabled}>
              {({ droppableProps, innerRef: droppableInnerRef, placeholder }) => (
                <div ref={droppableInnerRef} {...droppableProps} className="w-full">
                  {fields.map((field: ReturnType<typeof EMPTY_OPTION>, index, arr) => {
                    const name = `${formFieldName}.${index}`
                    const errorMessage = form.getFieldState(name + '.label')?.error?.message
                    return (
                      <Draggable key={field.id} index={index} draggableId={field.id} isDragDisabled={disabled}>
                        {({ draggableProps, innerRef, dragHandleProps }) => (
                          <div className="last:mb-0 mb-2 ">
                            <div
                              {...draggableProps}
                              {...dragHandleProps}
                              ref={innerRef}
                              className="flex items-center gap-x-1"
                              data-testid={`custom-field-form-draggable-option-wrapper-${index}`}
                            >
                              <div className="w-full">
                                <InputText
                                  className="w-full"
                                  icon={
                                    !disabled && (
                                      <div
                                        className="h-10 min-w-10 flex items-center justify-center"
                                        data-testid={`custom-field-form-draggable-option-holder-${index}`}
                                      >
                                        <GripHorizontalIcon width={16} height={16} />
                                      </div>
                                    )
                                  }
                                  defaultValue={form.getValues(name)?.label}
                                  onChange={(e) => {
                                    form.setValue(name + '.label', e.target.value, {
                                      shouldDirty: true,
                                      shouldValidate: true,
                                    })
                                  }}
                                  disabled={disabled}
                                  isError={!!errorMessage}
                                  autoFocus={itShouldFocusNextInput.current}
                                  data-testid={`custom-field-form-draggable-option-input-${index}`}
                                />
                              </div>
                              {arr.length > 1 && !disabled && (
                                <Button
                                  variant="ghost"
                                  onClick={() => remove(index)}
                                  icon
                                  type="button"
                                  data-testid={`custom-field-form-draggable-option-remove-button-${index}`}
                                >
                                  <TrashIcon />
                                </Button>
                              )}
                            </div>
                            {!!errorMessage && <FormMessage>{t(errorMessage)}</FormMessage>}
                          </div>
                        )}
                      </Draggable>
                    )
                  })}
                  {placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
          {!disabled && (
            <Button
              variant="ghost"
              onClick={() => {
                itShouldFocusNextInput.current = true
                append(EMPTY_OPTION())
              }}
              size={'sm'}
              type="button"
              data-testid={`custom-field-form-option-add-button`}
            >
              <PlusIcon />
              {t('LIST_SETTINGS_OPTIONS_SECTION_ADD')}
            </Button>
          )}
        </Space>
      </div>
    </>
  )
}

export { DraggableOptionsList }
