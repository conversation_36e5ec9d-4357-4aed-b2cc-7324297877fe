import { Box, Button, InputText, Label, Paragraph, Select, Separator, Space, Switch } from '@ikatec/nebula-react'
import React, { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { components as reactSelectComponents } from 'react-select'

import { CheckIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { Form, FormControl, FormField, FormItem, FormMessage } from '../../../app/components/common/unconnected/ui/form'
import { cn } from '../../../app/components/common/unconnected/ui/utils'
import {
  CustomFieldType,
  CustomFieldTypeToTranslateKey,
  DEFAULT_TYPES_OPTIONS,
  DEFAULT_TYPES_OPTIONS_FOR_EDITING,
} from '../constants'
import { SettingsByType } from './custom-fields-settings-presets'
import z from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { EMPTY_OPTION } from './custom-fields-settings-presets/draggable-options-list'

const schema = z
  .object({
    name: z
      .string({ message: 'common:REQUIRED_FIELD' })
      .nonempty({ message: 'common:REQUIRED_FIELD' })
      .max(255, 'common:INPUT_FORM_MESSAGES#MAX_SIZE')
      .refine((name) => name.trim().length > 0, { message: 'common:REQUIRED_FIELD' }),
    allowed: z.string({ message: 'common:REQUIRED_FIELD' }).nonempty({ message: 'common:REQUIRED_FIELD' }),
    type: z.string({ message: 'common:REQUIRED_FIELD' }).nonempty({ message: 'common:REQUIRED_FIELD' }),
    showOnRegister: z.boolean().default(false),
    required: z.boolean().default(false),
    settings: z.object({
      options: z
        .array(
          z.object({
            label: z.string().nullish(),
            id: z.string().nullish(),
          }),
        )
        .min(0)
        .default([]),
      placeholder: z.string().nullish().default(''),
      currency: z.string().nullish().default(''),
      maxLength: z.number().nullish(),
      min: z
        .string()
        .default('')
        .transform((v) => String(v ?? ''))
        .nullish(),
      max: z
        .string()
        .default('')
        .transform((v) => String(v ?? ''))
        .nullish(),
    }),
  })
  .superRefine((data, ctx) => {
    console.log(data)
    const { max, min } = data?.settings ?? {}
    if ([CustomFieldType.numeric, CustomFieldType.monetary].includes(data.type as CustomFieldType)) {
      if (min?.length && max?.length) {
        if (+min >= +max) {
          ctx.addIssue({
            path: ['settings.max'],
            code: z.ZodIssueCode.custom,
            message: 'common:INPUT_FORM_MESSAGES#GREATER_THAN',
          })

          ctx.addIssue({
            path: ['settings.min'],
            code: z.ZodIssueCode.custom,
            message: 'common:INPUT_FORM_MESSAGES#LESS_THAN',
          })
        }
      }
    }

    if ([CustomFieldType.list, CustomFieldType.checkbox].includes(data.type as CustomFieldType)) {
      const options = data?.settings?.options ?? []
      const indexMissingLabel = options.reduce((prev, curr, index) => {
        if (!curr.label) {
          prev.push(index)
        }

        return prev
      }, [] as number[])

      for (const optionIndex of indexMissingLabel) {
        ctx.addIssue({
          path: [`settings.options.${optionIndex}.label`],
          code: z.ZodIssueCode.custom,
          message: 'common:REQUIRED_FIELD',
        })
      }
    }
  })

export interface CustomFieldValues extends z.infer<typeof schema> {}

interface CustomFieldFormProps {
  onCancel: VoidFunction
  onSubmit: (values: CustomFieldValues) => void
  defaultValues?: Partial<CustomFieldValues>
  isPending?: boolean
  isEdit?: boolean
  isView?: boolean
}

type CustomFieldFormRef = { isDirty: boolean }

const CustomFieldForm = forwardRef<CustomFieldFormRef, CustomFieldFormProps>(
  ({ isView = false, isEdit = false, onCancel, onSubmit, defaultValues, isPending }, ref) => {
    const { t } = useTranslation(['customFields', 'common'])
    const form = useForm<CustomFieldValues>({
      defaultValues: {
        ...defaultValues,
        allowed: null,
        name: '',
        required: false,
        settings: {},
        showOnRegister: false,
        type: null,
      },
      resetOptions: {
        keepDefaultValues: true,
      },
      resolver: zodResolver(schema),
      criteriaMode: 'all',
      mode: 'all',
      reValidateMode: 'onChange',
    })

    const availableCustomFields = !isEdit ? DEFAULT_TYPES_OPTIONS : DEFAULT_TYPES_OPTIONS_FOR_EDITING
    const types = useMemo<{ label: string; description: string; value: string }[]>(
      () =>
        Object.values(availableCustomFields).map((value: any) => ({
          ...value,
          label: t(CustomFieldTypeToTranslateKey[value.value]?.label),
          description: t(CustomFieldTypeToTranslateKey[value.value]?.description),
        })),
      [t],
    )
    const allowedOptions = useMemo(() => [{ label: t('OPTION_SELECT_CONTACTS'), value: 'contacts' }], [t])

    const showOnRegister = form.watch('showOnRegister')
    const type = form.watch('type')
    const currency = form.watch('settings.currency')

    console.log(currency)
    const handleSubmit = onSubmit

    useEffect(() => {
      if (isEdit || isView) return
      form.setValue(
        'settings',
        {
          placeholder: '',
          options: [EMPTY_OPTION()],
        },
        { shouldTouch: true },
      )
    }, [type, isEdit, isView])

    useEffect(() => {
      if (!defaultValues) return
      form.reset({
        ...defaultValues,
        settings: {
          ...defaultValues.settings,
          min: String(defaultValues.settings.min),
          max: String(defaultValues.settings.max),
        },
      })
    }, [defaultValues])

    useImperativeHandle(ref, () => ({ isDirty: form.formState.isDirty }))

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} autoComplete="off" className="grid gap-6">
          <Box border>
            <Space direction="column" size="md">
              <div className="w-full">
                <FormField
                  name="name"
                  control={form.control}
                  render={({ field, fieldState }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <fieldset>
                            <Label htmlFor="custom-fields-form-input-name">
                              {t('customFields:COLUMN_CUSTOM_FIELD_NAME')}
                            </Label>
                            <InputText
                              {...field}
                              isError={!!fieldState.error?.message}
                              id="custom-fields-form-input-name"
                              data-testid="custom-fields-form-input-name"
                              disabled={isView}
                            />
                          </fieldset>
                        </FormControl>
                        <FormMessage messageParams={{ size: 256 }} />
                      </FormItem>
                    )
                  }}
                />
              </div>

              <div className="w-full">
                <FormField
                  name="allowed"
                  control={form.control}
                  render={({ field, fieldState }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <fieldset>
                            <Label htmlFor="custom-field-form-select-allowed">
                              {t('customFields:COLUMN_CUSTOM_FIELD_ENTITYS')}
                            </Label>
                            <Select
                              {...field}
                              isError={!!fieldState.error?.message}
                              id="custom-field-form-select-allowed"
                              data-testid="custom-field-form-select-allowed"
                              options={allowedOptions}
                              isClearable
                              placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                              onChange={(value: { value: string }) => {
                                form.setValue('allowed', value?.value, {
                                  shouldDirty: true,
                                  shouldTouch: true,
                                  shouldValidate: true,
                                })
                              }}
                              value={allowedOptions.find((opt) => opt.value === field.value)}
                              disabled={isView}
                            />
                          </fieldset>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </div>

              <div className="w-full">
                <FormField
                  name="type"
                  control={form.control}
                  render={({ field, fieldState }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <fieldset>
                            <Label htmlFor="custom-field-form-select-type">
                              {t('customFields:COLUMN_CUSTOM_FIELD_TYPE')}
                            </Label>
                            <Select
                              {...field}
                              isError={!!fieldState.error?.message}
                              id="custom-field-form-select-type"
                              data-testid="custom-field-form-select-type"
                              options={types}
                              onChange={(value: (typeof types)[number]) => {
                                form.setValue('type', value?.value, {
                                  shouldDirty: true,
                                  shouldTouch: true,
                                  shouldValidate: true,
                                })
                              }}
                              isClearable
                              placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                              components={{
                                Option(props) {
                                  const { description, label } = props.data as (typeof types)[number]
                                  return (
                                    <reactSelectComponents.Option {...props}>
                                      <div
                                        className={cn(
                                          'flex items-center px-4 py-2 gap-2 cursor-pointer text-sm hover:text-inputSelect-focus-text hover:bg-listItem-background-hover bg-listItem-background-default text-inputSelect-default-text',
                                          {
                                            'text-inputSelect-focus-text font-semibold bg-listItem-background-hover':
                                              props.isSelected,
                                          },
                                        )}
                                      >
                                        <div>
                                          <span className="flex items-center gap-x-2">
                                            {props.isSelected && <CheckIcon size={'16px'} />}
                                            {label}
                                          </span>
                                          <span className={cn('text-xs', { 'ps-6': props.isSelected })}>
                                            {description}
                                          </span>
                                        </div>
                                      </div>
                                    </reactSelectComponents.Option>
                                  )
                                },
                              }}
                              value={types.find((opt) => opt.value === field.value)}
                              disabled={isEdit || isView}
                            />
                          </fieldset>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )
                  }}
                />
              </div>

              <SettingsByType type={type as CustomFieldType} disabled={isView} />

              <div className="w-full pt-3">
                <Separator orientation="horizontal" />
              </div>

              <div className="w-full py-3">
                <FormField
                  name="showOnRegister"
                  control={form.control}
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormControl>
                          <fieldset>
                            <div className="flex justify-between items-center">
                              <Label>
                                <Space direction="column" size="xs">
                                  {t('customFields:SHOW_ON_REGISTER_FIELD')}
                                  <Paragraph size="sm">{t('SHOW_ON_REGISTER_FIELD_DESCRIPTION')}</Paragraph>
                                </Space>
                              </Label>
                              <Switch
                                id="custom-field-form-switch-showOnRegister"
                                data-testid="custom-field-form-switch-showOnRegister"
                                onCheckedChange={(checked) => form.setValue('showOnRegister', checked)}
                                checked={field.value}
                                disabled={isView}
                              />
                            </div>
                          </fieldset>
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />
              </div>

              {!!showOnRegister && (
                <>
                  <Separator orientation="horizontal" />
                  <div className="w-full py-3">
                    <FormField
                      name="required"
                      control={form.control}
                      render={({ field }) => {
                        return (
                          <FormItem>
                            <FormControl>
                              <fieldset>
                                <div className="flex justify-between items-center">
                                  <Label>
                                    <Space direction="column" size="xs">
                                      {t('customFields:REQUIRED_FIELD')}
                                      <Paragraph size="sm">{t('REQUIRED_FIELD_DESCRIPTION')}</Paragraph>
                                    </Space>
                                  </Label>
                                  <Switch
                                    id="custom-field-form-switch-required"
                                    data-testid="custom-field-form-switch-required"
                                    onCheckedChange={(checked) => form.setValue('required', checked)}
                                    checked={field.value}
                                    disabled={isView}
                                  />
                                </div>
                              </fieldset>
                            </FormControl>
                          </FormItem>
                        )
                      }}
                    />
                  </div>
                </>
              )}
            </Space>
          </Box>

          {!isView && (
            <Space className="w-full">
              <Button
                variant="secondary"
                type="button"
                onClick={onCancel}
                data-testid="custom-fields-form-button-cancel"
              >
                {t('common:FORM_ACTION_CANCEL')}
              </Button>

              <Button
                variant="primary"
                type="submit"
                data-testid="custom-fields-form-button-submit"
                disabled={isPending || !form.formState.isValid}
              >
                {isPending ? <LoaderIcon className="animate-spin" /> : null}
                {t('common:FORM_ACTION_SAVE')}
              </Button>
            </Space>
          )}
        </form>
      </Form>
    )
  },
)

export { CustomFieldForm }
