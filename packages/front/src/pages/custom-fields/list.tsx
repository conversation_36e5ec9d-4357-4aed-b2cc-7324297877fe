import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Heading,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tag,
} from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { EllipsisVerticalIcon, EyeIcon, PencilIcon, PlusIcon, Trash2Icon } from 'lucide-react'
import React, { memo } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import customFieldsApi, { CustomFieldsQueryKey, type CustomFieldsFilters } from '../../api/custom-fields'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { useSearchParams } from '../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { debounceFn } from '../../utils/debouce-fn'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { CustomFieldTypeToTranslateKey } from './constants'

const ListCustomFields = () => {
  const history = useHistory()

  const { params, setValues, set, hasFilter, clear, searchParams, remove } = useSearchParams<CustomFieldsFilters>({
    order: 'ASC',
    page: 1,
    sort: 'name',
  })
  const { t } = useTranslation(['customFields', 'common'])

  const queryClient = useQueryClient()

  const {
    data: customFields,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [CustomFieldsQueryKey, 'list', params],
    queryFn: () => customFieldsApi.getAll({ ...params }),
    placeholderData() {
      return getPlaceholderData(
        queryClient.getQueriesData<typeof customFields>({ queryKey: [CustomFieldsQueryKey, 'list'] }),
      )
    },
  })

  const { currentPage, data = [], limit, total } = customFields ?? {}

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/custom-fields/create', search: searchParams }))
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/custom-fields/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/custom-fields/${id}/edit`, search: searchParams }))
  }

  const navigateToDelete = (id: string) => {
    setTimeout(() => history.push({ pathname: `/custom-fields/${id}/delete`, search: searchParams }))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'order', 'sort', 'name'])
  }

  const handleSetInputFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      remove(['page', 'order', 'sort'])
      set('name', value)
    })
  }

  const translationTypesEntitys = {
    type: t('LABEL_TYPE'),
    ...Object.entries(CustomFieldTypeToTranslateKey).reduce((prev, acc) => {
      const [key, value] = acc
      prev[key] = t(value.label)
      return prev
    }, {}),
    contacts: t('OPTION_SELECT_CONTACTS'),
  }

  return (
    <>
      <Helmet title={t('TITLE_CUSTOM_FIELDS')} />

      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="custom-fields-list-heading">
              {t('TITLE_CUSTOM_FIELDS')}
            </Heading>

            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="custom-fields-list-input-filter"
                name="label"
                defaultValue={params.name}
                onChange={(e) => handleSetInputFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />

              <IfUserCan permission="customFields.create">
                <Button size="md" data-testid="custom-fields-list-button-create" onClick={navigateToCreate}>
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>

        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundWithoutFilter
              onAdd={navigateToCreate}
              moduleTitle={t('customFields:NO_CUSTOM_FIELD_CREATED')}
              moduleDescription={t('customFields:START_CREATE_FIRST_CUSTOM_FIELD')}
            />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && data?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead data-testid="custom-fields-th-name">
                    <Space size="md" className="justify-start items-center">
                      {t('COLUMN_CUSTOM_FIELD_NAME')}
                      <SortTableButton
                        sort="name"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                        data-testid="custom-fields-list-sort-button-name"
                      />
                    </Space>
                  </TableHead>
                  <TableHead data-testid="custom-fields-th-type">
                    <Space size="md" className="justify-start items-center">
                      {t('COLUMN_CUSTOM_FIELD_TYPE')}
                      <SortTableButton
                        sort="type"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                        data-testid="custom-fields-list-sort-button-type"
                      />
                    </Space>
                  </TableHead>
                  <TableHead>{t('COLUMN_CUSTOM_FIELD_ENTITY')}</TableHead>
                  <TableHead className="w-60" data-testid="custom-fields-th-showOnRegister">
                    <Space size="md" className="justify-start items-center">
                      {t('COLUMN_CUSTOM_FIELD_SHOW_ON_REGISTER')}
                      <SortTableButton
                        sort="showOnRegister"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-60" data-testid="custom-fields-th-required">
                    <Space size="md" className="justify-start items-center">
                      {t('COLUMN_CUSTOM_FIELD_REQUIRED')}
                      <SortTableButton
                        sort="required"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                        data-testid="custom-fields-list-sort-button-required"
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[72px]" data-testid="custom-fields-label-actions"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((customField, index) => {
                  return (
                    <TableRow key={customField.id}>
                      <TableCell>
                        <div className="max-w-[350px] text-ellipsis overflow-hidden">{customField.name}</div>
                      </TableCell>
                      <TableCell>{translationTypesEntitys[customField.type]}</TableCell>
                      <TableCell>{translationTypesEntitys[customField.allowed]}</TableCell>
                      <TableCell>
                        <Tag color={!customField.showOnRegister ? 'gray' : 'green'} className="w-max">
                          {customField.showOnRegister ? t('common:YES_LABEL') : t('common:NO_LABEL')}
                        </Tag>
                      </TableCell>
                      <TableCell>
                        <Tag color={!customField.required ? 'gray' : 'green'} className="w-max">
                          {customField.required ? t('common:YES_LABEL') : t('common:NO_LABEL')}
                        </Tag>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              icon
                              variant="ghost"
                              size="sm"
                              data-testid={`custom-fields-list-button-actions-${index}`}
                            >
                              <EllipsisVerticalIcon />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              data-testid={`custom-fields-list-button-actions-${index}-view`}
                              onClick={() => navigateToView(customField.id)}
                            >
                              <EyeIcon />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </DropdownMenuItem>

                            <IfUserCan permission="customFields.update">
                              <DropdownMenuItem
                                data-testid={`custom-fields-list-button-actions-${index}-edit`}
                                onClick={() => navigateToEdit(customField.id)}
                              >
                                <PencilIcon />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </DropdownMenuItem>
                            </IfUserCan>
                            <IfUserCan permission="customFields.destroy">
                              <DropdownMenuItem
                                onClick={() => navigateToDelete(customField.id)}
                                data-testid={`custom-fields-list-button-actions-${index}-delete`}
                              >
                                <Trash2Icon />
                                {t('common:LABEL_DELETE')}
                              </DropdownMenuItem>
                            </IfUserCan>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}

          {/* if has data and isSuccessful, render pagination */}
          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>
    </>
  )
}

const memoizedListCustomFields = memo(ListCustomFields, () => true)

export { memoizedListCustomFields as ListCustomFields }
