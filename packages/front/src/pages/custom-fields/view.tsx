import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>crumbList,
  <PERSON>readcrumbPage,
  BreadcrumbSeparator,
  Button,
  Heading,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError, HttpStatusCode } from 'axios'
import { ChevronLeftIcon } from 'lucide-react'
import React, { useCallback, useEffect, useRef } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import { Link } from 'react-router-dom'
import customFieldsApi, { CustomFieldsData, CustomFieldsQueryKey } from '../../api/custom-fields'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import useToggle from '../../app/hooks/useToggle'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { FormPageSkeleton } from '../../components/unconnected/skeletons/form-page-skeleton'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { CustomFieldForm, CustomFieldValues } from './components/form'
import { LightningLearningBox } from './components/lightning-learning-box'
import { CustomFieldType } from './constants'

const ViewCustomFields = () => {
  const { t } = useTranslation(['customFields', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const formRef = useRef(null)

  const { data, isError, isFetching } = useQuery({
    queryKey: [CustomFieldsQueryKey, id],
    queryFn: () => customFieldsApi.getById(id),
    enabled: Boolean(id),
  })

  const queryClient = useQueryClient()
  const parseCustomFieldData = (values: CustomFieldValues): CustomFieldsData => {
    return {
      allowed: values.allowed,
      name: values.name,
      required: values.required,
      settings: values.settings,
      showOnRegister: values.showOnRegister,
      type: values.type as CustomFieldType,
    }
  }
  const { mutateAsync, isPending } = useMutation({
    mutationFn: async (values: CustomFieldValues) => customFieldsApi.update(id, parseCustomFieldData(values)),
    onSuccess: () => {
      toast.success(t('CUSTOM_FIELD_ADD_SUCCESS'))

      queryClient.invalidateQueries({
        queryKey: [CustomFieldsQueryKey],
      })
      queryClient.invalidateQueries({
        queryKey: [CustomFieldsQueryKey, id],
      })

      history.replace('/custom-fields')
    },
    onError: (error: AxiosError<{ message: string }>) => {
      const errorMap = {
        [HttpStatusCode.Conflict]: t('MODAL_DUPLICATE_NAME'),
      }
      toast.error(errorMap[error.response.status] ?? t('MODAL_MESSAGE_ERROR'))
    },
  })

  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const handleGoBack = useCallback(() => {
    if (formRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/custom-fields')
  }, [formRef])

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/custom-fields')
    }
  }, [isError])

  if (isFetching) {
    return <FormPageSkeleton />
  }

  return (
    <Container>
      <Helmet title={`${t('common:ACTIONS_SUBMENU_VIEW')} ${t('TITLE_CUSTOM_FIELDS_SINGULAR')}`} />
      <ContainerHeader mtSize="sm">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <Link to="/custom-fields">{t('TITLE_CUSTOM_FIELDS_SINGULAR')}</Link>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                {t('common:ACTIONS_SUBMENU_VIEW')} {t('TITLE_CUSTOM_FIELDS_SINGULAR').toLowerCase()}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div className="flex items-center justify-between">
          <Space className="w-full items-center" size="sm">
            <Button
              icon
              variant="ghost"
              onClick={handleGoBack}
              aria-label="Go back to users' list"
              data-testid="custom-fields-view-button-go-back"
              role="link"
            >
              <ChevronLeftIcon />
            </Button>

            <Heading level="1" data-testid="custom-fields-view-heading">
              {t('common:ACTIONS_SUBMENU_VIEW')} {t('TITLE_CUSTOM_FIELDS_SINGULAR').toLowerCase()}
            </Heading>
          </Space>

          <IfUserCan permission="customFields.update">
            <Button
              variant="secondary"
              onClick={() => history.push(`/custom-fields/${id}/edit`)}
              aria-label="Go to edit roles' page"
              data-testid="roles-view-button-edit"
              role="link"
            >
              {t('common:ACTIONS_SUBMENU_EDIT')}
            </Button>
          </IfUserCan>
        </div>
      </ContainerHeader>
      <ContainerContent>
        <Space className="gap-6">
          <div className="flex-1">
            <CustomFieldForm
              ref={formRef}
              onSubmit={(values) => mutateAsync(values)}
              onCancel={handleGoBack}
              isPending={isPending}
              defaultValues={data}
              isView
            />
          </div>
          <aside className="hidden md-block">
            <LightningLearningBox />
          </aside>
        </Space>
      </ContainerContent>

      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={history.goBack}
      />
    </Container>
  )
}

export { ViewCustomFields }
