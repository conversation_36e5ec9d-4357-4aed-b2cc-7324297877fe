import React from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'
import customFieldsApi, { CustomFieldsQueryKey } from '../../api/custom-fields'

const DeleteCustomFields = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['customFields', 'common'])
  const history = useHistory()

  const { isFetching, isError, isSuccess } = useQuery({
    queryKey: [customFieldsApi, id],
    queryFn: () => customFieldsApi.getById(id),
    enabled: !!id,
  })

  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      await customFieldsApi.delete(id)
    },
    onSuccess: async () => {
      toast.success(t('CUSTOM_FIELD_DELETED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [CustomFieldsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [CustomFieldsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])
    },
    onError: () => {
      toast.error(t('CUSTOM_FIELD_DELETION_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('CUSTOM_FIELD_NOT_FOUND'))
      return history.replace('/custom-fields')
    }
  }, [isError])

  return (
    <AlertDialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/custom-fields')
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />
            <span className="lowercase first-letter:uppercase">
              {t('common:LABEL_DELETE')} {t('TITLE_CUSTOM_FIELDS_SINGULAR')}
            </span>
          </AlertDialogTitle>

          {isFetching && (
            <>
              <Skeleton width="80%" height="24px" />
              <Skeleton width="50%" height="24px" />
            </>
          )}

          {isSuccess && (
            <>
              <AlertDialogDescription>{t('MODAL_DELETE_CUSTOM_FIELD_WARNING')}</AlertDialogDescription>
              <AlertDialogDescription>{t('MODAL_DELETE_CUSTOM_FIELD_CONFIRM_QUESTION')}</AlertDialogDescription>
            </>
          )}
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel className="w-full" data-testid="custom-fields-delete-button-cancel">
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>

          <AlertDialogAction
            variant="danger"
            className="w-full"
            onClick={() => mutate()}
            disabled={isPending || isFetching}
            data-testid="custom-fields-delete-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { DeleteCustomFields }
