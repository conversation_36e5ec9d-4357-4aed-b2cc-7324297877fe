import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { useHistory, useParams } from 'react-router'
import organizationsApi, { organizationsQueryKey } from '../../api/organizations'
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
  Button,
  Space,
  toast,
  Paragraph,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'

const ViewOrganizations = () => {
  const { t } = useTranslation(['organizationPage', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [organizationsQueryKey, id],
    queryFn: () => organizationsApi.getById(id),
    enabled: <PERSON>olean(id),
  })

  useEffect(() => {
    if (isError) {
      toast.info(t('ORGANIZATION_NOT_FOUND'))
      return history.replace('/organizations')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.goBack()
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('VIEW_ORGANIZATION')}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          {isFetching && (
            <Space size="md">
              <div className="flex items-center gap-1 flex-1">
                <Paragraph size="sm" className="font-semibold">
                  {t('common:LABEL_NAME')}:
                </Paragraph>
                <p>{<Skeleton width="180px" height="24px" />}</p>
              </div>
            </Space>
          )}
          {isSuccess && !isFetching && (
            <Space size="md">
              <div className="flex items-baseline gap-1 flex-1">
                <Paragraph size="sm" className="font-semibold">
                  {t('common:LABEL_NAME')}:
                </Paragraph>
                <Paragraph size="sm" className="max-w-64 break-words">
                  {data.name}
                </Paragraph>
              </div>
            </Space>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="organizations-view-button-close">
              {t('common:FORM_ACTION_CLOSE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { ViewOrganizations }
