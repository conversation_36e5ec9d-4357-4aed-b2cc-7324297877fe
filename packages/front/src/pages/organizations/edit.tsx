import React from 'react'
import { <PERSON><PERSON>, DialogBody, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useEffect } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import organizationsApi, { type OrganizationsData, organizationsQueryKey } from '../../api/organizations'
import { OrganizationsForm } from './components/form'

const EditOrganizations = () => {
  const { t } = useTranslation(['organizationPage', 'common'])
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching } = useQuery({
    queryKey: [organizationsQueryKey, id],
    queryFn: () => organizationsApi.getById(id),
    enabled: <PERSON><PERSON>an(id),
  })
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: OrganizationsData) => organizationsApi.update(id, values),
    onSuccess: async () => {
      toast.success(t('ORGANIZATION_EDITED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [organizationsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [organizationsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])

      history.goBack()
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { message } = error?.response?.data ?? {}
      const errorMap = {
        'There already exists a resource with same fields name,accountId.': () => t('ORGANIZATION_SAME_NAME_ERROR'),
      }
      toast.success(errorMap[message]?.() || t('ORGANIZATION_EDITED_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.success(t('ORGANIZATION_NOT_FOUND'))
      return history.replace('/organizations')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${t('common:LABEL_EDITING')} ${t('ORGANIZATION').toLocaleLowerCase()} - ${data?.name}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.goBack()
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('EDIT_ORGANIZATION')}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <OrganizationsForm
              onCancel={() => history.goBack()}
              onSubmit={(values) => mutateAsync(values)}
              isPending={isPending}
              isFetching={isFetching}
              defaultValues={data}
            />
          </DialogBody>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { EditOrganizations }
