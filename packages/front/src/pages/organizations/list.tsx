import React from 'react'
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Heading,
} from '@ikatec/nebula-react'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import { useTranslation } from 'react-i18next'
import { Helmet } from 'react-helmet'
import { useHistory } from 'react-router'
import { useSearchParams } from '../../hooks/use-search-params'
import { EllipsisVerticalIcon, EyeIcon, PencilIcon, PlusIcon, Trash2Icon } from 'lucide-react'
import { debounceFn } from '../../utils/debouce-fn'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import organizationsApi, { OrganizationsFilters, organizationsQueryKey } from '../../api/organizations'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import { truncateText } from '../../app/utils/truncate-text'

const ListOrganizations = () => {
  const { t } = useTranslation(['organizationPage', 'common'])
  const history = useHistory()
  const queryClient = useQueryClient()

  const { params, setValues, set, hasFilter, clear, searchParams, remove } = useSearchParams<OrganizationsFilters>({
    order: 'ASC',
    sort: 'name',
    page: 1,
    name: '',
  })

  const {
    data: organizations,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [organizationsQueryKey, 'list', params],
    queryFn: () => organizationsApi.getAll({ ...params }),
    placeholderData() {
      return getPlaceholderData(
        queryClient.getQueriesData<typeof organizations>({ queryKey: [organizationsQueryKey, 'list'] }),
      )
    },
  })

  const { currentPage, data = [], limit, total } = organizations ?? {}

  const handleClearInputFilter = () => {
    remove(['page', 'order', 'sort', 'name'])
  }

  const handleSetInputFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      remove(['page', 'order', 'sort'])
      set('name', value)
    })
  }

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/organizations/create', search: searchParams }))
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/organizations/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/organizations/${id}/edit`, search: searchParams }))
  }

  const navigateToDelete = (id: string) => {
    setTimeout(() => history.push({ pathname: `/organizations/${id}/delete`, search: searchParams }))
  }

  return (
    <Container>
      <Helmet title={t('ORGANIZATIONS')} />
      <ContainerHeader className="flex justify-between items-center">
        <Heading level="1" data-testid="organizations-list-heading">
          {t('ORGANIZATIONS')}
        </Heading>

        <Space direction="row" size="lg" className="items-center">
          <InputFilter
            data-testid="organizations-list-input-filter"
            name="name"
            defaultValue={params.name}
            onChange={(e) => handleSetInputFilter(e.target.value)}
            onClean={handleClearInputFilter}
          />
          <IfUserCan permission="organizations.create">
            <Button size="md" data-testid="organizations-list-button-create" onClick={navigateToCreate}>
              <PlusIcon />
              {t('common:LIST_ADD_NEW')}
            </Button>
          </IfUserCan>
        </Space>
      </ContainerHeader>
      <ContainerContent>
        {/* if is fetching another page or is initial fetch */}
        {isFetching && <TableSkeleton />}

        {/* If not isFetching and isError, render error feedback */}
        {isError && <NoPossibleLoadData onRefresh={refetch} />}

        {/* If no data and is successful with filters, render empty state */}
        {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
          <NoResultsFoundByFilter onClearFilter={clear} />
        )}

        {/* If no data and is successful without filters */}
        {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
          <NoResultsFoundWithoutFilter
            onAdd={navigateToCreate}
            moduleTitle={t('organizationPage:NO_ORGANIZATIONS_CREATED')}
            moduleDescription={t('organizationPage:START_CREATE_FIRST_ORGANIZATION')}
          />
        )}

        {/* If has data and is successful, render table */}
        {isSuccess && data?.length > 0 && !isFetching && (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <Space size="md" className="justify-start items-center">
                    {t('common:LABEL_NAME')}
                    <SortTableButton
                      data-testid="organizations-list-button-sort-name"
                      sort="name"
                      order={params.order}
                      currentSort={params.sort}
                      onChange={(head, order) => {
                        setValues({
                          ...params,
                          order,
                          sort: head as 'name',
                        })
                      }}
                    />
                  </Space>
                </TableHead>
                <TableHead className="w-[72px]"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((organization, index) => (
                <TableRow key={organization.id}>
                  <TableCell>{truncateText(organization.name, 80)}</TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          icon
                          variant="ghost"
                          size="sm"
                          data-testid={`organizations-list-button-actions-${index}`}
                        >
                          <EllipsisVerticalIcon />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem
                          data-testid={`organizations-list-button-actions-${index}-view`}
                          onClick={() => navigateToView(organization.id)}
                        >
                          <EyeIcon />
                          {t('common:ACTIONS_SUBMENU_VIEW')}
                        </DropdownMenuItem>

                        <IfUserCan permission="organizations.update">
                          <DropdownMenuItem
                            data-testid={`organizations-list-button-actions-${index}-edit`}
                            onClick={() => navigateToEdit(organization.id)}
                          >
                            <PencilIcon />
                            {t('common:ACTIONS_SUBMENU_EDIT')}
                          </DropdownMenuItem>
                        </IfUserCan>
                        <IfUserCan permission="organizations.destroy">
                          <DropdownMenuItem
                            data-testid={`organizations-list-button-actions-${index}-delete`}
                            onClick={() => navigateToDelete(organization.id)}
                          >
                            <Trash2Icon />
                            {t('common:LABEL_DELETE')}
                          </DropdownMenuItem>
                        </IfUserCan>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* if has data and isSuccessful, render pagination */}
        {Boolean(total && isSuccess) && (
          <Pagination
            page={currentPage}
            pageSize={limit}
            total={total}
            onChangePage={(page) => set('page', page.toString())}
          />
        )}
      </ContainerContent>
    </Container>
  )
}

export { ListOrganizations }
