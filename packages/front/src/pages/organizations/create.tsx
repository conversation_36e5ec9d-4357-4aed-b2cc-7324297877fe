import React from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ody, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import organizationsApi, { type OrganizationsData, organizationsQueryKey } from '../../api/organizations'
import { OrganizationsForm } from './components/form'

const CreateOrganizations = () => {
  const { t } = useTranslation(['organizationPage', 'common'])
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: OrganizationsData) => organizationsApi.create(values),
    onSuccess: () => {
      toast.success(t('ORGANIZATION_ADDED_SUCCESS'))

      queryClient.invalidateQueries({
        queryKey: [organizationsQueryKey, 'list'],
      })

      history.goBack()
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { message } = error?.response?.data ?? {}
      const errorMap = {
        'There already exists a resource with same fields name,accountId.': () => t('ORGANIZATION_SAME_NAME_ERROR'),
      }
      toast.error(errorMap[message]?.() || t('ORGANIZATION_ADDED_ERROR'))
    },
  })

  return (
    <>
      <Helmet title={`${t('common:LABEL_CREATING')} ${t('ORGANIZATION').toLowerCase()}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.goBack()
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('ADD_ORGANIZATION')}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <OrganizationsForm
              onCancel={() => history.goBack()}
              onSubmit={(values) => mutateAsync(values)}
              isPending={isPending}
            />
          </DialogBody>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { CreateOrganizations }
