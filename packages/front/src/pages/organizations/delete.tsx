import React from 'react'
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogCancel,
  AlertDialogAction,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import organizationsApi, { organizationsQueryKey } from '../../api/organizations'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'
import { Helmet } from 'react-helmet'

const DeleteOrganizations = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['organizationPage', 'common'])
  const history = useHistory()

  const { data, isFetching, isError, isSuccess } = useQuery({
    queryKey: [organizationsQueryKey, id],
    queryFn: () => organizationsApi.getById(id),
    enabled: !!id,
  })

  const queryClient = useQueryClient()

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      await organizationsApi.delete(id)
    },
    onSuccess: async () => {
      toast.success(t('ORGANIZATION_DELETED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [organizationsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [organizationsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])
    },
    onError: () => {
      toast.error(t('ORGANIZATION_DELETION_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.info(t('ORGANIZATION_NOT_FOUND'))
      return history.replace('/organizations')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${t('common:LABEL_DELETING')} ${t('ORGANIZATION').toLocaleLowerCase()} - ${data?.name}`} />

      <AlertDialog
        open
        onOpenChange={(open) => {
          if (!open) history.goBack()
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />
              {t('organizationPage:DELETE_ORGANIZATION')}
            </AlertDialogTitle>

            {isFetching && (
              <>
                <Skeleton width="80%" height="24px" />
                <Skeleton width="50%" height="24px" />
              </>
            )}

            {isSuccess && (
              <>
                <AlertDialogDescription>{t('common:ARE_YOU_SURE_TO_CONTINUE')}</AlertDialogDescription>
                <AlertDialogDescription>{t('common:IRREVERSIBLE_ACTION_LABEL')}</AlertDialogDescription>
              </>
            )}
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel className="w-full" data-testid="organizations-delete-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </AlertDialogCancel>

            <AlertDialogAction
              variant="danger"
              className="w-full"
              onClick={() => mutate()}
              disabled={isPending || isFetching}
              data-testid="organizations-delete-button-confirm"
            >
              {isPending ? <LoaderIcon className="animate-spin" /> : null}
              {t('common:BUTTON_TEXT_CONFIRM')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export { DeleteOrganizations }
