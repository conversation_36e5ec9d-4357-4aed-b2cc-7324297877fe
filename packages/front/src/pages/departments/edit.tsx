import React from 'react'
import { <PERSON><PERSON>, DialogBody, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useEffect } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import departmentsApi, { type DepartmentsData, departmentsQueryKey } from '../../api/departments'
import { DepartmentsForm } from './components/form'

const EditDepartments = () => {
  const { t } = useTranslation(['department', 'common'])
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching } = useQuery({
    queryKey: [departmentsQueryKey, id],
    queryFn: () => departmentsApi.getById(id),
    enabled: <PERSON><PERSON><PERSON>(id),
  })
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: DepartmentsData) => departmentsApi.update(id, values),
    onSuccess: async () => {
      toast.success(t('DEPARTMENT_EDITED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [departmentsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [departmentsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])

      history.replace('/departments')
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { message } = error?.response?.data ?? {}
      const errorMap = {
        'There already exists a resource with same fields name,accountId.': () => t('DEPARTMENT_EDIT_SAME_NAME_ERROR'),
      }
      toast.success(errorMap[message]?.() || t('DEPARTMENT_CREATED_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.success(t('DEPARTMENT_NOT_FOUND'))
      return history.replace('/departments')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${t('common:LABEL_EDITING')} ${t('department:TITLE_DEPARTMENT')} - ${data?.name}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.replace('/departments')
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('department:EDIT_DEPARTMENT_TITLE')}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <DepartmentsForm
              onCancel={() => history.replace('/departments')}
              onSubmit={(values) => mutateAsync(values)}
              isPending={isPending}
              isFetching={isFetching}
              defaultValues={data}
            />
          </DialogBody>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { EditDepartments }
