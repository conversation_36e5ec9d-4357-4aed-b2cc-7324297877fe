import React from 'react'
import { <PERSON><PERSON>, <PERSON>alogB<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import departmentsApi, { type DepartmentsData, departmentsQueryKey } from '../../api/departments'
import { DepartmentsForm } from './components/form'

const CreateDepartment = () => {
  const { t } = useTranslation(['department', 'common'])
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: DepartmentsData) => departmentsApi.create(values),
    onSuccess: () => {
      toast.success(t('DEPARTMENT_ADDED_SUCCESS'))

      queryClient.invalidateQueries({
        queryKey: [departmentsQueryKey, 'list'],
      })

      history.replace('/departments')
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { message } = error?.response?.data ?? {}
      const errorMap = {
        'There already exists a resource with same fields name,accountId.': () => t('DEPARTMENT_EDIT_SAME_NAME_ERROR'),
      }
      toast.error(errorMap[message]?.() || t('DEPARTMENT_CREATED_ERROR'))
    },
  })

  return (
    <>
      <Helmet title={`${t('common:LABEL_CREATING')} ${t('department:TITLE_DEPARTMENT')}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.replace('/departments')
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('department:ADD_DEPARTMENT_TITLE')}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <DepartmentsForm
              onCancel={() => history.replace('/departments')}
              onSubmit={(values) => mutateAsync(values)}
              isPending={isPending}
            />
          </DialogBody>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { CreateDepartment }
