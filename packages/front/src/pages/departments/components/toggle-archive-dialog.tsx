import React from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { type AxiosError } from 'axios'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import departmentsApi, { departmentsQueryKey } from '../../../api/departments'
import { Department } from '../../../app/types/Department'

interface ToggleDepartmentArchiveDialogProps {
  isOpen: boolean
  onCancel: VoidFunction
  department: Department
}

const ToggleDepartmentArchiveDialog = ({ isOpen, onCancel, department }: ToggleDepartmentArchiveDialogProps) => {
  const { t } = useTranslation(['department', 'common'])
  const queryClient = useQueryClient()

  const isArchived = !!department.archivedAt

  const labelArchived = !isArchived ? t('department:LABEL_ARCHIVE') : t('department:LABEL_UNARCHIVE')

  const { mutateAsync, isPending } = useMutation({
    mutationFn: () => {
      return departmentsApi.archive(department.id, !isArchived)
    },
    onSuccess: async (res) => {
      toast.success(res?.archivedAt ? t('DEPARTMENT_ARCHIVE_SUCCESS') : t('DEPARTMENT_UNARCHIVE_SUCCESS'))
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [departmentsQueryKey, 'list'] }),
        queryClient.invalidateQueries({ queryKey: [departmentsQueryKey, department.id] }),
      ])
      onCancel()
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { status } = error?.response || {}

      if (status === 403) {
        const messageMap = {
          "Cannot archive because it's the only one.": () => t('CANNOT_ARCHIVE_BECAUSE_ONLY_ONE'),
          'Cannot archive because it is used in a bot.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_BOT'),
          'Cannot archive because it is used in a contact.': () => t('CANNOT_ARCHIVE_BACAUSE_USED_CONTACT'),
          'Cannot archive because it is used in a schedule.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_SCHEDULE'),
          'Cannot archive because it is used in a service.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_SERVICE'),
          'Cannot archive because it is used in a user.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_USER'),
          'Cannot archive because it is used in a quick reply.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_QUICK_REPLY'),
        }
        const message: () => string = messageMap[error.response.data.message]

        toast.error(message?.())
        return
      }

      toast.error(t('MODAL_MESSAGE_ERROR'))
    },
  })

  return (
    <AlertDialog open={isOpen} onOpenChange={onCancel}>
      {isOpen && <Helmet title={`${labelArchived} - ${department.name}`} />}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertTriangleIcon size={40} className="text-red-700" />
          <AlertDialogTitle>
            {labelArchived} {t('department:TITLE_DEPARTMENT')}
          </AlertDialogTitle>

          {!isArchived && <AlertDialogDescription>{t('ARCHIVE_DEPARTMENT_LABEL')}</AlertDialogDescription>}
          <AlertDialogDescription>{t('common:ARE_YOU_SURE_TO_CONTINUE')}</AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel className="w-full" data-testid="departments-toggle-archive-button-cancel">
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>

          <AlertDialogAction
            variant="danger"
            className="w-full"
            onClick={() => mutateAsync()}
            disabled={isPending}
            data-testid="departments-toggle-archive-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { ToggleDepartmentArchiveDialog }
