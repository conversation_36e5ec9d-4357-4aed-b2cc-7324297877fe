import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import z from 'zod'
import { type DepartmentsData } from '../../../api/departments'
import { Form, FormControl, FormField, FormItem, FormMessage } from '../../../app/components/common/unconnected/ui/form'
import { InputSkeleton } from '../../../components/unconnected/skeletons/input-skeleton'
import { Button, InputText, Label, Space } from '@ikatec/nebula-react'
import { LoaderIcon } from 'lucide-react'

interface DepartmentsFormProps {
  onCancel: VoidFunction
  onSubmit: (values: DepartmentsData) => void
  defaultValues?: Partial<DepartmentsData>
  isPending?: boolean
  isFetching?: boolean
}

const formSchema = z.object({
  name: z
    .string()
    .trim()
    .min(1, { message: 'department:DEPARTMENT_NAME_REQUIRED' })
    .max(255, { message: 'department:DEPARTMENT_NAME_MAX_LENGTH' }),
})

interface FormValues extends z.infer<typeof formSchema> {}

const DepartmentsForm = ({ onCancel, onSubmit, defaultValues, isPending, isFetching }: DepartmentsFormProps) => {
  const { t } = useTranslation(['department', 'common'])

  const formContext = useForm<FormValues>({
    defaultValues: {
      name: '',
      ...defaultValues,
    },
    resetOptions: {
      keepDefaultValues: true,
    },
    shouldFocusError: true,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(formSchema),
  })

  const handleSubmit = async (values: DepartmentsData) => {
    onSubmit(values)
  }

  useEffect(() => {
    if (!defaultValues) return

    formContext.reset(defaultValues)
  }, [defaultValues])

  if (isFetching) {
    return (
      <div>
        <div className="w-full">
          <InputSkeleton label={t('department:TABLE_DEPARTMENT_COLUMN_NAME')} />
        </div>

        <Space className="w-full mt-10">
          <Button className="w-full" variant="secondary" type="button" onClick={onCancel}>
            {t('common:FORM_ACTION_CANCEL')}
          </Button>
          <Button className="w-full" variant="primary" type="submit" disabled>
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:FORM_ACTION_SAVE')}
          </Button>
        </Space>
      </div>
    )
  }

  return (
    <Form {...formContext}>
      <form onSubmit={formContext.handleSubmit(handleSubmit)}>
        <div className="w-full">
          <FormField
            name="name"
            control={formContext.control}
            render={({ field, fieldState }) => {
              return (
                <FormItem>
                  <FormControl>
                    <>
                      <Label htmlFor="name">{t('department:TABLE_DEPARTMENT_COLUMN_NAME')}</Label>
                      <InputText
                        data-testid="departments-form-input-name"
                        type="text"
                        id="name"
                        name="name"
                        isError={Boolean(fieldState?.error?.message)}
                        {...field}
                      />
                    </>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </div>
        <Space className="w-full mt-10">
          <Button
            className="w-full"
            variant="secondary"
            type="button"
            onClick={onCancel}
            data-testid="departments-form-button-cancel"
          >
            {t('common:FORM_ACTION_CANCEL')}
          </Button>
          <Button
            className="w-full"
            variant="primary"
            type="submit"
            disabled={isPending || !formContext.formState.isValid}
            data-testid="departments-form-button-confirm"
          >
            {isPending && <LoaderIcon className="animate-spin" />}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </Space>
      </form>
    </Form>
  )
}

export { DepartmentsForm }
