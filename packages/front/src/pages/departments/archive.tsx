import React, { useEffect } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { type AxiosError } from 'axios'
import { AlertTriangleIcon, InfoIcon, LoaderIcon } from 'lucide-react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import departmentsApi, { departmentsQueryKey } from '../../api/departments'
import { useHistory, useParams } from 'react-router'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const ArchiveDepartment = () => {
  const { t } = useTranslation(['department', 'common'])
  const queryClient = useQueryClient()
  const history = useHistory()

  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [departmentsQueryKey, id],
    queryFn: () => departmentsApi.getById(id),
    enabled: Boolean(id),
  })

  const isArchived = !!data?.archivedAt

  const labelArchived = !isArchived ? t('department:LABEL_ARCHIVE') : t('department:LABEL_UNARCHIVE')

  const { mutateAsync, isPending } = useMutation({
    mutationFn: () => {
      return departmentsApi.archive(id, !isArchived)
    },
    onSuccess: async (res) => {
      toast.success(res?.archivedAt ? t('DEPARTMENT_ARCHIVE_SUCCESS') : t('DEPARTMENT_UNARCHIVE_SUCCESS'))
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [departmentsQueryKey, 'list'] }),
        queryClient.invalidateQueries({ queryKey: [departmentsQueryKey, data?.id] }),
      ])
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { status } = error?.response || {}

      if (status === 403) {
        const messageMap = {
          "Cannot archive because it's the only one.": () => t('CANNOT_ARCHIVE_BECAUSE_ONLY_ONE'),
          'Cannot archive because it is used in a bot.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_BOT'),
          'Cannot archive because it is used in a contact.': () => t('CANNOT_ARCHIVE_BACAUSE_USED_CONTACT'),
          'Cannot archive because it is used in a schedule.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_SCHEDULE'),
          'Cannot archive because it is used in a service.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_SERVICE'),
          'Cannot archive because it is used in a user.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_USER'),
          'Cannot archive because it is used in a quick reply.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_QUICK_REPLY'),
        }
        const message: () => string = messageMap[error.response.data.message]

        toast.error(message?.())
        return
      }

      toast.error(t('MODAL_MESSAGE_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('DEPARTMENT_NOT_FOUND'))
      return history.replace('/departments')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${labelArchived} - ${data?.name}`} />

      <AlertDialog open onOpenChange={(open) => !open && history.replace('/departments')}>
        <AlertDialogContent>
          <AlertDialogHeader>
            {isArchived && <InfoIcon size={40} className="text-primary-800 dark:text-neutral-600" />}
            {!isArchived && <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />}

            {isFetching && (
              <>
                <Skeleton width="50%" height="24px" />
                <AlertDialogDescription>
                  <Skeleton width="80%" height="24px" />
                </AlertDialogDescription>
                <AlertDialogDescription>
                  <Skeleton width="50%" height="24px" />
                </AlertDialogDescription>
              </>
            )}

            {isSuccess && (
              <>
                <AlertDialogTitle>
                  <span className="lowercase first-letter:uppercase">
                    {labelArchived} {t('department:TITLE_DEPARTMENT')}
                  </span>
                </AlertDialogTitle>

                {!isArchived && <AlertDialogDescription>{t('ARCHIVE_DEPARTMENT_LABEL')}</AlertDialogDescription>}

                <AlertDialogDescription>
                  {!isArchived ? t('ARE_YOU_SURE_YOU_WANT_TO_ARCHIVE') : t('ARE_YOU_SURE_YOU_WANT_TO_UNARCHIVE')}
                </AlertDialogDescription>
              </>
            )}
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel className="w-full" data-testid="departments-archive-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </AlertDialogCancel>

            <AlertDialogAction
              variant={isArchived ? 'primary' : 'danger'}
              className="w-full"
              onClick={() => mutateAsync()}
              disabled={isPending || isFetching}
              data-testid="departments-archive-button-confirm"
            >
              {isPending ? <LoaderIcon className="animate-spin" /> : null}
              {t('common:BUTTON_TEXT_CONFIRM')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export { ArchiveDepartment }
