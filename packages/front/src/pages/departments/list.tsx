import {
  Badge,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Heading,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tag,
} from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  BanIcon,
  CheckCircle2Icon,
  EllipsisVerticalIcon,
  EyeIcon,
  FilterIcon,
  PencilIcon,
  PlusIcon,
} from 'lucide-react'
import React, { memo, useMemo } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import departmentsApi, { departmentsQueryKey, type DepartmentsFilters } from '../../api/departments'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import useToggle from '../../app/hooks/useToggle'
import { FiltersTableTags, FiltersTagList } from '../../components/unconnected/table/filters-tag-list'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { useSearchParams } from '../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { debounceFn } from '../../utils/debouce-fn'
import { formatDate } from '../../utils/format-date'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { DepartmentsFilterFormValues, FilterForm } from './components/filter-form'
import { archiveOptions } from './constants'

const ListDepartments = () => {
  const history = useHistory()
  const {
    isOpen: filterDialogIsOpen,
    toggle: toggleFilterDialog,
    open: openFilterDialog,
    close: closeFilterDialog,
  } = useToggle(false)

  const countableFilters: (keyof DepartmentsFilters)[] = ['archivedAt']

  const { params, setValues, set, hasFilter, clear, searchParams, totalFilters, remove } =
    useSearchParams<DepartmentsFilters>(
      {
        order: 'ASC',
        sort: 'name',
        page: 1,
      },
      countableFilters,
    )

  const { t, i18n } = useTranslation(['department', 'common'])

  const { language } = i18n

  const queryClient = useQueryClient()

  const {
    data: departments,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [departmentsQueryKey, 'list', params],
    queryFn: () => departmentsApi.getAll({ ...params, customInclude: ['usersCount'] }),
    placeholderData() {
      return getPlaceholderData(
        queryClient.getQueriesData<typeof departments>({ queryKey: [departmentsQueryKey, 'list'] }),
      )
    },
  })

  const { currentPage, data = [], limit, total } = departments ?? {}

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/departments/create', search: searchParams }))
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/departments/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/departments/${id}/edit`, search: searchParams }))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'name', 'order', 'sort'])
  }

  const handleSetFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      set('name', value)
      remove(['page', 'order', 'sort'])
    })
  }

  const handleClearDrawerFilters = () => {
    remove(['archivedAt', 'page'])
    closeFilterDialog()
  }

  const handleSubmitDrawerFilters = (values: DepartmentsFilterFormValues) => {
    const { archivedAt } = values

    const keysToRemove: (keyof DepartmentsFilters)[] = ['page', 'order', 'sort']
    if (archivedAt?.value) {
      set('archivedAt', archivedAt.value)
    } else {
      keysToRemove.push('archivedAt')
    }

    remove(keysToRemove)
    closeFilterDialog()
  }

  const drawerFiltersList = useMemo<FiltersTableTags>(() => {
    const getLabelByFilterName = (name: string, value: string) => {
      if (name === 'archivedAt') {
        return archiveOptions.find((option) => option.value === value)?.label || ''
      }
    }

    const getNameByFilterName = (name: string) => {
      if (name === 'archivedAt') {
        return 'LABEL_STATUS'
      }
      return name
    }

    return Object.entries(params).reduce((acc, prev) => {
      const [name, label] = prev
      if (countableFilters.includes(prev[0] as any)) {
        acc.push({
          filter: name,
          name: t(getNameByFilterName(name) ?? name),
          label: t(getLabelByFilterName(name, label) ?? label),
        })
      }
      return acc
    }, [] as FiltersTableTags)
  }, [params, t])

  const navigateToArchive = (id: string) => {
    setTimeout(() => history.push({ pathname: `/departments/${id}/archive`, search: searchParams }))
  }

  return (
    <>
      <Helmet title={t('department:TITLE_DEPARTMENT_PLURAL')} />

      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="departments-list-heading">
              {t('department:TITLE_DEPARTMENT_PLURAL')}
            </Heading>

            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="departments-list-input-filter"
                name="name"
                defaultValue={params.name}
                onChange={(e) => handleSetFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />

              <Button variant="secondary" onClick={openFilterDialog} data-testid="departments-list-button-filters">
                <FilterIcon />
                {t('common:BUTTON_TEXT_FILTERS')}
                {totalFilters ? <Badge variant="secondary">{totalFilters}</Badge> : null}
              </Button>

              <FilterForm
                defaultValues={params}
                onSubmit={handleSubmitDrawerFilters}
                open={filterDialogIsOpen}
                onOpenChange={toggleFilterDialog}
              />

              <IfUserCan permission="departments.create">
                <Button size="md" data-testid="departments-list-button-add" onClick={navigateToCreate}>
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>

        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {isSuccess && !isFetching && hasFilter && (
            <FiltersTagList
              onClear={handleClearDrawerFilters}
              onDeleteFilter={(filter: keyof DepartmentsFilters) => remove([filter])}
              filters={drawerFiltersList}
            />
          )}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundWithoutFilter
              onAdd={navigateToCreate}
              moduleTitle={t('department:NO_DEPARTMENTS_CREATED')}
              moduleDescription={t('department:START_CREATE_FIRST_DEPARTMENT')}
            />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && data?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Space size="md" className="justify-start items-center">
                      {t('TABLE_DEPARTMENT_COLUMN_NAME')}
                      <SortTableButton
                        data-testid="departments-list-sort-name"
                        sort="name"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[220px]">
                    <Space size="md" className="justify-start items-center">
                      {t('LINKED_USERS_LABEL')}
                      <SortTableButton
                        data-testid="departments-list-sort-users-count"
                        sort="usersCount"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[160px]">
                    <Space size="md" className="justify-start items-center">
                      {t('common:CREATED_AT_LABEL')}
                      <SortTableButton
                        data-testid="departments-list-sort-created-at"
                        sort="createdAt"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-36">
                    <Space size="md" className="justify-start items-center">
                      {t('LABEL_STATUS')}
                      <SortTableButton
                        data-testid="departments-list-sort-archived-at"
                        sort="archivedAt"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[72px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((department, index) => {
                  const formattedCreatedAt = formatDate(new Date(department.createdAt), language)

                  const isArchived = !!department.archivedAt

                  return (
                    <TableRow key={department.id}>
                      <TableCell>
                        <div className="max-w-[600px] overflow-ellipsis overflow-hidden">{department.name}</div>
                      </TableCell>
                      <TableCell>{department.usersCount}</TableCell>
                      <TableCell>{formattedCreatedAt}</TableCell>
                      <TableCell className="flex justify-start items-center w-36">
                        <Tag color={isArchived ? 'gray' : 'green'}>
                          {!isArchived ? t('common:LABEL_UNARCHIVED') : t('common:LABEL_ARCHIVED')}
                        </Tag>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              icon
                              variant="ghost"
                              size="sm"
                              data-testid={`departments-list-button-actions-${index}`}
                            >
                              <EllipsisVerticalIcon />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              data-testid={`departments-list-button-actions-${index}-view`}
                              onClick={() => navigateToView(department.id)}
                            >
                              <EyeIcon />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </DropdownMenuItem>

                            <IfUserCan permission="departments.update">
                              <DropdownMenuItem
                                data-testid={`departments-list-button-actions-${index}-edit`}
                                onClick={() => navigateToEdit(department.id)}
                              >
                                <PencilIcon />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </DropdownMenuItem>
                            </IfUserCan>

                            <IfUserCan permission="departments.archive">
                              <DropdownMenuItem
                                data-testid={`departments-list-button-actions-${index}-archive`}
                                onClick={() => {
                                  navigateToArchive(department.id)
                                }}
                              >
                                {isArchived ? <CheckCircle2Icon /> : <BanIcon />}
                                {isArchived ? t('LABEL_UNARCHIVE') : t('LABEL_ARCHIVE')}
                              </DropdownMenuItem>
                            </IfUserCan>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}

          {/* if has data and isSuccessful, render pagination */}
          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>
    </>
  )
}

const memoizedListDepartments = memo(ListDepartments, () => true)

export { memoizedListDepartments as ListDepartments }
