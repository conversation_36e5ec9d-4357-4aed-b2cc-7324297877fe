import React, { useEffect } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import usersApi, { usersQueryKey } from '../../api/users'
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
  Button,
  DialogDescription,
  toast,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const UnblockUser = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const { id } = useParams<{ id: string }>()
  const { isError, isFetching, isSuccess } = useQuery({
    queryKey: [usersQueryKey, id],
    queryFn: () => usersApi.getById(id),
    enabled: <PERSON><PERSON><PERSON>(id),
  })
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: () => usersApi.unblock(id),
    onSuccess: async () => {
      toast.success(t('usersPage:UNBLOCK_USER_MESSAGE_SUCCESS'))
      await queryClient.invalidateQueries({
        queryKey: [usersQueryKey, 'list'],
      })
      history.replace('/users')
    },
    onError: () => {
      toast.error(t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/users')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/users')
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {isFetching && <Skeleton width="150px" height="24px" />}
            {isSuccess && t('MODAL_ENABLE_USER_ACCESS_TITLE')}
          </DialogTitle>
        </DialogHeader>
        <DialogBody>
          {isFetching && <Skeleton width="150px" height="24px" />}
          {isSuccess && (
            <>
              <DialogDescription>{t('MESSAGE_MODAL_UNBLOCK_USER')}</DialogDescription>

              <DialogDescription>{t('MESSAGE_MODAL_CONFIRMATION_UNBLOCK_USER')}</DialogDescription>
            </>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full" data-testid="users-unblock-button-cancel">
            <Button variant="secondary">{t('common:FORM_ACTION_CANCEL')}</Button>
          </DialogClose>

          <Button
            variant="primary"
            onClick={() => mutateAsync()}
            disabled={isPending}
            className="w-full"
            data-testid="users-unblock-button-confirm"
          >
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { UnblockUser }
