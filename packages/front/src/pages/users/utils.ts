import { type UserBody } from '../../api/users'
import { Department } from '../../app/types/Department'
import { Role } from '../../app/types/Role'
import { UserFormValues } from './components/form'

const parseUserFormValues = (values: Required<Omit<UserFormValues, 'id'>>): UserBody => {
  const { departments, roles, timetable, organizations, ...rest } = values

  return {
    ...rest,
    organizationIds: organizations?.map((organization) => String(organization.value)),
    departments: departments.map(
      (department) =>
        ({
          id: String(department.value),
          name: String(department.label),
        }) as Department,
    ),
    roles: roles.map(
      (role) =>
        ({
          id: String(role.value),
          displayName: String(role.label),
        }) as Role,
    ),
    timetableId: timetable ? String(timetable.value) : null,
  }
}

export { parseUserFormValues }
