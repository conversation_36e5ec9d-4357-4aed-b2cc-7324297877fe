import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { UsersFilters } from '../../../api/users'
import { Form, FormControl, FormField, FormItem } from '../../../app/components/common/unconnected/ui/form'
import {
  Label,
  Select,
  DrawerBody,
  DrawerFooter,
  DrawerClose,
  Button,
  Space,
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { archiveOptions } from '../constants'
import { DepartmentsSelect } from '../../../components/connected/departments-select'
import { RolesSelect } from '../../../components/connected/role-select'
import { TimetableSelect } from '../../../components/connected/timetable-select'

interface UserDrawerSubmitFilters {
  values: UsersFilters
  labels: Partial<Record<keyof UsersFilters, string | null>>
}

interface FilterDrawerProps {
  onSubmit: (data: UserDrawerSubmitFilters) => void
  defaultValues?: UsersFilters & Record<any, any>
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

interface UsersFilterDrawerValues {
  archivedAt: { value: UsersFilters['archivedAt']; label: string } | null
  department: { value: string; label: string } | null
  role: { value: string; label: string } | null
  timetable: { value: string; label: string } | null
}

const FilterDrawer = ({ onSubmit, defaultValues, open, onOpenChange }: FilterDrawerProps) => {
  const { t } = useTranslation(['usersPage', 'rolesPage', 'common'])

  const form = useForm<UsersFilterDrawerValues>({
    defaultValues: {
      department: null,
      role: null,
      timetable: null,
      archivedAt: null,
    },
  })

  const handleSubmit = (data: UsersFilterDrawerValues) => {
    onSubmit({
      values: {
        archivedAt: data.archivedAt?.value || null,
        department: data.department?.value || null,
        role: data.role?.value || null,
        timetable: data.timetable?.value || null,
      },
      labels: {
        archivedAt: data.archivedAt?.label || null,
        department: data.department?.label || null,
        role: data.role?.label || null,
        timetable: data.timetable?.label || null,
      },
    })
  }

  useEffect(() => {
    form.reset()

    if (!open || !defaultValues) return

    if (defaultValues.archivedAt) {
      form.setValue(
        'archivedAt',
        {
          value: defaultValues.archivedAt,
          label: t(archiveOptions.find((option) => option.value === defaultValues.archivedAt)?.label || ''),
        },
        { shouldDirty: true },
      )
    }

    if (defaultValues.department) {
      form.setValue(
        'department',
        {
          value: defaultValues.department,
          label: defaultValues['department-label'],
        },
        { shouldDirty: true },
      )
    }

    if (defaultValues.role) {
      form.setValue(
        'role',
        {
          value: defaultValues.role,
          label: defaultValues['role-label'],
        },
        { shouldDirty: true },
      )
    }

    if (defaultValues.timetable) {
      form.setValue(
        'timetable',
        {
          value: defaultValues.timetable,
          label: defaultValues['timetable-label'],
        },
        { shouldDirty: true },
      )
    }
  }, [open, defaultValues, form, t])

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent side="right">
        <DrawerHeader
          extraActions={
            form.formState.isDirty ? (
              <Button size="sm" variant="ghost" onClick={() => form.reset()} data-testid="users-filter-button-clear">
                {t('common:BUTTON_TEXT_CLEAR')}
              </Button>
            ) : null
          }
        >
          <DrawerTitle>{t('common:BUTTON_TEXT_FILTERS')}</DrawerTitle>
        </DrawerHeader>
        <DrawerBody>
          <Form {...form}>
            <form>
              <Space direction="column" className="w-full">
                <FormField
                  name="archivedAt"
                  control={form.control}
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <FormControl>
                          <>
                            <Label htmlFor="archivedAt">{t('TABLE_COLUMN_STATUS')}</Label>
                            <Select
                              id="archivedAt"
                              name="archivedAt"
                              placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                              options={archiveOptions.map(({ label, value }) => ({ value, label: t(label) }))}
                              isClearable
                              data-testid="users-filter-input-archivedAt"
                              {...field}
                            />
                          </>
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />

                <FormField
                  name="role"
                  control={form.control}
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <FormControl>
                          <>
                            <Label htmlFor="role">{t('rolesPage:TITLE_ROLES')}</Label>

                            <RolesSelect
                              id="role"
                              name="role"
                              placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                              isClearable
                              isPaged={false}
                              data-testid="users-filter-input-role"
                              {...field}
                            />
                          </>
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />

                <FormField
                  name="department"
                  control={form.control}
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <FormControl>
                          <>
                            <Label htmlFor="department">{t('TABLE_COLUMN_DEPARTMENT')}</Label>

                            <DepartmentsSelect
                              id="department"
                              name="department"
                              placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                              isPaged={false}
                              isClearable
                              data-testid="users-filter-input-department"
                              {...field}
                            />
                          </>
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />

                <FormField
                  name="timetable"
                  control={form.control}
                  render={({ field }) => {
                    return (
                      <FormItem className="w-full">
                        <FormControl>
                          <>
                            <Label htmlFor="timetable">{t('usersPage:TABLE_COLUMN_TIME_SCHEDULE')}</Label>

                            <TimetableSelect
                              id="timetable"
                              name="timetable"
                              placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                              isClearable
                              isPaged={false}
                              data-testid="users-filter-input-timetable"
                              {...field}
                            />
                          </>
                        </FormControl>
                      </FormItem>
                    )
                  }}
                />
              </Space>
            </form>
          </Form>
        </DrawerBody>
        <DrawerFooter>
          <Space className="w-full">
            <DrawerClose asChild>
              <Button className="w-full" variant="secondary" data-testid="users-filter-button-cancel">
                {t('common:FORM_ACTION_CANCEL')}
              </Button>
            </DrawerClose>

            <Button
              className="w-full"
              onClick={form.handleSubmit(handleSubmit)}
              data-testid="users-filter-button-apply"
            >
              {t('common:BUTTON_TEXT_SIMPLE_SUBMIT_FILTERS')}
            </Button>
          </Space>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export { FilterDrawer, type UsersFilterDrawerValues, type UserDrawerSubmitFilters }
