import React from 'react'
import { Label, Table, TableBody, TableHeader, TableHead, TableRow, TableCell } from '@ikatec/nebula-react'
import { useQuery } from '@tanstack/react-query'
import { useTranslation } from 'react-i18next'
import timetableApi, { timetableQueryKey } from '../../../api/timetable'
import { TableSkeleton } from '../../../components/unconnected/table/table-skeleton'

const TimeTable = (timeTable) => {
  const { t } = useTranslation(['timeTablePage', 'workPlan', 'common'])
  const id = timeTable.timeTable.value

  const { data, isFetching, isSuccess, isError } = useQuery({
    queryKey: [timetableQueryKey, id],
    queryFn: () => timetableApi.getById(id),
    enabled: Boolean(id),
  })

  const weekMap = {
    sun: t('workPlan:SUNDAY'),
    mon: t('workPlan:MONDAY'),
    tue: t('workPlan:TUESDAY'),
    wed: t('workPlan:WEDNESDAY'),
    thu: t('workPlan:THURSDAY'),
    fri: t('workPlan:FRIDAY'),
    sat: t('workPlan:SATURDAY'),
  }

  return (
    <div className="w-full">
      {isFetching && <TableSkeleton rows={4} />}
      {isSuccess && !isFetching && (
        <>
          <Label>
            {t('LABEL_TIMES')} - {data.name}
          </Label>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{t('TABLE_COLUMN_DAYS')}</TableHead>
                <TableHead className="w-28">{t('TABLE_COLUMN_START')}</TableHead>
                <TableHead className="w-28">{t('TABLE_COLUMN_END')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.workPlan.map((time) => {
                return (
                  <TableRow key={time.start}>
                    <TableCell>{time.weekDays.map((day) => weekMap[day as keyof typeof weekMap]).join(', ')}</TableCell>
                    <TableCell>{time.start}</TableCell>
                    <TableCell>{time.end}</TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </>
      )}
      {isError && !isFetching && <Label className="text-red-500">{t('common:LIST_IS_ERROR')}</Label>}
    </div>
  )
}

export { TimeTable }
