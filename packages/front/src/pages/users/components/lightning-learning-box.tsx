import { Box, Heading, Paragraph, Space } from '@ikatec/nebula-react'
import { ZapIcon } from 'lucide-react'
import React from 'react'
import { useTranslation } from 'react-i18next'

const LightningLearningBox = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  return (
    <Box variant="secondary">
      <Space className="items-center mb-4" size="sm">
        <ZapIcon width={24} height={24} className="text-primary-800 dark:text-primary-300" />
        <Heading level="4" className="text-primary-800 dark:text-primary-300">
          {t('common:LIGHTNING_LEARNING')}
        </Heading>
      </Space>

      <div className="pl-8">
        <Heading level="5" className="mb-2">
          {t('usersPage:USER_PERMISSIONS_LABEL')}
        </Heading>
        <Paragraph size="sm" className="w-72">
          {t('usersPage:USER_PERMISSIONS_DESCRIPTION')}
        </Paragraph>
      </div>
    </Box>
  )
}

export { LightningLearningBox }
