import {
  Badge,
  Button,
  Checkbox,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Heading,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tag,
} from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  BanIcon,
  CheckCircle2Icon,
  ChevronDown,
  EllipsisVerticalIcon,
  EyeIcon,
  Mail,
  FilterIcon,
  PencilIcon,
  PlusIcon,
  Lock,
} from 'lucide-react'
import React, { memo, useMemo, useState } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useSelector } from 'react-redux'
import { useHistory } from 'react-router'

import usersApi, { type UsersFilters, usersQueryKey } from '../../api/users'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import { AvailableLicensesAlert } from '../../app/components/common/unconnected/AvailableLicensesAlert'
import useToggle from '../../app/hooks/useToggle'
import { getUserAccount, getUser as getUserSelector } from '../../app/modules/auth/selectors'
import accountApi from '../../app/resources/account/api'
import User from '../../app/types/User'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { useSearchParams } from '../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { debounceFn } from '../../utils/debouce-fn'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { FilterDrawer, type UserDrawerSubmitFilters } from './components/filter-form'
import { archiveOptions } from './constants'
import { FiltersTableTags, FiltersTagList } from '../../components/unconnected/table/filters-tag-list'

const countableFilters: (keyof UsersFilters)[] = ['archivedAt', 'department', 'role', 'timetable']

const ListUsers = () => {
  const userAccount = useSelector(getUserAccount)
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set())
  const history = useHistory()

  const {
    isOpen: filterDialogIsOpen,
    toggle: toggleFilterDialog,
    open: openFilterDialog,
    close: closeFilterDialog,
  } = useToggle(false)

  const userAuth: User = useSelector(getUserSelector)

  const { params, setValues, set, hasFilter, clear, searchParams, remove, totalFilters } =
    useSearchParams<UsersFilters>(
      {
        order: 'ASC',
        sort: 'name',
        page: 1,
      },
      countableFilters,
    )

  const { t } = useTranslation(['usersPage', 'common'])

  const isAdmin = () => {
    return userAuth.roles.find((role) => role.isAdmin)
  }

  const queryClient = useQueryClient()

  const {
    data: users,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [usersQueryKey, 'list', params],
    queryFn: () => usersApi.getAll({ ...params, customInclude: ['departments', 'timetable', 'roles'] }),
    placeholderData() {
      return getPlaceholderData(queryClient.getQueriesData<typeof users>({ queryKey: [usersQueryKey, 'list'] }))
    },
  })

  const { currentPage, data = [], limit, total } = users ?? {}

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/users/create', search: searchParams }))
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/users/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/users/${id}/edit`, search: searchParams }))
  }
  const navigateToResetPassword = (id: string) => {
    setTimeout(() => history.push({ pathname: `/users/${id}/reset-password`, search: searchParams }))
  }
  const navigateToArchive = (id: string) => {
    setTimeout(() => history.push({ pathname: `/users/${id}/archive`, search: searchParams }))
  }
  const navigateToUpdateTimetable = () => {
    setTimeout(() =>
      history.push({
        pathname: '/users/update-timetable',
        state: { ids: Array.from(selectedIds) },
      }),
    )
  }

  const navigateToUnblock = (id: string) => {
    setTimeout(() => history.push({ pathname: `/users/${id}/unblock`, search: searchParams }))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'order', 'sort', 'name'])
  }

  const handleSetInputFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      remove(['page', 'order', 'sort'])
      set('name', value)
    })
  }

  const handleClearDrawerFilters = () => {
    remove([...countableFilters, 'page', 'order', 'sort'])
    closeFilterDialog()
  }

  const handleSubmitDrawerFilters = (userDrawerSubmitFilters: UserDrawerSubmitFilters) => {
    const keysToRemove: (keyof UsersFilters)[] = ['page', 'order', 'sort']

    const { values, labels } = userDrawerSubmitFilters

    Object.entries(values).forEach(([key, value]) => {
      if (value) {
        set(key as keyof UsersFilters, value)
        set((key + '-label') as keyof UsersFilters, labels[key])
      } else keysToRemove.push(key as keyof UsersFilters)
    })

    remove(keysToRemove)
    closeFilterDialog()
  }

  const { creditsControlEnabled } = userAccount

  const { data: accountAmounts } = useQuery({
    queryKey: [usersQueryKey, 'list', 'getAccountAmounts'],
    queryFn: () => {
      return accountApi.getAccountAmounts({
        filter: 'users.amounts',
      })
    },
    enabled: creditsControlEnabled,
  })

  const massActionsPermissions = ['users.update']

  const allSelected = selectedIds.size === data?.length

  const drawerFiltersList = useMemo<FiltersTableTags>(() => {
    const labelByColumn = {
      archivedAt: 'TABLE_COLUMN_STATUS',
      department: 'TABLE_COLUMN_DEPARTMENTS',
      role: 'TABLE_COLUMN_POSTS',
      timetable: 'TABLE_COLUMN_TIME_SCHEDULE',
    } satisfies Partial<Record<keyof UsersFilters, string>>

    const getLabelByFilterName = (name: string, value: string) => {
      if (name === 'archivedAt') {
        return archiveOptions.find((option) => option.value === value)?.label || ''
      }
    }

    return Object.entries(params).reduce((acc, prev) => {
      const [name, label] = prev

      const urlLabel = params[name + '-label']

      if (countableFilters.includes(prev[0] as any)) {
        acc.push({
          name: t(labelByColumn[name] ?? name),
          filter: name,
          label: t(getLabelByFilterName(name, label) ?? urlLabel ?? label),
        })
      }
      return acc
    }, [] as FiltersTableTags)
  }, [params, t])

  return (
    <>
      <Helmet title={t('usersPage:TITLE_USERS')} />

      <Container>
        <ContainerHeader>
          {!!accountAmounts?.usersAmounts?.available && (
            <IfUserCan permission="users.create">
              <AvailableLicensesAlert quantity={accountAmounts.usersAmounts.available} type="user" />
            </IfUserCan>
          )}

          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="users-list-heading">
              {t('usersPage:TITLE_USERS')}
            </Heading>

            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="users-list-input-filter"
                name="label"
                defaultValue={params.name}
                onChange={(e) => handleSetInputFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />

              <Button variant="secondary" onClick={openFilterDialog} data-testid="users-list-button-filters">
                <FilterIcon />
                {t('common:BUTTON_TEXT_FILTERS')}
                {totalFilters ? <Badge variant="secondary">{totalFilters}</Badge> : null}
              </Button>

              <FilterDrawer
                open={filterDialogIsOpen}
                onOpenChange={toggleFilterDialog}
                onSubmit={handleSubmitDrawerFilters}
                defaultValues={params}
              />

              {selectedIds.size > 0 && (
                <IfUserCan any permissions={massActionsPermissions}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild data-testid="users-list-button-bulk-actions">
                      <Button variant="secondary" data-testid="users-button-bulk-actions">
                        {t('common:BUTTON_BULK_ACTIONS')}
                        <ChevronDown />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem
                        onClick={navigateToUpdateTimetable}
                        data-testid="users-list-button-bulk-actions-timetable"
                      >
                        {t('CHANGE_TIMETABLE')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </IfUserCan>
              )}

              <IfUserCan permission="users.create">
                <Button size="md" onClick={navigateToCreate} data-testid="users-list-button-add">
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>

        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {isSuccess && !isFetching && hasFilter && (
            <FiltersTagList
              onClear={handleClearDrawerFilters}
              onDeleteFilter={(filter: keyof UsersFilters) => remove([filter])}
              filters={drawerFiltersList}
            />
          )}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundWithoutFilter
              onAdd={navigateToCreate}
              moduleTitle={t('usersPage:NO_DATA_CREATED')}
              moduleDescription={t('usersPage:START_CREATE_FIRST_DATA')}
            />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && data?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <IfUserCan any permissions={massActionsPermissions}>
                    <TableHead className="w-[40px] ">
                      <Checkbox
                        variant={selectedIds.size > 0 && !allSelected ? 'multiselect' : 'default'}
                        checked={selectedIds.size > 0}
                        onCheckedChange={(isChecked) => {
                          setSelectedIds(isChecked || !allSelected ? new Set(data.map((user) => user.id)) : new Set())
                        }}
                        data-testid="users-list-checkbox-select-all"
                      />
                    </TableHead>
                  </IfUserCan>
                  <TableHead>
                    <Space size="sm" className="justify-start items-center whitespace-nowrap">
                      {t('usersPage:TABLE_COLUMN_NAME')}
                      <SortTableButton
                        data-testid="users-list-sort-name"
                        sort="name"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead>
                    <Space size="sm" className="justify-start items-center">
                      {t('usersPage:TABLE_COLUMN_EMAIL')}
                      <SortTableButton
                        data-testid="users-list-sort-email"
                        sort="email"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead>
                    <Space size="sm" className="items-center">
                      {t('usersPage:TABLE_COLUMN_POSTS')}
                      {/* <SortTableButton
                        data-testid="users-list-sort-role"
                        sort="role"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      /> */}
                    </Space>
                  </TableHead>
                  <TableHead>
                    <Space size="sm" className="items-center">
                      {t('usersPage:TABLE_COLUMN_DEPARTMENTS')}
                      {/* <SortTableButton
                        data-testid="users-list-sort-department"
                        sort="department"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      /> */}
                    </Space>
                  </TableHead>
                  <TableHead className="whitespace-nowrap">
                    <Space size="sm" className="items-center">
                      {t('usersPage:TABLE_COLUMN_TIME_SCHEDULE')}
                      {/* <SortTableButton
                        data-testid="users-list-sort-timetable"
                        sort="timetable"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      /> */}
                    </Space>
                  </TableHead>
                  <TableHead className="w-32">
                    <Space size="sm" className="items-center">
                      {t('usersPage:TABLE_COLUMN_STATUS')}{' '}
                      <SortTableButton
                        data-testid="users-list-sort-archivedAt"
                        sort="archivedAt"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[72px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((user, index) => {
                  const isArchived = !!user.archivedAt
                  const isChecked = selectedIds.has(user.id)
                  const departmentsText = user.departments?.map((department) => department.name).join(', ')
                  const rolesText = user.roles?.map((role) => role.displayName).join(', ')
                  return (
                    <TableRow key={user.id}>
                      <IfUserCan any permissions={massActionsPermissions}>
                        <TableCell>
                          <Checkbox
                            checked={isChecked}
                            onCheckedChange={() => {
                              setSelectedIds((state) => {
                                const newSet = new Set(state)

                                if (isChecked) {
                                  newSet.delete(user.id)
                                } else {
                                  newSet.add(user.id)
                                }
                                return newSet
                              })
                            }}
                            data-testid={`users-list-checkbox-select-${index}`}
                          />
                        </TableCell>
                      </IfUserCan>
                      <TableCell className="overflow-hidden text-ellipsis max-w-52 whitespace-nowrap" title={user.name}>
                        {user.name}
                      </TableCell>
                      <TableCell
                        className="overflow-hidden text-ellipsis max-w-52 whitespace-nowrap"
                        title={user.email}
                      >
                        {user.email}
                      </TableCell>
                      <TableCell className="overflow-hidden text-ellipsis max-w-40 whitespace-nowrap" title={rolesText}>
                        {rolesText || '-'}
                      </TableCell>
                      <TableCell
                        className="overflow-hidden text-ellipsis max-w-44 whitespace-nowrap"
                        title={departmentsText}
                      >
                        {departmentsText || '-'}
                      </TableCell>
                      <TableCell>{user.timetable?.name ?? '-'}</TableCell>
                      <TableCell className="flex justify-start items-center w-32">
                        <Tag color={isArchived ? 'gray' : 'green'}>
                          {!isArchived ? t('common:LABEL_UNARCHIVED') : t('common:LABEL_ARCHIVED')}
                        </Tag>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button icon variant="ghost" size="sm" data-testid={`users-list-button-actions-${index}`}>
                              <EllipsisVerticalIcon />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => navigateToView(user.id)}
                              data-testid={`users-list-button-actions-${index}-view`}
                            >
                              <EyeIcon />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </DropdownMenuItem>

                            <IfUserCan permission="users.update">
                              <DropdownMenuItem
                                onClick={() => navigateToEdit(user.id)}
                                data-testid={`users-list-button-actions-${index}-edit`}
                              >
                                <PencilIcon />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </DropdownMenuItem>
                            </IfUserCan>
                            {!user.archivedAt && (
                              <IfUserCan permission="users.view">
                                <DropdownMenuItem
                                  onClick={() => navigateToResetPassword(user.id)}
                                  data-testid={`users-list-button-actions-${index}-reset-password`}
                                >
                                  <Mail />
                                  {t('RESET_PASSWORD')}
                                </DropdownMenuItem>
                              </IfUserCan>
                            )}

                            {userAuth?.id !== user.id && (
                              <IfUserCan permission="users.archive">
                                <DropdownMenuItem
                                  data-testid={`users-list-button-actions-${index}-archive`}
                                  onClick={() => {
                                    navigateToArchive(user.id)
                                  }}
                                >
                                  {isArchived ? <CheckCircle2Icon /> : <BanIcon />}
                                  {isArchived ? t('common:LABEL_UNARCHIVE') : t('common:ACTIONS_SUBMENU_ARCHIVE')}
                                </DropdownMenuItem>
                              </IfUserCan>
                            )}

                            {isAdmin() && !isArchived && (
                              <DropdownMenuItem
                                data-testid={`users-list-button-actions-${index}-unblock`}
                                onClick={() => {
                                  navigateToUnblock(user.id)
                                }}
                              >
                                <Lock />
                                {t('common:LABEL_UNLOCK_ACCESS')}
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}

          {/* if has data and isSuccessful, render pagination */}
          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>
    </>
  )
}

const memoizedListUsers = memo(ListUsers, () => true)

export { memoizedListUsers as ListUsers }
