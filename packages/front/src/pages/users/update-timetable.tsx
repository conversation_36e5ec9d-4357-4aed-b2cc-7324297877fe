import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogBody,
  <PERSON>alog<PERSON>lose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  Label,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import { Spinner } from 'reactstrap'
import usersApi, { usersQueryKey } from '../../api/users'
import { Form, FormControl, FormField, FormItem } from '../../app/components/common/unconnected/ui/form'
import { TimetableSelect } from '../../components/connected/timetable-select'

const UpdateTimetable = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const history = useHistory()
  const queryClient = useQueryClient()
  const { ids } = (history.location.state ?? {}) as { ids: string[] }

  const form = useForm({
    defaultValues: {
      timetable: {
        value: '',
        label: '',
      },
    },
  })

  const { mutateAsync, isPending } = useMutation({
    mutationFn: () =>
      usersApi.bulkUpdate({
        ids,
        timetableId: form.getValues().timetable.value,
      }),
    onSuccess: async () => {
      toast.success(t('UPDATE_TIMETABLE_SUCCESS'))
      await queryClient.invalidateQueries({
        queryKey: [usersQueryKey, 'list'],
      })
      history.replace('/users')
    },
    onError: () => {
      toast.error(t('common:THIS_ACTION_COULD_NOT_BE_PERFORMED'))
    },
  })

  if (ids && ids?.length === 0) {
    toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))

    history.replace('/users')
  }

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/users')
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('CHANGE_TIMETABLE')}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <Form {...form}>
            <FormField
              name="timetable"
              control={form.control}
              render={({ field }) => {
                return (
                  <FormItem className="w-full">
                    <FormControl>
                      <>
                        <Label htmlFor="timetable">{t('usersPage:TABLE_COLUMN_TIME_SCHEDULE')}</Label>

                        <TimetableSelect
                          id="timetable"
                          name="timetable"
                          placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                          isClearable
                          isPaged={false}
                          data-testid="users-update-timetable-input-timetable"
                          {...field}
                        />
                      </>
                    </FormControl>
                  </FormItem>
                )
              }}
            />
          </Form>
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="users-update-timetable-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
          </DialogClose>
          <Button
            variant="primary"
            className="w-full"
            onClick={() => mutateAsync()}
            disabled={isPending}
            data-testid="users-update-timetable-button-confirm"
          >
            {isPending ? <Spinner size={'sm'} /> : t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { UpdateTimetable }
