import React, { useEffect } from 'react'
import { useQuery, useMutation } from '@tanstack/react-query'
import { useHistory, useParams } from 'react-router'
import usersApi, { usersQueryKey } from '../../api/users'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
  DialogBody,
  DialogClose,
  Button,
  toast,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import resetPasswordApi from '../../api/forgot-password'
import { Spinner } from 'reactstrap'

const ResetPassword = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()

  const { data, isError } = useQuery({
    queryKey: [usersQueryKey, id],
    queryFn: () => usersApi.getById(id),
    enabled: <PERSON><PERSON>an(id),
  })

  const { isPending, mutate } = useMutation({
    mutationFn: () =>
      resetPasswordApi.request({
        accountId: data.accountId,
        email: data.email,
      }),
    onSuccess: () => {
      toast.success(t('SEND_RESET_LINK_TO_EMAIL_SUCCESS'))
      history.replace('/users')
    },
    onError: () => {
      toast.error(t('SEND_RESET_LINK_TO_EMAIL_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      history.replace('/users')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/users')
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('RESET_PASSWORD')}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          <DialogDescription>{t('SEND_RESET_LINK_TO_EMAIL_WARN')}</DialogDescription>
          <DialogDescription>{t('SEND_RESET_LINK_TO_EMAIL_CONFIRM')}</DialogDescription>
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="users-reset-password-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </Button>
          </DialogClose>
          <Button
            variant="primary"
            className="w-full"
            onClick={() => mutate()}
            disabled={isPending}
            data-testid="users-reset-password-button-confirm"
          >
            {isPending ? <Spinner size={'sm'} /> : t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { ResetPassword }
