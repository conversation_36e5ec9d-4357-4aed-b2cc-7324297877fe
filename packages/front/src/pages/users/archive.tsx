import React, { useMemo } from 'react'
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogFooter,
  AlertDialogTitle,
  AlertDialogCancel,
  AlertDialogAction,
  AlertDialogDescription,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, InfoIcon, LoaderIcon } from 'lucide-react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import usersApi, { usersQueryKey } from '../../api/users'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'
import config from '../../../config'
import { AxiosError } from 'axios'

const ArchiveUser = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['usersPage', 'common'])
  const history = useHistory()

  const { data, isFetching, isError, isSuccess } = useQuery({
    queryKey: [usersQueryKey, id],
    queryFn: () => usersApi.getById(id),
    enabled: !!id,
  })

  const isArchived = useMemo(() => !!data?.archivedAt, [data?.archivedAt])

  const alertDialogTitle = useMemo(
    () => (isArchived ? t('common:LABEL_UNARCHIVE') : t('common:ACTIONS_SUBMENU_ARCHIVE')),
    [isArchived],
  )

  const queryClient = useQueryClient()

  const messageMap = useMemo(
    () => ({
      'Cannot archive because it is used in a bot.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_BOT'),
      'Cannot archive because it is used in a contact.': () => t('CANNOT_ARCHIVE_BACAUSE_USED_CONTACT'),
      'Cannot archive because it is used in a schedule.': () => t('CANNOT_ARCHIVE_BECAUSE_USED_SCHEDULE'),
      'Cannot unarchive because is not user super admin.': () =>
        t('CANNOT_DESARCHIVE_BECAUSE_NOT_SUPER_ADM', {
          whitelabel: config('whitelabel.appName'),
        }),
      'Cannot archive because exist user in ticket.': () => t('CANNOT_ARCHIVE_BECAUSE_EXIST_USER_CALL'),
      'Plan limit reached.': () => t('CANNOT_ARCHIVE_LIMIT_PLAN'),
    }),
    [t],
  )

  const { mutate, isPending } = useMutation({
    mutationFn: async () => {
      return usersApi.archive(id, !isArchived)
    },
    onSuccess: async () => {
      toast.success(!isArchived ? t('USER_ARCHIVED_SUCCESSFULLY') : t('USER_UNARCHIVE_SUCCESSFULLY'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [usersQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [usersQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])
    },
    onError: (error: AxiosError<{ message: string }>) => {
      const { data: errorData } = error?.response || {}

      const errorMessage = messageMap[errorData?.message]?.() ?? t('USER_DELETION_ERROR')

      toast.error(errorMessage)
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('USER_NOT_FOUND'))
      return history.replace('/users')
    }
  }, [isError])

  return (
    <AlertDialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/users')
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {isArchived && <InfoIcon size={40} className="text-primary-800 dark:text-neutral-600" />}
            {!isArchived && <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />}
            {alertDialogTitle} {t('GET_TITLE_USER')}
          </AlertDialogTitle>

          {isFetching && (
            <>
              <Skeleton width="80%" height="24px" />
              <Skeleton width="50%" height="24px" />
            </>
          )}

          {isSuccess && (
            <>
              <AlertDialogDescription>
                {isArchived ? t('ARE_YOU_SURE_UNARCHIVE_USER') : t('ARE_YOU_SURE_ARCHIVE_USER')}
              </AlertDialogDescription>
            </>
          )}
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel className="w-full" data-testid="users-archive-button-cancel">
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>

          <AlertDialogAction
            variant={isArchived ? 'primary' : 'danger'}
            className="w-full"
            onClick={() => mutate()}
            disabled={isPending || isFetching}
            data-testid="users-archive-button-confirm"
          >
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { ArchiveUser }
