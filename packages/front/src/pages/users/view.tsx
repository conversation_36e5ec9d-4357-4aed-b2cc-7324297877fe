import {
  <PERSON>ton,
  <PERSON>alog,
  DialogBody,
  DialogClose,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
  Label,
  Paragraph,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useQuery } from '@tanstack/react-query'
import React, { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import usersApi, { usersQueryKey } from '../../api/users'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const ViewUsers = () => {
  const { t } = useTranslation(['usersPage', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [usersQueryKey, id],
    queryFn: () => usersApi.getById(id),
    enabled: <PERSON><PERSON><PERSON>(id),
  })

  const userToString = (users) => users && users.map((user) => user.name || user.displayName).join(', ')

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/users')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.replace('/users')
      }}
    >
      <DialogContent>
        <DialogHeader className="flex items-center">
          <DialogTitle className="break-words max-w-[500px] text-center">
            {isFetching && <Skeleton width="150px" height="24px" />}
            {isSuccess && !isFetching && data.name}
          </DialogTitle>
        </DialogHeader>
        <DialogBody>
          {isFetching && (
            <Space className="w-full items-start" direction="column">
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_NAME')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_EMAIL')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('CREATE_USER_LABEL_PHONE')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('CREATE_USER_LABEL_BRANCH')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_POSTS')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_DEPARTMENTS')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
              <Space size="sm">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_TIME_SCHEDULE')}:
                </Paragraph>
                <Skeleton width="150px" height="24px" />
              </Space>
            </Space>
          )}

          {isSuccess && !isFetching && (
            <Space className="items-start w-150 text-neutral-1000 dark:text-neutral-200" direction="column">
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_NAME')}:
                </Paragraph>{' '}
                <Paragraph size="sm" className="break-words max-w-[552px]">
                  {data.name || '-'}
                </Paragraph>
              </Space>
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_EMAIL')}:
                </Paragraph>{' '}
                <Paragraph size="sm">{data.email || '-'}</Paragraph>
              </Space>
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('CREATE_USER_LABEL_PHONE')}:
                </Paragraph>{' '}
                <Paragraph size="sm">{data?.phoneNumber || '-'}</Paragraph>
              </Space>
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('CREATE_USER_LABEL_BRANCH')}:
                </Paragraph>{' '}
                <Paragraph size="sm">{data?.branch || '-'}</Paragraph>
              </Space>
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_POSTS')}:
                </Paragraph>{' '}
                <Paragraph size="sm">{userToString(data?.roles) || '-'}</Paragraph>
              </Space>
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_DEPARTMENTS')}:
                </Paragraph>{' '}
                <Paragraph size="sm">{userToString(data?.departments) || '-'}</Paragraph>
              </Space>
              <Space direction="row" size="xs" className="items-baseline">
                <Paragraph size="sm" className="font-semibold">
                  {t('TABLE_COLUMN_TIME_SCHEDULE')}:
                </Paragraph>{' '}
                <Paragraph size="sm">{data?.timetable?.name || '-'}</Paragraph>
              </Space>
            </Space>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="users-view-button-close">
              {t('common:FORM_ACTION_CLOSE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { ViewUsers }
