import { Heading } from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { CardCredits } from './components/card-credits'
import creditsApi, { creditsQueryKey } from '../../api/credits'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { CardSkeleton } from '../../components/unconnected/table/card-skeleton'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { Credits } from '../../app/types/Credits'

const ListCredits = () => {
  const { t } = useTranslation(['creditsPage', 'common'])
  const queryClient = useQueryClient()

  const {
    data: credits,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [creditsQueryKey],
    queryFn: creditsApi.getBalances,
    placeholderData() {
      return getPlaceholderData(queryClient.getQueriesData<typeof credits>({ queryKey: [creditsQueryKey] }))
    },
  })

  return (
    <>
      <Helmet title={t('TITLE_CREDITS')} />
      <Container>
        <ContainerHeader>
          <Heading data-testid="credits-list-heading" level="1">
            {t('TITLE_CREDITS')}
          </Heading>
        </ContainerHeader>
        <ContainerContent>
          {!isFetching && isError && <NoPossibleLoadData onRefresh={refetch} />}
          <div className="grid grid-cols-3 gap-4">
            {isFetching && <CardSkeleton />}
            {!isFetching &&
              isSuccess &&
              Object.entries(credits).map(([serviceType, balance]: [keyof Credits, number]) => (
                <CardCredits key={serviceType} serviceType={serviceType} balance={balance} />
              ))}
          </div>
        </ContainerContent>
      </Container>
    </>
  )
}

export { ListCredits }
