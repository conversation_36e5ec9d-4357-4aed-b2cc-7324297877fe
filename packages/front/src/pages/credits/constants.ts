export const TYPE_ENUM = {
  in: 'FILTERS_LABEL_ENTRANCE',
  out: 'FILTERS_LABEL_EXIT',
}

export const ORIGIN_ENUM = {
  trial: 'LABEL_TRIAL',
  single: 'LABEL_SINGLE',
  bulk: '<PERSON>BE<PERSON>_BULK',
  chargeback: 'LABEL_CHARGEBACK',
  renewal: 'LABEL_RENEWAL',
  reset: 'LABEL_RESET',
}

export const SERVICE_TYPE_ENUM = {
  copilot: 'LABEL_COPILOT',
  csat: 'LABEL_CSAT',
  'magic-text': 'LABEL_MAGIC_TEXT',
  'sms-wavy': 'LABEL_SMS',
  summary: 'LABEL_SUMMARY',
  transcription: 'LABEL_TRANSCRIPTION',
  agent: 'LABEL_AGENT',
}

export const typesOptions = [
  { value: 'all', label: 'common:BUTTON_TEXT_LABEL_FILTERS_ALL' },
  { value: 'in', label: 'FILTERS_LABEL_ENTRANCE' },
  { value: 'out', label: 'FILTERS_LABEL_EXIT' },
]
