import { <PERSON><PERSON>, <PERSON>, Separator, <PERSON>, Heading, Paragraph } from '@ikatec/nebula-react'
import { Bot, MessageCircle, WandSparkles, Star, CpuIcon } from 'lucide-react'
import React from 'react'
import { useHistory } from 'react-router'
import { useTranslation } from 'react-i18next'
import { SERVICE_TYPE_ENUM } from '../constants'
import { Credits } from '../../../app/types/Credits'
import convertSecondsToTimeString from '../../../app/utils/convertSecondsToTimeString'

interface CardCreditsProps {
  serviceType: keyof Credits
  balance: number
}

const CardCredits = ({ serviceType, balance }: CardCreditsProps) => {
  const history = useHistory()
  const { t, i18n } = useTranslation(['creditsPage', 'common'])
  const { language } = i18n

  const navigateToView = () => history.push({ pathname: `/credits/${serviceType}` })

  const iconsServices = {
    copilot: <Bot className="text-neutral-1000 dark:text-neutral-300" size="20" />,
    csat: <Star className="text-neutral-1000 dark:text-neutral-300" size="20" />,
    'magic-text': <WandSparkles className="text-neutral-1000 dark:text-neutral-300" size="20" />,
    'sms-wavy': <MessageCircle className="text-neutral-1000 dark:text-neutral-300" size="20" />,
    summary: <WandSparkles className="text-neutral-1000 dark:text-neutral-300" size="20" />,
    transcription: <WandSparkles className="text-neutral-1000 dark:text-neutral-300" size="20" />,
    agent: <CpuIcon className="text-neutral-1000 dark:text-neutral-300" size="20" />,
  }

  const formatNumber = (value: number): string =>
    value.toLocaleString(language, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })

  return (
    <Box border paddingSize="sm">
      <div className="flex-col gap-3">
        <div className="flex justify-between items-center">
          <Space className="items-center" size="sm">
            {iconsServices[serviceType]}
            <Heading level="4">{t(SERVICE_TYPE_ENUM[serviceType]) || serviceType}</Heading>
          </Space>

          <Button variant="secondary" size="xs" onClick={() => navigateToView()}>
            {t('common:DETAILS_LABEL')}
          </Button>
        </div>

        <Separator className="my-3" orientation="horizontal" />

        <Space direction="column" size="sm">
          <Paragraph size="sm">{t('LABEL_AMOUNT')}</Paragraph>
          <Paragraph className="text-xl font-semibold text-neutral-1000 dark:text-neutral-100">
            {serviceType === 'transcription' ? convertSecondsToTimeString(balance) : formatNumber(balance)}
          </Paragraph>
        </Space>
      </div>
    </Box>
  )
}

export { CardCredits }
