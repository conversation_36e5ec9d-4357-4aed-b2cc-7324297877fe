import {
  But<PERSON>,
  <PERSON><PERSON><PERSON>,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Heading,
  Tag,
  Select,
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from '@ikatec/nebula-react'
import { ChevronLeft } from 'lucide-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import { useHistory } from 'react-router'
import { useParams, Link } from 'react-router-dom'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundByServiceType } from '../../components/unconnected/table/no-results-found-by-service-type'
import creditsA<PERSON>, { credits<PERSON><PERSON>y<PERSON><PERSON>, CreditsFilters } from '../../api/credits'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { useSearchParams } from '../../hooks/use-search-params'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { TYPE_ENUM, ORIGIN_ENUM, SERVICE_TYPE_ENUM, typesOptions } from './constants'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import convertSecondsToTimeString from '../../app/utils/convertSecondsToTimeString'
import { formatDate } from '../../utils/format-date'

const ViewCredits = () => {
  const { serviceType } = useParams<{ serviceType: string }>()
  const history = useHistory()
  const { t, i18n } = useTranslation(['creditsPage', 'common'])
  const { language } = i18n
  const queryClient = useQueryClient()

  const countableFilters: (keyof CreditsFilters)[] = ['type']

  const { params, setValues, set, hasFilter, clear, remove } = useSearchParams<CreditsFilters>(
    {
      order: 'DESC',
      sort: 'createdAt',
      page: 1,
      serviceType: serviceType,
    },
    countableFilters,
  )

  const {
    data: credits,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [creditsQueryKey, 'list', params],
    queryFn: () => creditsApi.getAll({ ...params }),
    placeholderData() {
      return getPlaceholderData(queryClient.getQueriesData<typeof credits>({ queryKey: [creditsQueryKey] }))
    },
  })

  const { currentPage, data = [], limit, total } = credits ?? {}

  const navigateToList = () => {
    setTimeout(() => history.push({ pathname: `/credits` }))
  }

  const title = `${t('CREDIT_MOVEMENT')}: ${t(SERVICE_TYPE_ENUM[serviceType]) || serviceType}`

  const formatNumber = (value: number): string =>
    value.toLocaleString(language, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    })

  interface CreditMovementsFilterValues {
    value: string
    label: string
  }

  const handleSetSelectFilter = (type: CreditMovementsFilterValues) => {
    const keysToRemove: (keyof CreditsFilters)[] = ['page', 'order', 'sort']
    if (type?.value) {
      remove(['page', 'order', 'sort'])
      set('type', type.value)
    } else {
      keysToRemove.push('type')
    }
    remove(keysToRemove)
  }

  return (
    <>
      <Helmet title={title} />
      <Container>
        <ContainerHeader mtSize="sm">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <Link to="/credits" data-testid="credits-view-breadcrumb-link-to-credits-list">
                  {t('TITLE_CREDITS')}
                </Link>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{t(SERVICE_TYPE_ENUM[serviceType])}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
          <div className="flex justify-between items-center">
            <Space size="sm" className="items-center">
              <Link
                to="/credits"
                aria-label="Go back to credits' list"
                data-testid="credits-view-button-go-back"
                role="link"
              >
                <Button variant="ghost" icon>
                  <ChevronLeft />
                </Button>
              </Link>
              <Heading level="1" data-testid="credits-list-heading">
                {title}
              </Heading>
            </Space>
            {((isSuccess && !!total && !isFetching) || hasFilter) && (
              <Select
                id="type"
                name="type"
                placeholder={t('PLACEHOLDER_SELECT_TYPE_MOVEMENT')}
                options={typesOptions.map(({ label, value }) => ({ value, label: t(label) }))}
                isClearable
                className="w-56"
                data-testid="credits-view-select-filter-type"
                onChange={handleSetSelectFilter}
                isSearchable={false}
              />
            )}
          </div>
        </ContainerHeader>
        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundByServiceType navigateToList={navigateToList} />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && data?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-1/4">
                    <Space size="md" className="justify-start items-center">
                      {t('TABLE_COLUMN_DATE')}
                      <SortTableButton
                        sort="createdAt"
                        order={params.order}
                        currentSort={params.sort}
                        data-testid="credits-view-sort-table-button-created-at"
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-1/4">
                    <Space size="md" className="justify-start items-center">
                      {t('TABLE_COLUMN_TYPE')}
                      <SortTableButton
                        sort="type"
                        order={params.order}
                        currentSort={params.sort}
                        data-testid="credits-view-sort-table-button-type"
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-1/4">
                    <Space size="md" className="justify-start items-center">
                      {t('TABLE_COLUMN_QUANTITY')}
                      <SortTableButton
                        sort="amount"
                        order={params.order}
                        currentSort={params.sort}
                        data-testid="credits-view-sort-table-button-amount"
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-1/4">
                    <Space size="md" className="justify-start items-center">
                      {t('TABLE_COLUMN_ORIGIN')}
                      <SortTableButton
                        sort="origin"
                        order={params.order}
                        currentSort={params.sort}
                        data-testid="credits-view-sort-table-button-origin"
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((creditMovement) => (
                  <TableRow key={creditMovement.id} data-testid="credits-list-heading">
                    <TableCell>
                      {formatDate(new Date(creditMovement.createdAt), language, {
                        timeStyle: 'medium',
                        hourCycle: 'h24',
                      })}
                    </TableCell>
                    <TableCell className="flex justify-start items-center">
                      <Tag color={creditMovement.type === 'in' ? 'green' : 'red'}>
                        {t(TYPE_ENUM[creditMovement.type]) || creditMovement.type}
                      </Tag>
                    </TableCell>
                    <TableCell>
                      {['transcription'].includes(serviceType)
                        ? creditMovement.amount > 0
                          ? convertSecondsToTimeString(creditMovement.amount)
                          : '0s'
                        : formatNumber(creditMovement.amount)}
                    </TableCell>
                    <TableCell>{t(ORIGIN_ENUM[creditMovement.origin]) || creditMovement.origin}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>
    </>
  )
}

export { ViewCredits }
