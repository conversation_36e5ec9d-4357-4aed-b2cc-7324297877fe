import React from 'react'
import { <PERSON><PERSON>, <PERSON>alogB<PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import ticketTopicsApi, { type TicketTopicsData, ticketTopicsQueryKey } from '../../api/ticket-topics'
import { TicketTopicsForm } from './components/form'

const CreateTicketTopics = () => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: TicketTopicsData) => ticketTopicsApi.create(values),
    onSuccess: () => {
      toast.success(t('TICKET_TOPIC_ADDED_SUCCESS'))

      queryClient.invalidateQueries({
        queryKey: [ticketTopicsQueryKey, 'list'],
      })

      history.goBack()
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { message } = error?.response?.data ?? {}
      const errorMap = {
        'There already exists a resource with same fields name,accountId.': () => t('TICKET_TOPIC_SAME_NAME_ERROR'),
      }
      toast.error(errorMap[message]?.() || t('TICKET_TOPIC_ADDED_ERROR'))
    },
  })

  return (
    <>
      <Helmet title={`${t('common:LABEL_CREATING')} ${t('TICKET_TOPIC').toLowerCase()}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.goBack()
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('ADD_TICKET_TOPIC')}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <TicketTopicsForm
              onCancel={() => history.goBack()}
              onSubmit={(values) => mutateAsync(values)}
              isPending={isPending}
            />
          </DialogBody>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { CreateTicketTopics }
