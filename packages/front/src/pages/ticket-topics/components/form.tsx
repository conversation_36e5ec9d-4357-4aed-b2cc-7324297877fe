import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import z from 'zod'
import { Form, FormControl, FormField, FormItem, FormMessage } from '../../../app/components/common/unconnected/ui/form'
import { InputSkeleton } from '../../../components/unconnected/skeletons/input-skeleton'
import { Button, InputText, Label, Space } from '@ikatec/nebula-react'
import { LoaderIcon } from 'lucide-react'
import { TicketTopicsData } from '../../../api/ticket-topics'

interface TicketTopicsFormProps {
  onCancel: VoidFunction
  onSubmit: (values: TicketTopicsData) => void
  defaultValues?: Partial<TicketTopicsData>
  isPending?: boolean
  isFetching?: boolean
}

const formSchema = z.object({
  name: z
    .string()
    .trim()
    .min(1, { message: 'ticketTopicsPage:TICKET_TOPIC_NAME_REQUIRED' })
    .max(255, { message: `ticketTopicsPage:TICKET_TOPIC_NAME_MAX_LENGTH` }),
})

interface FormValues extends z.infer<typeof formSchema> {}

const TicketTopicsForm = ({ onCancel, onSubmit, defaultValues, isPending, isFetching }: TicketTopicsFormProps) => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])

  const formContext = useForm<FormValues>({
    defaultValues: {
      name: '',
      ...defaultValues,
    },
    resetOptions: {
      keepDefaultValues: true,
    },
    shouldFocusError: true,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(formSchema),
  })

  const handleSubmit = async (values: TicketTopicsData) => {
    onSubmit(values)
  }

  useEffect(() => {
    if (!defaultValues) return

    formContext.reset(defaultValues)
  }, [defaultValues])

  if (isFetching) {
    return (
      <div>
        <div className="w-full">
          <InputSkeleton label={t('common:LABEL_NAME')} />
        </div>

        <Space className="w-full mt-10">
          <Button className="w-full" variant="secondary" type="button" onClick={onCancel}>
            {t('common:FORM_ACTION_CANCEL')}
          </Button>
          <Button className="w-full" variant="primary" type="submit" disabled>
            {isPending ? <LoaderIcon className="animate-spin" /> : null}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </Space>
      </div>
    )
  }

  return (
    <Form {...formContext}>
      <form onSubmit={formContext.handleSubmit(handleSubmit)}>
        <div className="w-full">
          <FormField
            name="name"
            control={formContext.control}
            render={({ field, fieldState }) => {
              return (
                <FormItem>
                  <FormControl>
                    <>
                      <Label htmlFor="name">{t('common:LABEL_NAME')}</Label>
                      <InputText
                        data-testid="ticket-topics-form-input-name"
                        type="text"
                        id="name"
                        name="name"
                        isError={Boolean(fieldState?.error?.message)}
                        {...field}
                      />
                    </>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )
            }}
          />
        </div>
        <Space className="w-full mt-10">
          <Button
            className="w-full"
            variant="secondary"
            type="button"
            onClick={onCancel}
            data-testid="ticket-topics-form-button-cancel"
          >
            {t('common:FORM_ACTION_CANCEL')}
          </Button>
          <Button
            className="w-full"
            variant="primary"
            type="submit"
            disabled={isPending || !formContext.formState.isValid}
            data-testid="ticket-topics-form-button-confirm"
          >
            {isPending && <LoaderIcon className="animate-spin" />}
            {t('common:BUTTON_TEXT_CONFIRM')}
          </Button>
        </Space>
      </form>
    </Form>
  )
}

export { TicketTopicsForm }
