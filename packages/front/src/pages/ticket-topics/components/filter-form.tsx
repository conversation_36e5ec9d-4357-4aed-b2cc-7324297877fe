import React, { useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { TicketTopicsFilters } from '../../../api/ticket-topics'
import { Form, FormControl, FormField, FormItem } from '../../../app/components/common/unconnected/ui/form'
import {
  Label,
  Select,
  DrawerBody,
  DrawerFooter,
  DrawerClose,
  Button,
  Space,
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { archiveOptions } from '../constants'

interface TicketTopicsFilterFormValues {
  archivedAt: { value: string; label: string } | null
}

interface FilterFormProps {
  onSubmit: (data: TicketTopicsFilterFormValues) => void
  defaultValues?: TicketTopicsFilters
  open?: boolean
  onOpenChange?: (openChange: boolean) => void
}

const FilterForm = ({ onSubmit, defaultValues, onOpenChange, open }: FilterFormProps) => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])

  const initialValues = useMemo(
    () => ({
      archivedAt: defaultValues?.archivedAt
        ? {
            value: defaultValues.archivedAt,
            label: t(archiveOptions.find((option) => option.value === defaultValues.archivedAt)?.label || ''),
          }
        : null,
    }),
    [defaultValues],
  )

  const form = useForm<TicketTopicsFilterFormValues>({
    defaultValues: initialValues,
  })

  const handleSubmit = useCallback((data: TicketTopicsFilterFormValues) => {
    console.log('Form submitted with data:', data)
    onSubmit(data)
  }, [])

  const handleClearDrawerFilters = useCallback(() => {
    form.reset({ archivedAt: null })
  }, [form])

  const handleInitDrawer = useCallback(() => {
    form.reset()

    if (!open || !initialValues) return

    if (initialValues.archivedAt?.value) {
      form.setValue('archivedAt', initialValues.archivedAt, { shouldDirty: true })
    }
  }, [open, initialValues])

  useEffect(() => {
    handleInitDrawer()
  }, [handleInitDrawer])

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerContent side="right">
        <DrawerHeader
          extraActions={
            form.formState.isDirty && (
              <Button size="sm" variant="ghost" onClick={handleClearDrawerFilters}>
                {t('common:BUTTON_TEXT_CLEAR')}
              </Button>
            )
          }
        >
          <DrawerTitle>{t('common:BUTTON_TEXT_FILTERS')}</DrawerTitle>
        </DrawerHeader>

        {/* DrawerBody and DrawerFooter to handle form submit */}
        <DrawerBody>
          <Form {...form}>
            <form>
              <FormField
                name="archivedAt"
                control={form.control}
                render={({ field }) => {
                  return (
                    <FormItem>
                      <FormControl>
                        <>
                          <Label htmlFor="archivedAt">{t('common:STATUS')}</Label>
                          <Select
                            id="archivedAt"
                            name="archivedAt"
                            placeholder={t('common:SELECT_OPTION_PLACEHOLDER_TEXT')}
                            options={archiveOptions.map(({ label, value }) => ({ value, label: t(label) }))}
                            isClearable
                            data-testid="ticket-topics-filter-form-select-archived-at"
                            {...field}
                          />
                        </>
                      </FormControl>
                    </FormItem>
                  )
                }}
              />
            </form>
          </Form>
        </DrawerBody>
        <DrawerFooter>
          <Space className="w-full">
            <DrawerClose asChild>
              <Button className="w-full" variant="secondary" data-testid="ticket-topics-filter-form-button-cancel">
                {t('common:FORM_ACTION_CANCEL')}
              </Button>
            </DrawerClose>

            <Button
              className="w-full"
              onClick={form.handleSubmit(handleSubmit)}
              data-testid="ticket-topics-filter-form-button-confirm"
            >
              {t('common:BUTTON_TEXT_SIMPLE_SUBMIT_FILTERS')}
            </Button>
          </Space>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}

export { FilterForm, type TicketTopicsFilterFormValues }
