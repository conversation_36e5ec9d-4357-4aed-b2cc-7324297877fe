import React from 'react'
import { <PERSON><PERSON>, DialogBody, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON>Header, DialogTitle, toast } from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { useEffect } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory, useParams } from 'react-router'
import ticketTopicsApi, { type TicketTopicsData, ticketTopicsQueryKey } from '../../api/ticket-topics'
import { TicketTopicsForm } from './components/form'

const EditTicketTopics = () => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching } = useQuery({
    queryKey: [ticketTopicsQueryKey, id],
    queryFn: () => ticketTopicsApi.getById(id),
    enabled: Bo<PERSON>an(id),
  })
  const history = useHistory()

  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: TicketTopicsData) => ticketTopicsApi.update(id, values),
    onSuccess: async () => {
      toast.success(t('TICKET_TOPIC_EDITED_SUCCESS'))

      await Promise.all([
        queryClient.invalidateQueries({
          queryKey: [ticketTopicsQueryKey, 'list'],
        }),
        queryClient.invalidateQueries(
          {
            queryKey: [ticketTopicsQueryKey, id],
          },
          { cancelRefetch: true },
        ),
      ])

      history.goBack()
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      const { message } = error?.response?.data ?? {}
      const errorMap = {
        'There already exists a resource with same fields name,accountId.': () => t('TICKET_TOPIC_SAME_NAME_ERROR'),
      }
      toast.success(errorMap[message]?.() || t('TICKET_TOPIC_EDITED_ERROR'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.success(t('TICKET_TOPIC_NOT_FOUND'))
      return history.replace('/ticket-topics')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${t('common:LABEL_EDITING')} ${t('TICKET_TOPIC').toLocaleLowerCase()} - ${data?.name}`} />
      <Dialog
        open
        onOpenChange={(open) => {
          if (!open) history.goBack()
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('EDIT_TICKET_TOPIC')}</DialogTitle>
          </DialogHeader>
          <DialogBody>
            <TicketTopicsForm
              onCancel={() => history.goBack()}
              onSubmit={(values) => mutateAsync(values)}
              isPending={isPending}
              isFetching={isFetching}
              defaultValues={data}
            />
          </DialogBody>
        </DialogContent>
      </Dialog>
    </>
  )
}

export { EditTicketTopics }
