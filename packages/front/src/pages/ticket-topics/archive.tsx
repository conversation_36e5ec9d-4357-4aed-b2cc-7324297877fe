import React, { useEffect } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { type AxiosError } from 'axios'
import { AlertTriangleIcon, InfoIcon, LoaderIcon } from 'lucide-react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import ticketTopicsApi, { ticketTopicsQueryKey } from '../../api/ticket-topics'
import { useHistory, useParams } from 'react-router'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const ArchiveTicketTopics = () => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])
  const queryClient = useQueryClient()
  const history = useHistory()

  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [ticketTopicsQueryKey, id],
    queryFn: () => ticketTopicsApi.getById(id),
    enabled: Boolean(id),
  })

  const isArchived = !!data?.archivedAt

  const labelArchived = !isArchived ? t('common:LABEL_ARCHIVE') : t('common:LABEL_UNARCHIVE')

  const { mutateAsync, isPending } = useMutation({
    mutationFn: () => {
      return ticketTopicsApi.archive(id, !isArchived)
    },
    onSuccess: async (res) => {
      toast.success(res?.archivedAt ? t('TICKET_TOPIC_ARCHIVED_SUCCESS') : t('TICKET_TOPIC_UNARCHIVE_SUCCESS'))
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [ticketTopicsQueryKey, 'list'] }),
        queryClient.invalidateQueries({ queryKey: [ticketTopicsQueryKey, data?.id] }),
      ])
    },
    onError: (error: AxiosError<{ message?: string }>) => {
      toast.error(t('LIST_IS_ERROR_DESCRIPTION'))
    },
  })

  useEffect(() => {
    if (isError) {
      toast.error(t('TICKET_TOPIC_NOT_FOUND'))
      return history.replace('/ticket-topics')
    }
  }, [isError])

  return (
    <>
      <Helmet title={`${labelArchived} - ${data?.name}`} />

      <AlertDialog open onOpenChange={(open) => !open && history.goBack()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            {isArchived && <InfoIcon size={40} className="text-primary-800 dark:text-neutral-600" />}
            {!isArchived && <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />}

            {isFetching && (
              <Space size="lg" direction="column" className="w-full items-center justify-center">
                <Skeleton width="50%" height="24px" />
                <Skeleton width="80%" height="24px" />
              </Space>
            )}

            {!isFetching && isSuccess && (
              <>
                <AlertDialogTitle>
                  {isArchived ? t('ACTIVE_TICKET_TOPICS') : t('ARCHIVE_TICKET_TOPIC')}
                </AlertDialogTitle>

                <AlertDialogDescription>
                  {!isArchived ? t('ARE_YOU_SURE_YOU_WANT_TO_ARCHIVE') : t('ARE_YOU_SURE_YOU_WANT_TO_UNARCHIVE')}
                </AlertDialogDescription>
              </>
            )}
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel className="w-full" data-testid="ticket-topics-archive-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </AlertDialogCancel>

            <AlertDialogAction
              variant={isArchived ? 'primary' : 'danger'}
              className="w-full"
              onClick={() => mutateAsync()}
              disabled={isPending || isFetching}
              data-testid="ticket-topics-archive-button-confirm"
            >
              {isPending && <LoaderIcon className="animate-spin" />}
              {t('common:BUTTON_TEXT_CONFIRM')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export { ArchiveTicketTopics }
