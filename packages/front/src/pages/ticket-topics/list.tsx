import {
  Badge,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tag,
  Heading,
} from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import {
  BanIcon,
  CheckCircle2Icon,
  EllipsisVerticalIcon,
  EyeIcon,
  FilterIcon,
  PencilIcon,
  PlusIcon,
} from 'lucide-react'
import React, { memo, useMemo } from 'react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import { useHistory } from 'react-router'
import ticketTopicsApi, { ticketTopicsQueryKey, type TicketTopicsFilters } from '../../api/ticket-topics'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import useToggle from '../../app/hooks/useToggle'
import { FiltersTableTags, FiltersTagList } from '../../components/unconnected/table/filters-tag-list'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { useSearchParams } from '../../hooks/use-search-params'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { debounceFn } from '../../utils/debouce-fn'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { TicketTopicsFilterFormValues, FilterForm } from './components/filter-form'
import { archiveOptions } from './constants'
import { truncateText } from '../../app/utils/truncate-text'

const ListTicketTopics = () => {
  const history = useHistory()
  const {
    isOpen: filterDialogIsOpen,
    toggle: toggleFilterDialog,
    open: openFilterDialog,
    close: closeFilterDialog,
  } = useToggle(false)

  const countableFilters: (keyof TicketTopicsFilters)[] = ['archivedAt']

  const { params, setValues, set, hasFilter, clear, searchParams, totalFilters, remove } =
    useSearchParams<TicketTopicsFilters>(
      {
        order: 'ASC',
        sort: 'name',
        page: 1,
      },
      countableFilters,
    )

  const { t } = useTranslation(['ticketTopicsPage', 'common'])

  const queryClient = useQueryClient()

  const {
    data: ticketTopics,
    isFetching,
    isSuccess,
    isError,
    refetch,
  } = useQuery({
    queryKey: [ticketTopicsQueryKey, 'list', params],
    queryFn: () => ticketTopicsApi.getAll(params),
    placeholderData() {
      return getPlaceholderData(
        queryClient.getQueriesData<typeof ticketTopics>({ queryKey: [ticketTopicsQueryKey, 'list'] }),
      )
    },
  })

  const { currentPage, data = [], limit, total } = ticketTopics ?? {}

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/ticket-topics/create', search: searchParams }))
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/ticket-topics/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/ticket-topics/${id}/edit`, search: searchParams }))
  }

  const navigateToArchive = (id: string) => {
    setTimeout(() => history.push({ pathname: `/ticket-topics/${id}/archive`, search: searchParams }))
  }

  const handleClearInputFilter = () => {
    remove(['page', 'name', 'order', 'sort'])
  }

  const handleSetFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      set('name', value)
      remove(['page', 'order', 'sort'])
    })
  }

  const handleClearDrawerFilters = () => {
    remove(['archivedAt', 'page'])
    closeFilterDialog()
  }

  const handleSubmitDrawerFilters = (values: TicketTopicsFilterFormValues) => {
    const { archivedAt } = values

    const keysToRemove: (keyof TicketTopicsFilters)[] = ['page', 'order', 'sort']
    if (archivedAt?.value) {
      set('archivedAt', archivedAt.value)
    } else {
      keysToRemove.push('archivedAt')
    }

    remove(keysToRemove)
    closeFilterDialog()
  }

  const drawerFiltersList = useMemo<FiltersTableTags>(() => {
    const getLabelByFilterName = (name: string, value: string) => {
      if (name === 'archivedAt') {
        return archiveOptions.find((option) => option.value === value)?.label || ''
      }
    }

    const getNameByFilterName = (name: string) => {
      if (name === 'archivedAt') {
        return t('common:STATUS')
      }
      return name
    }

    return Object.entries(params).reduce((acc, prev) => {
      const [name, label] = prev
      if (countableFilters.includes(prev[0] as any)) {
        acc.push({
          filter: name,
          name: t(getNameByFilterName(name) ?? name),
          label: t(getLabelByFilterName(name, label.toString()) ?? label.toString()),
        })
      }
      return acc
    }, [] as FiltersTableTags)
  }, [params, t])

  return (
    <>
      <Helmet title={t('ticketTopicsPage:TICKET_TOPICS')} />

      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="ticket-topics-list-heading">
              {t('ticketTopicsPage:TICKET_TOPICS')}
            </Heading>

            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="ticket-topics-list-input-filter"
                name="label"
                defaultValue={params.name}
                onChange={(e) => handleSetFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />

              <Button variant="secondary" onClick={openFilterDialog} data-testid="ticket-topics-list-button-filter">
                <FilterIcon />
                {t('common:BUTTON_TEXT_FILTERS')}
                {totalFilters ? <Badge variant="secondary">{totalFilters}</Badge> : null}
              </Button>

              <FilterForm
                defaultValues={params}
                onSubmit={handleSubmitDrawerFilters}
                open={filterDialogIsOpen}
                onOpenChange={toggleFilterDialog}
              />

              <IfUserCan permission="ticketTopics.create">
                <Button size="md" data-testid="ticket-topics-list-button-add" onClick={navigateToCreate}>
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>

        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {isSuccess && !isFetching && hasFilter && (
            <FiltersTagList
              onClear={handleClearDrawerFilters}
              onDeleteFilter={(filter: keyof TicketTopicsFilters) => remove([filter])}
              filters={drawerFiltersList}
            />
          )}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && data?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && data?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundWithoutFilter
              onAdd={navigateToCreate}
              moduleTitle={t('ticketTopicsPage:NO_TICKET_TOPICS_CREATED')}
              moduleDescription={t('ticketTopicsPage:START_CREATE_FIRST_TICKET_TOPIC')}
            />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && data?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Space size="md" className="justify-start items-center">
                      {t('common:LABEL_NAME')}
                      <SortTableButton
                        sort="name"
                        order={params.order}
                        currentSort={params.sort}
                        data-testid="ticket-topics-list-sort-label"
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-36">
                    <Space size="md" className="justify-start items-center">
                      {t('common:STATUS')}
                      <SortTableButton
                        sort="archivedAt"
                        order={params.order}
                        currentSort={params.sort}
                        data-testid="ticket-topics-list-sort-archived"
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head,
                          })
                        }}
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[72px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((ticketTopic, index) => {
                  return (
                    <TableRow key={ticketTopic.id}>
                      <TableCell>{truncateText(ticketTopic.name, 80)}</TableCell>
                      <TableCell className="flex justify-start items-center w-36">
                        <Tag color={ticketTopic.archivedAt ? 'gray' : 'green'}>
                          {!ticketTopic.archivedAt ? t('common:LABEL_UNARCHIVED') : t('common:LABEL_ARCHIVED')}
                        </Tag>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              icon
                              variant="ghost"
                              size="sm"
                              data-testid={`ticket-topics-list-button-actions-${index}`}
                            >
                              <EllipsisVerticalIcon />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              data-testid={`ticket-topics-list-button-actions-${index}-view`}
                              onClick={() => navigateToView(ticketTopic.id)}
                            >
                              <EyeIcon />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </DropdownMenuItem>

                            <IfUserCan permission="ticketTopics.update">
                              <DropdownMenuItem
                                data-testid={`ticket-topics-list-button-actions-${index}-edit`}
                                onClick={() => navigateToEdit(ticketTopic.id)}
                              >
                                <PencilIcon />
                                {t('common:ACTIONS_SUBMENU_EDIT')}
                              </DropdownMenuItem>
                            </IfUserCan>

                            <IfUserCan permission="ticketTopics.archive">
                              <DropdownMenuItem
                                data-testid={`ticket-topics-list-button-actions-${index}-archive`}
                                onClick={() => {
                                  navigateToArchive(ticketTopic.id)
                                }}
                              >
                                {ticketTopic.archivedAt ? <CheckCircle2Icon /> : <BanIcon />}
                                {ticketTopic.archivedAt
                                  ? t('common:LABEL_UNARCHIVE')
                                  : t('common:ACTIONS_SUBMENU_ARCHIVE')}
                              </DropdownMenuItem>
                            </IfUserCan>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}

          {/* if has data and isSuccessful, render pagination */}
          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>
    </>
  )
}

const memoizedListTicketTopics = memo(ListTicketTopics, () => true)

export { memoizedListTicketTopics as ListTicketTopics }
