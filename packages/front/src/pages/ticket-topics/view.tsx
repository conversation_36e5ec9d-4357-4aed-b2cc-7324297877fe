import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { useHistory, useParams } from 'react-router'
import ticketTopicsApi, { ticketTopicsQueryKey } from '../../api/ticket-topics'
import {
  Dialog,
  DialogBody,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
  Button,
  Space,
  toast,
  Tag,
  Paragraph,
} from '@ikatec/nebula-react'
import { useTranslation } from 'react-i18next'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import { useEffect } from 'react'

const ViewTicketTopics = () => {
  const { t } = useTranslation(['ticketTopicsPage', 'common'])
  const history = useHistory()
  const { id } = useParams<{ id: string }>()
  const { data, isError, isFetching, isSuccess } = useQuery({
    queryKey: [ticketTopicsQueryKey, id],
    queryFn: () => ticketTopicsApi.getById(id),
    enabled: Boolean(id),
  })

  useEffect(() => {
    if (isError) {
      toast.info(t('TICKET_TOPIC_NOT_FOUND'))
      return history.replace('/ticket-topics')
    }
  }, [isError])

  return (
    <Dialog
      open
      onOpenChange={(open) => {
        if (!open) history.goBack()
      }}
    >
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('VIEW_TICKET_TOPIC')}</DialogTitle>
        </DialogHeader>
        <DialogBody>
          {isFetching && (
            <Space size="md">
              <div className="flex items-center gap-1 flex-1">
                <Paragraph size="sm" className="font-semibold">
                  {t('common:LABEL_NAME')}:
                </Paragraph>
                <p>{<Skeleton width="180px" height="24px" />}</p>
              </div>
              <div className="flex items-center gap-1 flex-1">
                <Paragraph size="sm" className="font-semibold">
                  {t('common:STATUS')}:
                </Paragraph>
                <p>{<Skeleton width="180px" height="24px" />}</p>
              </div>
            </Space>
          )}
          {isSuccess && !isFetching && (
            <Space size="md" className="flex items-center justify-center">
              <div className="flex items-baseline gap-1 flex-1">
                <Paragraph size="sm" className="font-semibold">
                  {t('common:LABEL_NAME')}:
                </Paragraph>
                <Paragraph size="sm" className="max-w-64 break-words">
                  {data.name}
                </Paragraph>
              </div>
              <div className="flex items-center gap-1 flex-1">
                <Paragraph size="sm" className="font-semibold">
                  {t('common:STATUS')}:
                </Paragraph>
                <Tag color={data.archivedAt ? 'gray' : 'green'}>
                  {!data.archivedAt ? t('common:LABEL_UNARCHIVED') : t('common:LABEL_ARCHIVED')}
                </Tag>
              </div>
            </Space>
          )}
        </DialogBody>
        <DialogFooter>
          <DialogClose asChild className="w-full">
            <Button variant="secondary" data-testid="ticket-topics-view-button-close">
              {t('common:FORM_ACTION_CLOSE')}
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export { ViewTicketTopics }
