// COPY_ROLES_PERMISSIONS_DIALOG_WARNING
// ARE_YOU_SURE_TO_CONTINUE

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertProps,
} from '@ikatec/nebula-react'
import React from 'react'
import { useTranslation } from 'react-i18next'

interface DuplicateRolesPermissionConfirmationDialogProps extends Pick<AlertProps, 'open' | 'onOpenChange'> {
  onConfirm: VoidFunction
  onCancel: VoidFunction
}

const DuplicateRolesPermissionConfirmationDialog = ({
  onCancel,
  onConfirm,
  onOpenChange,
  open,
}: DuplicateRolesPermissionConfirmationDialogProps) => {
  const { t } = useTranslation(['rolesPage', 'common'])

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('common:BUTTON_DISCARD_CHANGES')}</AlertDialogTitle>

          <AlertDialogDescription>{t('rolesPage:COPY_ROLES_PERMISSIONS_DIALOG_WARNING')}</AlertDialogDescription>
          <AlertDialogDescription>{t('common:ARE_YOU_SURE_TO_CONTINUE')}</AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel
            className="w-full"
            data-testid="roles-duplicate-permission-dialog-button-cancel"
            onClick={onCancel}
          >
            {t('common:FORM_ACTION_CANCEL')}
          </AlertDialogCancel>

          <AlertDialogAction
            variant="primary"
            className="w-full"
            onClick={onConfirm}
            data-testid="roles-duplicate-permission-dialog-button-confirm"
          >
            {t('common:BUTTON_TEXT_CONFIRM')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

export { DuplicateRolesPermissionConfirmationDialog }
