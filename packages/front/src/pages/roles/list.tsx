import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Heading,
  Pagination,
  Space,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@ikatec/nebula-react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Helmet } from 'react-helmet'
import { useHistory } from 'react-router'
import {
  CopyIcon,
  EllipsisVerticalIcon,
  ExternalLinkIcon,
  EyeIcon,
  PencilIcon,
  PlusIcon,
  Trash2Icon,
} from 'lucide-react'
import rolesApi, { RolesFilters, rolesQueryKey } from '../../api/roles'
import { useSearchParams } from '../../hooks/use-search-params'
import { getPlaceholderData } from '../../utils/get-placeholder-data'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { InputFilter } from '../../components/unconnected/table/input-filter'
import { debounceFn } from '../../utils/debouce-fn'
import IfUserCan from '../../app/components/common/connected/IfUserCan'
import { TableSkeleton } from '../../components/unconnected/table/table-skeleton'
import { NoPossibleLoadData } from '../../components/unconnected/table/no-possible-load-data'
import { NoResultsFoundByFilter } from '../../components/unconnected/table/no-results-found-by-filter'
import { NoResultsFoundWithoutFilter } from '../../components/unconnected/table/no-results-found-without-filter'
import { SortTableButton } from '../../components/unconnected/table/sort-table-button'

const RolesList = () => {
  const history = useHistory()
  const queryClient = useQueryClient()
  const { params, remove, set, searchParams, hasFilter, clear, setValues } = useSearchParams<RolesFilters>({
    sort: 'displayName',
    order: 'ASC',
  })
  const { t } = useTranslation(['rolesPage', 'common', 'usersPage'])

  const { data, isFetching, isSuccess, isError, refetch } = useQuery({
    queryKey: [rolesQueryKey, 'list', params],
    queryFn: () => rolesApi.getAll({ ...params, customInclude: ['usersCount'] }),
    placeholderData() {
      return getPlaceholderData(queryClient.getQueriesData<typeof data>({ queryKey: [rolesQueryKey, 'list'] }))
    },
  })

  const handleClearInputFilter = () => {
    remove(['page', 'order', 'sort', 'displayName'])
  }

  const handleSetInputFilter = (value: string) => {
    debounceFn(() => {
      if (!value) {
        handleClearInputFilter()
        return
      }
      remove(['page', 'order', 'sort'])
      set('displayName', value)
    })
  }

  const navigateToView = (id: string) => {
    setTimeout(() => history.push({ pathname: `/roles/${id}`, search: searchParams }))
  }

  const navigateToEdit = (id: string) => {
    setTimeout(() => history.push({ pathname: `/roles/${id}/edit`, search: searchParams }))
  }

  const navigateToDelete = (id: string) => {
    setTimeout(() => history.push({ pathname: `/roles/${id}/delete`, search: searchParams }))
  }

  const navigateToCreate = () => {
    setTimeout(() => history.push({ pathname: '/roles/create', search: searchParams }))
  }

  const handleDuplicateRole = (id: string) => {
    history.push('/roles/create', {
      duplicateRoleId: id,
    })
  }

  const navigateToUsersList = (id: string, displayName: string) => {
    history.push({
      pathname: '/users',
      search: new URLSearchParams({ role: id, 'role-label': displayName }).toString(),
    })
  }
  const { currentPage, data: roles = [], limit, total } = data ?? {}

  return (
    <>
      <Helmet title={t('rolesPage:TITLE_ROLES_PLURAL')} />

      <Container>
        <ContainerHeader>
          <div className="flex justify-between items-center">
            <Heading level="1" data-testid="roles-list-heading">
              {t('rolesPage:TITLE_ROLES_PLURAL')}
            </Heading>

            <Space direction="row" size="lg" className="items-center">
              <InputFilter
                data-testid="roles-list-input-filter"
                name="displayName"
                defaultValue={params.displayName}
                onChange={(e) => handleSetInputFilter(e.target.value)}
                onClean={handleClearInputFilter}
              />

              <IfUserCan permission="roles.create">
                <Button size="md" data-testid="roles-list-button-create" onClick={navigateToCreate}>
                  <PlusIcon />
                  {t('common:LIST_ADD_NEW')}
                </Button>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>
        <ContainerContent>
          {/* if is fetching another page or is initial fetch */}
          {isFetching && <TableSkeleton />}

          {/* If not isFetching and isError, render error feedback */}
          {isError && <NoPossibleLoadData onRefresh={refetch} />}

          {/* If no data and is successful with filters, render empty state */}
          {!isFetching && roles?.length === 0 && isSuccess && hasFilter && (
            <NoResultsFoundByFilter onClearFilter={clear} />
          )}

          {/* If no data and is successful without filters */}
          {!isFetching && roles?.length === 0 && isSuccess && !hasFilter && (
            <NoResultsFoundWithoutFilter
              onAdd={navigateToCreate}
              moduleTitle={t('rolesPage:TITLE_ROLES').toLocaleLowerCase()}
              moduleDescription={t('rolesPage:TITLE_ROLES').toLocaleLowerCase()}
            />
          )}

          {/* If has data and is successful, render table */}
          {isSuccess && roles?.length > 0 && !isFetching && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Space size="md" className="justify-start items-center">
                      {t('rolesPage:TABLE_ROLES_COLUMN_NAME')}
                      <SortTableButton
                        sort="displayName"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head as RolesFilters['sort'],
                          })
                        }}
                        data-testid="roles-list-sortButton-displayName"
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[120px] ">
                    <Space size="md" className="justify-start items-center">
                      {t('usersPage:TITLE_USERS')}
                      <SortTableButton
                        sort="usersCount"
                        order={params.order}
                        currentSort={params.sort}
                        onChange={(head, order) => {
                          setValues({
                            ...params,
                            order,
                            sort: head as RolesFilters['sort'],
                          })
                        }}
                        data-testid="roles-list-sortButton-usersCount"
                      />
                    </Space>
                  </TableHead>
                  <TableHead className="w-[72px] "></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {roles?.map((role, i) => {
                  return (
                    <TableRow key={role.id}>
                      <TableCell>{role.displayName}</TableCell>
                      <TableCell>{role.usersCount}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button icon variant="ghost" size="sm" data-testid={`roles-list-button-actions-${i}`}>
                              <EllipsisVerticalIcon />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              data-testid={`roles-list-button-actions-${i}-view`}
                              onClick={() => navigateToView(role.id)}
                            >
                              <EyeIcon />
                              {t('common:ACTIONS_SUBMENU_VIEW')}
                            </DropdownMenuItem>
                            <IfUserCan permission="users.view">
                              <DropdownMenuItem
                                data-testid={`roles-list-button-actions-${i}-view-users`}
                                onClick={() => navigateToUsersList(role.id, role.displayName)}
                              >
                                <ExternalLinkIcon />
                                {t('common:VIEW_LABEL')} {t('usersPage:TITLE_USERS').toLowerCase()}
                              </DropdownMenuItem>
                            </IfUserCan>
                            <IfUserCan permission="roles.create">
                              <DropdownMenuItem
                                data-testid={`roles-list-button-actions-${i}-duplicate`}
                                onClick={() => handleDuplicateRole(role.id)}
                              >
                                <CopyIcon />
                                {t('common:DUPLICATE_LABEL')}
                              </DropdownMenuItem>
                            </IfUserCan>
                            {!role.isAdmin && (
                              <>
                                <IfUserCan permission="roles.update">
                                  <DropdownMenuItem
                                    data-testid={`roles-list-button-actions-${i}-edit`}
                                    onClick={() => navigateToEdit(role.id)}
                                  >
                                    <PencilIcon />
                                    {t('common:ACTIONS_SUBMENU_EDIT')}
                                  </DropdownMenuItem>
                                </IfUserCan>
                                <IfUserCan permission="roles.destroy">
                                  <DropdownMenuItem
                                    onClick={() => navigateToDelete(role.id)}
                                    data-testid={`roles-list-button-actions-${i}-delete`}
                                  >
                                    <Trash2Icon />
                                    {t('common:LABEL_DELETE')}
                                  </DropdownMenuItem>
                                </IfUserCan>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          )}

          {/* if has data and isSuccessful, render pagination */}
          {Boolean(total && isSuccess) && (
            <Pagination
              page={currentPage}
              pageSize={limit}
              total={total}
              onChangePage={(page) => set('page', page.toString())}
            />
          )}
        </ContainerContent>
      </Container>
    </>
  )
}

export { RolesList }
