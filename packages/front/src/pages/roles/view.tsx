import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>read<PERSON>rumb<PERSON><PERSON>,
  <PERSON>readcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  Heading,
  Space,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AxiosError, HttpStatusCode } from 'axios'
import { ChevronLeftIcon, CopyIcon, EllipsisVerticalIcon, ExternalLinkIcon, TrashIcon } from 'lucide-react'
import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { Link, useHistory, useParams } from 'react-router-dom'
import { PermissionsModel } from '../../api/permissions'
import rolesApi, { RolesData, rolesQueryKey } from '../../api/roles'
import If<PERSON>ser<PERSON><PERSON> from '../../app/components/common/connected/IfUserCan'
import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'
import useToggle from '../../app/hooks/useToggle'
import { ConfirmDiscardFormChangesDialog } from '../../components/unconnected/confirm-discard-form-changes-dialog'
import { FormPageSkeleton } from '../../components/unconnected/skeletons/form-page-skeleton'
import { Container, ContainerContent, ContainerHeader } from '../../layouts/container'
import { RolesForm, RolesFormRef, RolesFormValues } from './components/form'
import { LightningLearningBox } from './components/lightning-learning-box'
import { Helmet } from 'react-helmet'

// don't forget to implements skeleton screen
const RolesView = () => {
  const { id } = useParams<{ id: string }>()
  const { t } = useTranslation(['rolesPage', 'common', 'usersPage'])

  const { data, isLoading, isError, isSuccess } = useQuery({
    queryKey: [rolesQueryKey, id],
    queryFn: () => rolesApi.getById(id, { include: ['permissions'] }),
    enabled: !!id,
  })
  const history = useHistory()
  const queryClient = useQueryClient()
  const { mutateAsync, isPending } = useMutation({
    mutationFn: (values: RolesData) => rolesApi.update(id, values),
    async onSuccess() {
      toast.success(t('EDIT_ROLES_SUCCESS'))
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [rolesQueryKey, 'list'] }),
        queryClient.invalidateQueries({ queryKey: [rolesQueryKey, id] }),
      ])
      history.replace('/roles')
    },
    onError(error: AxiosError) {
      const errorHandlingMap = {
        [HttpStatusCode.Unauthorized]: 'rolesPage:LABEL_ERROR_MESSAGE_DISPLAY',
      }

      return toast.error(t(errorHandlingMap[error.status] || 'common:MESSAGE_ERROR_SERVER_PROBLEM'))
    },
  })

  const handleMutate = (formValues: RolesFormValues) => {
    mutateAsync({
      displayName: formValues.name,
      permissions: [...formValues.permissions].map((v) => ({ id: v })) as PermissionsModel[],
    })
  }

  const defaultValues = useMemo<RolesFormValues>(
    () => ({
      name: data?.displayName,
      permissions: new Set(data?.permissions?.flatMap((p) => p.id) ?? []),
    }),
    [data],
  )

  const formRef = useRef<RolesFormRef>(null)
  const { isOpen: goBackAlertIsOpen, ...goBackAlertActions } = useToggle()

  const handleGoBack = useCallback(() => {
    if (formRef.current?.isDirty) {
      goBackAlertActions.open()
      return
    }

    history.replace('/roles')
  }, [])

  useEffect(() => {
    if (isError) {
      toast.error(t('common:MESSAGE_NOT_FOUND_ELEMENTS'))
      return history.replace('/roles')
    }
  }, [isError])

  const handleDuplicateRole = () => {
    history.push('/roles/create', {
      duplicateRoleId: id,
    })
  }

  const navigateToEdit = () => {
    history.push({ pathname: `/roles/${id}/edit` })
  }

  const navigateToUsersList = () => {
    history.push({
      pathname: '/users',
      search: new URLSearchParams({ role: id, 'role-label': data?.displayName }).toString(),
    })
  }

  const navigateToDelete = () => {
    history.push({ pathname: `/roles/${id}/delete` })
  }

  if (isLoading) {
    return (
      <FormPageSkeleton>
        <Skeleton className="w-full h-52" />
      </FormPageSkeleton>
    )
  }
  return (
    <>
      <Helmet title={`${t('common:DETAILS_LABEL')} ${t('common:LABEL_OF')}  ${t('TITLE_ROLES').toLowerCase()}`} />
      <Container>
        <ContainerHeader mtSize="sm">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <Link to="/roles" data-testid="roles-view-breadcrumb-link-to-roles-list">
                  {t('TITLE_ROLES_PLURAL')}
                </Link>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>
                  {t('common:DETAILS_LABEL')} {t('common:LABEL_OF')} {t('TITLE_ROLES').toLowerCase()}
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex items-center justify-between">
            <Space className="w-full items-center" size="sm">
              <Button
                icon
                variant="ghost"
                onClick={handleGoBack}
                aria-label="Go to edit roles' list"
                data-testid="roles-view-button-go-back"
                role="link"
              >
                <ChevronLeftIcon />
              </Button>
              <Heading level="1" data-testid="roles-view-heading">
                {t('common:DETAILS_LABEL')} {t('common:LABEL_OF')} {t('TITLE_ROLES').toLowerCase()}
              </Heading>
            </Space>
            <Space className="items-center" size="sm">
              {!data.isAdmin && (
                <IfUserCan permission="roles.update">
                  <Button
                    variant="secondary"
                    onClick={navigateToEdit}
                    aria-label="Go to edit roles' list"
                    data-testid="roles-view-button-edit"
                    role="link"
                  >
                    {t('common:ACTIONS_SUBMENU_EDIT')}
                  </Button>
                </IfUserCan>
              )}

              <IfUserCan permissions={['roles.create', 'roles.destroy', 'users.view']} any>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button icon variant="secondary" data-testid="roles-view-button-actions">
                      <EllipsisVerticalIcon />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-52">
                    <IfUserCan permission="roles.create">
                      <DropdownMenuItem onClick={handleDuplicateRole} data-testid="roles-view-button-actions-duplicate">
                        <CopyIcon />
                        {t('common:DUPLICATE_LABEL')}
                      </DropdownMenuItem>
                    </IfUserCan>
                    <IfUserCan permission="users.view">
                      <DropdownMenuItem
                        onClick={navigateToUsersList}
                        data-testid="roles-view-button-actions-view-users"
                      >
                        <ExternalLinkIcon />
                        {t('common:VIEW_LABEL')} {t('usersPage:TITLE_USERS').toLowerCase()}
                      </DropdownMenuItem>
                    </IfUserCan>
                    {!data.isAdmin && (
                      <IfUserCan permission="roles.destroy">
                        <DropdownMenuItem data-testid="roles-view-button-actions-destroy" onClick={navigateToDelete}>
                          <TrashIcon />
                          {t('common:LABEL_DELETE')}
                        </DropdownMenuItem>
                      </IfUserCan>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </IfUserCan>
            </Space>
          </div>
        </ContainerHeader>
        <ContainerContent>
          {isSuccess && (
            <Space className="gap-6">
              <div className="flex-1">
                <RolesForm
                  onSubmit={handleMutate}
                  isPending={isPending}
                  defaultValues={defaultValues}
                  ref={formRef}
                  disabled
                />
              </div>
              <aside>
                <LightningLearningBox />
              </aside>
            </Space>
          )}
        </ContainerContent>
      </Container>
      <ConfirmDiscardFormChangesDialog
        open={goBackAlertIsOpen}
        onOpenChange={goBackAlertActions.set}
        onContinue={() => history.replace('/roles')}
      />
    </>
  )
}

export { RolesView }
