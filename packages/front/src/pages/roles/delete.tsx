import React, { useEffect } from 'react'
import { useHistory, useParams } from 'react-router'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  toast,
} from '@ikatec/nebula-react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { AlertTriangleIcon, LoaderIcon } from 'lucide-react'
import { Helmet } from 'react-helmet'
import { useTranslation } from 'react-i18next'
import rolesApi, { rolesQueryKey } from '../../api/roles'

import { Skeleton } from '../../app/components/common/unconnected/ui/skeleton'

const RolesDelete = () => {
  const history = useHistory()
  const queryClient = useQueryClient()
  const { t } = useTranslation(['rolesPage', 'common'])

  const { id } = useParams<{ id: string }>()
  const { data, isFetching, isSuccess, isError } = useQuery({
    queryKey: [rolesQueryKey, id],
    queryFn: () => rolesApi.getById(id),
  })

  const { mutateAsync, isPending } = useMutation({
    mutationFn: () => {
      return rolesApi.delete(id)
    },
    onSuccess: async () => {
      toast.success(t('TOAST_DELETE_ROLES'))
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: [rolesQueryKey, 'list'] }),
        queryClient.invalidateQueries({ queryKey: [rolesQueryKey, data?.id] }),
      ])
    },
    onError: () => {
      toast.error(t('MODAL_MESSAGE_ERROR'))
    },
  })

  useEffect(() => {
    if (data?.isAdmin) {
      toast.error(
        `${t('CONTENT_MODAL_ACTION_ADM', {
          action: t('common:TEXT_EXCLUSION'),
        })}`,
      )
      return history.replace('/roles')
    }

    if (isError) {
      toast.error(t('ROLES_NOT_FOUND'))
      return history.replace('/roles')
    }
  }, [isError, data])

  return (
    <>
      <Helmet title={`${t('TITLE_ROLES_PLURAL')} - ${t('common:MODAL_DELETE_BUTTON_CONFIRM')}`} />
      <AlertDialog open onOpenChange={(open) => !open && history.replace('/roles')}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertTriangleIcon size={40} className="text-red-700 dark:text-neutral-600" />
            {isFetching && data?.isAdmin && (
              <>
                <Skeleton width="50%" height="24px" />
                <AlertDialogDescription>
                  <Skeleton width="80%" height="24px" />
                </AlertDialogDescription>
                <AlertDialogDescription>
                  <Skeleton width="50%" height="24px" />
                </AlertDialogDescription>
              </>
            )}
            {isSuccess && !data?.isAdmin && (
              <>
                <AlertDialogTitle>{`${t('common:LABEL_DELETE')} ${t('TITLE_ROLES')}`}</AlertDialogTitle>
                <AlertDialogDescription>{t('DELETE_DESCRIPTION')}</AlertDialogDescription>
                <AlertDialogDescription>{t('common:IRREVERSIBLE_ACTION_LABEL')}</AlertDialogDescription>
              </>
            )}
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel className="w-full" data-testid="roles-delete-dialog-button-cancel">
              {t('common:FORM_ACTION_CANCEL')}
            </AlertDialogCancel>

            <AlertDialogAction
              variant="danger"
              className="w-full"
              onClick={() => mutateAsync()}
              disabled={isPending || isFetching || data?.isAdmin}
              data-testid="roles-delete-dialog-button-confirm"
            >
              {isPending ? <LoaderIcon className="animate-spin" /> : null}
              {t('common:BUTTON_TEXT_CONFIRM')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}

export { RolesDelete }
