import React from 'react'
import { Provider as ReduxProvider } from 'react-redux'
import Helmet from 'react-helmet'
import ReactDOM<PERSON>erver from 'react-dom/server'
import { createStore } from 'redux'

import i18n from '../../../../client/i18n'
import PDFHTML from './PDFHTML'
import Ticket, { fetch } from '../../../../app/components/Pdf/Ticket'
import format from '../../../../app/utils/date/format'
import { getAxios } from '../../../../app/utils/apiResource'
import getTimezoneMinutesOffset from '../../../../../internal/utils/getTimezoneMinutesOffset'
import timezoneCls from './timezoneCls'
import { pageDictionary, generatedInDictionary } from './dictionary'
import { chain } from 'lodash'

export default async (request, response) =>
  timezoneCls.run(async () => {
    try {
      const { id, lng } = request.params
      const { pdf: renderAsPdf, withoutComments, webchatExport } = request.query
      const { authorization, impersonate = false } = request.headers
      const state = {}
      const store = createStore((s) => s, state)

      const { ticket, messages } = await fetch({
        id,
        authorization,
        impersonate,
        headers: {
          'x-real-ip-alternative': request.headers['x-real-ip-alternative'],
        },
        withoutComments: withoutComments || webchatExport,
      })
      const { account } = ticket

      const accountTimezoneOffset = getTimezoneMinutesOffset(account.settings.timezone)

      timezoneCls.set('accountTimezoneOffset', accountTimezoneOffset)

      i18n.changeLanguage(lng)

      const defaultTimestamp = format(new Date(1), 'dd/MM/yyyy')
      const messagesByDate = chain(messages)
        .groupBy((data) => format(new Date(data.timestamp), 'dd/MM/yyyy'))
        .map((messages, date) => ({ date, messages }))
        .filter(({ date }) => date !== defaultTimestamp)
        .value()

      const App = (
        <ReduxProvider store={store}>
          <div style={{ zoom: '200%', width: '100%' }}>
            <Ticket ticket={ticket} messagesByDate={messagesByDate} exportPdf webchatExport={webchatExport} />
          </div>
        </ReduxProvider>
      )

      const appString = ReactDOMServer.renderToStaticMarkup(App)

      const helmet = Helmet.rewind()

      const htmlString = `<!DOCTYPE html>${ReactDOMServer.renderToStaticMarkup(
        <PDFHTML helmet={helmet} reactAppString={appString} />,
      )}`

      if (!renderAsPdf) {
        response.status(200).type('html').send(htmlString)
        return
      }

      const Wrapper = (props) => (
        <div
          style={{
            fontSize: 10,
            fontFamily: 'Roboto, Helvetica Neue, Ubuntu, sans-serif',
            width: '100%',
          }}
        >
          {props.children}
        </div>
      )

      const Header = ({ title }) => (
        <div style={{ textAlign: 'center' }}>
          <b>{title}</b>
        </div>
      )

      const footerStyles = {
        container: {
          margin: '0 25px 0 25px',
          paddingTop: '10px',
        },
        left: {
          float: 'left',
          width: '50%',
        },
        right: {
          float: 'right',
          width: '50%',
          textAlign: 'right',
        },
        clearFix: {
          content: '',
          display: 'table',
          clear: 'both',
        },
      }

      const Footer = () => (
        <div style={footerStyles.container}>
          <div style={footerStyles.left}>{`${generatedInDictionary[lng]} ${format(new Date())}`}</div>
          <div style={footerStyles.right}>
            {pageDictionary[lng]} <span className="pageNumber" /> de <span className="totalPages" />
          </div>
        </div>
      )
      const pageTitle = helmet.title.toString().match(new RegExp('>(.*?)<'))[1]

      const headerHtmlString = ReactDOMServer.renderToStaticMarkup(
        <Wrapper>
          <Header title={pageTitle} />
        </Wrapper>,
      )
      const footerHtmlString = ReactDOMServer.renderToStaticMarkup(
        <Wrapper>
          <Footer />
        </Wrapper>,
      )

      const axios = await getAxios()

      const pdfStream = await axios.post(
        '/generate-pdf',
        {
          body: htmlString,
          header: headerHtmlString,
          footer: footerHtmlString,
          fileName: `ticket-${ticket.protocol}.pdf`,
        },
        {
          responseType: 'stream',
          headers: {
            ...axios.defaults.headers,
            'x-real-ip-alternative': request.headers['x-real-ip-alternative'],
          },
          baseURL: process.env.NODE_ENV == 'development' ? 'http://app-api:8080/v1' : process.env.API_URL,
        },
      )

      response.status(200).type('application/pdf').header('Content-disposition', 'inline; filename="ticket.pdf"')

      pdfStream.data.pipe(response)
    } catch (e) {
      response.status(e.status || 500).send('Internal error')
      console.error(e)
    }
  })

export const exportTerm = async (request, response) =>
  timezoneCls.run(async () => {
    try {
      const { id, language } = request.params
      const { authorization } = request.headers

      const config = {
        headers: { authorization },
      }

      const responseTerm = await getAxios().get(`/terms/${id}`, config)
      const term = responseTerm?.data

      const store = createStore((s) => s, {})
      const App = (
        <ReduxProvider store={store}>
          <div dangerouslySetInnerHTML={{ __html: term.text[language] }} />
        </ReduxProvider>
      )

      const appString = ReactDOMServer.renderToStaticMarkup(App)

      const helmet = Helmet.rewind()

      const htmlString = `<!DOCTYPE html>${ReactDOMServer.renderToStaticMarkup(
        <PDFHTML helmet={helmet} reactAppString={appString} />,
      )}`

      const Wrapper = (props) => (
        <div
          style={{
            fontSize: 10,
            fontFamily: 'Roboto, Helvetica Neue, Ubuntu, sans-serif',
            width: '100%',
          }}
        >
          {props.children}
        </div>
      )

      const Header = ({ title }) => (
        <div style={{ textAlign: 'center' }}>
          <b>{title}</b>
        </div>
      )

      const headerHtmlString = ReactDOMServer.renderToStaticMarkup(
        <Wrapper>
          <Header title={term.title[language]} />
        </Wrapper>,
      )

      const pdfStream = await getAxios().post(
        '/generate-pdf',
        {
          body: htmlString,
          header: headerHtmlString,
        },
        { responseType: 'stream' },
      )

      response.status(200).type('application/pdf').header('Content-disposition', 'inline; filename="ticket.pdf"')

      pdfStream.data.pipe(response)
    } catch (e) {
      response.status(e.status || 500).send('Internal error')
      console.error(e)
    }
  })
