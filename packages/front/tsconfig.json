{"compilerOptions": {"target": "ES2015", "moduleResolution": "NodeNext", "allowJs": true, "checkJs": false, "noEmit": true, "strict": false, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "rootDir": "./", "outDir": "dist", "sourceMap": true, "module": "NodeNext", "jsx": "react", "lib": ["DOM", "ES6"], "resolveJsonModule": true, "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*", "eslint.config.js", "./tailwind.config.js"], "exclude": ["node_modules", "dist"], "types": ["react", "react-dom", "react-router-dom", "react-query", "jest"]}