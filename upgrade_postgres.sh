#!/bin/bash

set -e

docker rm $(docker ps -aq) -f

password=$(awk -F= '/^DB_PASSWORD/ {print $2}' /opt/app/envfile-back.env)

# https://github.com/pgautoupgrade/docker-pgautoupgrade
docker run --name pgauto -it \
        --mount type=bind,source=/opt/app/postgresql/data,target=/var/lib/postgresql/data \
        -e POSTGRES_PASSWORD=$password \
        -e PGAUTO_ONESHOT=yes \
        pgautoupgrade/pgautoupgrade:16-alpine



# Remover vestigios do container postgres referentes ao dpgautoupgrade
docker compose up -d --force-recreate --no-deps postgresql
sleep 5
docker rm $(docker ps -aq) -f

docker compose up -d --force-recreate
