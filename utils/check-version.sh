#!/usr/bin/env bash

set -e
PRE_RELEASE=$1

version_compare () {
    if [[ $1 == $2 ]]
    then
        echo ""
        return 0
    fi
    local IFS=.
    local i

    # remove the -xxx from MRs tags. This is necessary to be able to compare the string precisely
    # Before | After
    # 1.1.0-mr-100.0 | 1.1.0-mr
    local ver1=($(echo "$1" | cut -d- -f 1))
    local ver2=($(echo "$2" | cut -d- -f 1)) # sed -E "s/(-{1,}[0-9])\w+//")
    # fill empty fields in ver1 with zeros
    for ((i=${#ver1[@]}; i<${#ver2[@]}; i++))
    do
        ver1[i]=0
    done
    for ((i=0; i<${#ver1[@]}; i++))
    do
        if [[ -z ${ver2[i]} ]]
        then
            # fill empty fields in ver2 with zeros
            ver2[i]=0
        fi
        if ((10#${ver1[i]} > 10#${ver2[i]}))
        then
            echo "$1"
            return 0
        fi
        if ((10#${ver1[i]} < 10#${ver2[i]}))
        then
          echo "$2"
          return 0
        fi
    done
    return 0
}

is_prerelease_correct() {
    local ver1=($(echo "$1" | cut -d- -f 2,3))
    local ver2=($(echo "$2" | cut -d- -f 2,3))

    if [[ "$ver1" == "$ver2" ]]; then
        return 0
    fi
    return 1
}

PROJECT_VERSION=${2:-$(awk -F\" '/version/ { print $4 }' lerna.json | head -n 1)}

if [[ $PRE_RELEASE == "release" ]]; then
    VERSION=$(awk -F\" '/version/ { print $4 }' lerna.json | head -n 1 | cut -d- -f1 | cut -d. -f 1,2)
    ORIGIN_TAG_VERSION=${3:-$(git tag --sort=v:refname --list "v$VERSION*" | grep -Ev "\-(mr|rc|dev)" | tail -n 1 | sed "s/v//")}
elif [[ $PRE_RELEASE == "rc" ]]; then
    RC_BASE_VERSION=$(git branch --show-current | awk -F'release/' '{print $2}')
    if [[ -n "$RC_BASE_VERSION" ]]; then
        LERNA_BASE_VERSION=$(awk -F\" '/"version":/ {print $4}' lerna.json | awk -F'-' '{print $1}')
        echo "Release base version: $RC_BASE_VERSION"
        if [[ "$LERNA_BASE_VERSION" != "$RC_BASE_VERSION" ]]; then
            LAST_RC_VERSION=$(git tag --sort=v:refname --list "v$RC_BASE_VERSION*" | grep -E "\-rc" | tail -n 1 | sed "s/v//")
            LAST_RC_VERSION=${LAST_RC_VERSION:-$RC_BASE_VERSION-rc.0}
            echo "Change lerna.json version to ${LAST_RC_VERSION}"
            sed -i "s/\"version\": *\"[^\"]*\"/\"version\": \"$LAST_RC_VERSION\"/" lerna.json ./packages/*/package.json
            git commit -am "fix(version): versao alterada para ${LAST_RC_VERSION}"
            exit 0
        fi
    fi
    VERSION=$(awk -F\" '/version/ { print $4 }' lerna.json | head -n 1 | cut -d- -f1 | cut -d. -f 1,2,3)
    ORIGIN_TAG_VERSION=${3:-$(git tag --sort=v:refname --list "v$VERSION*" | grep -E "\-rc" | tail -n 1 | sed "s/v//")}
else
    ORIGIN_TAG_VERSION=${3:-$(git tag --sort=v:refname --list "v*$PRE_RELEASE\.*" | tail -n 1 | sed "s/v//")}
fi

echo "Current version: ${PROJECT_VERSION}
Origin tag version: ${ORIGIN_TAG_VERSION}
"

if [[ -z "$ORIGIN_TAG_VERSION" ]]; then
    echo "$PRE_RELEASE is a new tag"
    exit 0
fi

CORRECT_TAG=$(version_compare "$PROJECT_VERSION" "$ORIGIN_TAG_VERSION")

if [[ -z "$CORRECT_TAG" ]] && ! is_prerelease_correct "$PROJECT_VERSION" "$ORIGIN_TAG_VERSION" ; then
    CORRECT_TAG=${ORIGIN_TAG_VERSION}
    echo "=== :warning: Ching chong your version is wrong :warning: ===
    Current: ${PROJECT_VERSION} | Correct: ${CORRECT_TAG}
    "
fi

if [[ -n "$CORRECT_TAG" && $CORRECT_TAG != $PROJECT_VERSION ]]; then
  git checkout "v$CORRECT_TAG" -- lerna.json
  git commit -am "fix (version): versao alterada para ${CORRECT_TAG}"
else
    echo ":ok_hand: perfect!"
fi

exit 0
