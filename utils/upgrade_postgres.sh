#!/bin/bash
set -e

POSTGRES_DATA=$(echo $(
        ([ -d "/opt/app/postgresql/data" ] && echo "/opt/app/postgresql/data") ||
                ([ -d "/var/lib/postgresql/data" ] && echo "/var/lib/postgresql/data") ||
                ([ -d "~/postgresql/data" ] && echo "~/postgresql/data") ||
                ([ -d "/home/<USER>/data" ] && echo "/home/<USER>/data")
))
echo "=> Postgres data dir: ${POSTGRES_DATA}"
ENVFILE=$(echo $(
        ([ -f "/opt/app/envfile-back.env" ] && echo "/opt/app/envfile-back.env)") ||
                ([ -f "../packages/back/.env" ] && echo "../packages/back/.env") ||
                ([ -f "./packages/back/.env" ] && echo "./packages/back/.env") ||
                ([ -f ".env" ] && echo ".env")
))

echo "=> Envfile: ${ENVFILE}"
DB_PASSWORD=$(awk -F= '/^DB_PASSWORD/ {print $2}' $ENVFILE || echo '')

echo "=> Stop all containers"
docker stop $(docker ps -aq) -f 2>/dev/null || true

echo "=> Removing postgresql containers"
docker rm $(docker ps -f name=postgresql2 -aq) -f 2>/dev/null || true

echo "=> Removing pgauto containers previous execution..."
docker rm $(docker ps -f name=pgauto -aq) -f 2>/dev/null || true

echo "=> Running pgauto upgrade..."
# https://github.com/pgautoupgrade/docker-pgautoupgrade
docker run --name pgauto -it \
        --mount type=bind,source=$POSTGRES_DATA,target=/var/lib/postgresql/data \
        -e POSTGRES_PASSWORD=$DB_PASSWORD \
        -e PGAUTO_ONESHOT=yes \
        pgautoupgrade/pgautoupgrade:16-alpine

echo "=> Cleaning postgresql container"
# Remover vestigios do container postgres referentes ao dpgautoupgrade
docker compose up -d --force-recreate --no-deps postgresql
sleep 5

echo "=> Getting up containers"
docker compose up -d
