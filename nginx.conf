worker_processes auto;

events {
    worker_connections 1024;
}

http {
    client_max_body_size 100M;

    # APP_API
    upstream app_api {
        least_conn;
        server digisac3-app-api-1:8080;
    }

    # APP_WORKERS
    upstream app_workers {
        least_conn;
        server digisac3-app-workers-1:8000;
    }

    # APP_WORKERS_GO
    upstream app_workers_go {
        least_conn;
        server digisac3-app-workers-go-1:8000;
    }

    # Bloco server principal para APP_API
    server {
        listen 8080;

        location / {
            proxy_pass http://app_api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Bloco server principal para APP_WORKERS
    server {
        listen 8000;

        location / {
            proxy_pass http://app_workers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

   # Bloco server principal para APP_WORKERS_GO
    server {
        listen 8400;

        location / {
            proxy_pass http://app_workers_go;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Bloco server consolidado para APP_WORKERS, APP_WORKERS_GO
    server {
        listen 80;

        # Rota para APP_WORKERS
        location /workers/ {
            proxy_pass http://app_workers;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Rota para APP_WORKERS_GO
        location /workers_go/ {
            proxy_pass http://app_workers_go;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
